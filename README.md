# 🚀 FarTech AI Eclipse Plugin v4.0

An AI-powered Eclipse plugin with **Intelligent Automatic CRUD Operations** that integrates with FarTech AI to provide seamless code assistance, analysis, and file management directly within your Eclipse IDE.

## 🤖 **NEW: Automatic CRUD Operations**

**Just chat naturally - FarTech AI automatically detects and performs file operations!**

- **No special commands needed** - just describe what you want
- **Intelligent detection** - AI understands your intent from conversation
- **Automatic file creation** - code blocks become files automatically
- **Smart file reading** - mention files and AI reads them for you
- **Context-aware modifications** - AI updates existing files intelligently
- **Safe operations** - always asks for confirmation with automatic backups

## ✨ Features

### 🤖 AI-Powered Code Assistance
- **Code Analysis**: Get intelligent insights about your code structure and quality
- **Code Explanation**: Understand complex code segments with AI explanations
- **Code Generation**: Generate code snippets and boilerplate code
- **Refactoring Suggestions**: Get AI-powered refactoring recommendations
- **Documentation Generation**: Automatically generate documentation for your code
- **Test Generation**: Create unit tests with AI assistance

### 🤖 **Intelligent Automatic CRUD Operations**
- **🎯 Natural Language**: Just describe what you want - no special syntax needed
- **🧠 Intent Detection**: AI automatically detects file operations from conversation
- **📝 Auto-Create**: Code blocks in AI responses become files automatically
- **👁️ Auto-Read**: Mention files and AI reads them for you
- **✏️ Auto-Modify**: Request changes and AI updates files intelligently
- **📁 Auto-Structure**: Ask for project organization and AI creates directories
- **🛡️ Safe Operations**: Always asks for confirmation with automatic backups
- **🔄 Context-Aware**: Remembers your project structure and coding patterns

### 📋 **Manual CRUD Operations** (Legacy Support)
- **Create Files**: AI can create new files with specified content
- **Read Files**: AI can read and analyze existing files in your workspace
- **Update Files**: AI can modify existing files with new content
- **Delete Files**: AI can safely delete files with user confirmation
- **Copy Files**: AI can duplicate files to new locations
- **Move Files**: AI can relocate files within the workspace
- **Rename Files**: AI can rename files and change extensions
- **Create Directories**: AI can create new folder structures
- **Directory Operations**: List directory contents and navigate workspace structure
- **Command Execution**: Execute PowerShell and CMD commands with AI assistance

## 📋 **CRUD Operations Guide**

FarTech AI includes powerful file operations that allow the AI to interact with your workspace files safely and efficiently. All destructive operations require user confirmation for security.

### 🔍 **READ Operations**

**Read File Content:**
```
User: "Show me the contents of src/Main.java"
AI Response: [READ_FILE:src/Main.java]
```

**List Directory Contents:**
```
User: "What files are in the src directory?"
AI Response: [list_files:src]
```

### ✏️ **CREATE/UPDATE Operations**

**Create New File:**
```
User: "Create a new README file with project description"
AI Response: [write_file:README.md]
# Project Title
This is a sample project description.
[end_file]
```

**Update Existing File:**
```
User: "Add a new method to the Utils class"
AI Response: [write_file:src/Utils.java]
public class Utils {
    // existing code...

    public static String newMethod() {
        return "Hello World";
    }
}
[end_file]
```

### 🗑️ **DELETE Operations**

**Delete File:**
```
User: "Remove the old config file"
AI Response: [delete_file:old-config.xml]
```

### 💻 **Command Execution**

**PowerShell Commands:**
```
User: "Run a Maven build"
AI Response: [exec_powershell:mvn clean install]
```

**CMD Commands:**
```
User: "Check Java version"
AI Response: [exec_cmd:java -version]
```

**PowerShell with Working Directory:**
```
User: "Run tests in the test directory"
AI Response: [exec_powershell_dir:./test:npm test]
```

### 🎯 **Automatic CRUD Examples**

**🤖 Natural Language → Automatic Actions:**

```
👤 User: "Create a Java service class for user management"

🤖 FarTech AI: "I'll create a UserService class for you..."

[Code response with class definition]

🤖 Auto-Operations Detected:
📝 Auto-Create File (80% confidence)
   Path: src/main/java/UserService.java
   Reason: Auto-detected code block for file creation
   Action: [write_file:src/main/java/UserService.java][code][end_file]

🔐 CONFIRMATION REQUIRED
AI wants to CREATE file: src/main/java/UserService.java
Allow this operation? [Yes] [No]
```

```
👤 User: "Show me the main configuration file"

🤖 FarTech AI: Auto-Operations Detected:
👁️ Auto-Read File (70% confidence)
   Path: config/application.properties
   Action: [READ_FILE:config/application.properties]

[Displays file content automatically]
```

### 🛡️ **Safety Features**

- **🔐 User Confirmation**: All operations require explicit user approval
- **💾 Automatic Backups**: Files backed up before modifications
- **📊 Confidence Levels**: Operations show confidence percentages
- **🛡️ Safe Cancellation**: Users can cancel any detected operation
- **📝 File Size Limits**: Read operations limited to 100KB files for performance
- **⚠️ Error Handling**: Comprehensive error messages for failed operations
- **🔄 Workspace Integration**: Automatic workspace re-indexing after file changes

### 💬 Interactive Chat Interface
- **WhatsApp-style Chat**: Clean, intuitive chat interface with user messages on the right and AI responses on the left
- **File Attachments**: Attach files and images to your conversations
- **Context-Aware**: AI understands your project structure and workspace context
- **Chat History**: Persistent conversation history across sessions

### 🔧 Development Tools Integration
- **Workspace Indexing**: Automatically index and analyze your workspace files
- **File Operations**: AI can read, edit, and delete files with your permission
- **Command Execution**: Execute cmd or PowerShell commands through the AI
- **Context Menu Integration**: Right-click on files/code for quick AI actions

### 🔗 CI/CD and Quality Integration
- **Jenkins Integration**: Connect with Jenkins for build status and CI/CD workflows
- **SonarQube Integration**: Integrate with SonarQube for code quality analysis
- **Bitbucket Integration**: Connect with Bitbucket repositories for version control insights

### 🔒 Bulletproof Security
- **Hardcoded FarTech AI Endpoint**: Always uses `https://ai.ad-ins.com/api`
- **4-Layer Protection System**: Prevents any unauthorized endpoint changes
- **No Base URL Configuration**: Settings dialog has NO Base URL field
- **BaseUrlInterceptor**: Catches and blocks any URL modification attempts

## 📦 Installation

### Prerequisites
- **Eclipse IDE**: Eclipse 2019-12 or later
- **Java**: Java 8 or higher
- **FarTech AI API Key**: Get your API key from FarTech AI

### Quick Installation

1. **Download the Plugin**
   ```
   Download: fartech-ai-plugin-2.1.0-nocache.jar
   ```

2. **Install to Eclipse**
   ```
   Copy to: [Your Eclipse Path]/dropins/
   ```

3. **Restart Eclipse with Clean Flag**
   ```
   eclipse.exe -clean
   ```

4. **Open FarTech AI Assistant**
   ```
   Window → Show View → Other → FarTech AI → FarTech AI Assistant
   ```

## ⚙️ Configuration

### Setting up FarTech AI

1. **Open Settings**: Click the "Settings" button in the FarTech AI Assistant view

2. **Configure API Key**: Enter your FarTech AI API key (endpoint is pre-configured)

3. **Select Model**: Click "Load Models" and select your preferred model

4. **Test Connection**: Click "Test Connection" to verify configuration

### What You'll See in Settings
- **Title**: "FarTech AI Assistant Settings - NO BASE URL"
- **Provider**: FarTech AI (OpenAI Compatible)
- **Endpoint**: https://ai.ad-ins.com/api (read-only)
- **Fields**: Only API Key and Model selection
- **NO Base URL field anywhere**

### 🔗 DevOps Integration Setup

#### 🏗️ Jenkins Configuration

1. **Open Integration Settings**
   - `Window` → `Preferences` → `FarTech AI` → `Integrations`
   - Click **Jenkins** tab

2. **Required Settings:**
   - **Server URL**: `https://jenkins.company.com`
   - **Username**: Your Jenkins username
   - **API Token**: Generate from Jenkins user settings
   - **Job Name**: Pipeline or job to monitor

3. **Optional Settings:**
   - **Timeout**: Request timeout (default: 30s)
   - **Poll Interval**: Update frequency (default: 5 min)
   - **Notifications**: Enable build status alerts

4. **Test Connection** and **Save**

#### 🛡️ SonarQube Configuration

1. **Open SonarQube Tab** in Integration Settings

2. **Required Settings:**
   - **Server URL**: `https://sonarcloud.io` or your SonarQube server
   - **Token**: Generate from SonarQube account settings
   - **Project Key**: Your project identifier

3. **Optional Settings:**
   - **Organization**: For SonarCloud (if applicable)
   - **Quality Gate**: Custom quality gate name
   - **Detailed Metrics**: Include comprehensive metrics

4. **Test Connection** and **Save**

#### 📂 Bitbucket Configuration

1. **Open Bitbucket Tab** in Integration Settings

2. **Required Settings:**
   - **Server URL**: `https://bitbucket.org` or your Bitbucket server
   - **Username**: Your Bitbucket username
   - **App Password**: Generate from Bitbucket account settings
   - **Workspace**: Your workspace name
   - **Repository**: Repository name

3. **Optional Settings:**
   - **Default Branch**: Branch to monitor (default: main)
   - **Pull Requests**: Monitor PR status
   - **Issues**: Include issue tracking

4. **Test Connection** and **Save**

## 💬 Using the AI Assistant

### 🎨 Chat Interface

The FarTech AI assistant features a **WhatsApp-style chat interface**:

- **User messages** appear on the **right** (blue bubbles)
- **AI responses** appear on the **left** (gray bubbles)
- **File attachments** supported (drag & drop)
- **Image sharing** for screenshots and diagrams
- **Command execution** with AI assistance

### 🚀 Quick Actions

**Right-click any file for instant AI assistance:**

| Action | Description |
|--------|-------------|
| **Analyze Code** | Comprehensive code review with context |
| **Explain Function** | Detailed explanation of selected code |
| **Generate Tests** | Create unit tests with coverage analysis |
| **Optimize Performance** | Performance improvement suggestions |
| **Security Review** | Security vulnerability assessment |
| **Deployment Check** | Production readiness evaluation |

### 💡 Advanced Features

#### 📎 File Attachments
- **Drag & drop** files into chat
- **Share code snippets** for analysis
- **Upload screenshots** for visual context
- **Attach logs** for debugging assistance

#### ⚡ Command Execution
```
User: "Run the tests and show me the results"

FarTech AI: "I'll run your test suite for you:

> mvn test

[AI executes command and analyzes results]

Test Results:
✅ 45 tests passed
❌ 3 tests failed
⚠️ 2 tests skipped

Failed Tests Analysis:
1. PaymentServiceTest.testInvalidCard() - NullPointerException
2. UserServiceTest.testEmailValidation() - AssertionError
3. AuthTest.testTokenExpiry() - Timeout

Based on your recent commits and SonarQube analysis, these failures
are likely related to the authentication refactoring in commit abc123f.

Recommendations:
1. Fix null handling in PaymentService.validateCard()
2. Update email regex pattern in UserService
3. Increase timeout for AuthTest (Jenkins shows slow DB responses)

Would you like me to help fix these issues?"
```

## 🏗️ Architecture

### 📊 System Overview

```mermaid
graph TB
    A[Eclipse IDE] --> B[FarTech AI Plugin]
    B --> C[AI Provider Manager]
    B --> D[Integration Manager]
    B --> E[Chat Interface]

    C --> F[OpenAI API]
    C --> G[Azure OpenAI]
    C --> H[Local LLM]

    D --> I[Jenkins Integration]
    D --> J[SonarQube Integration]
    D --> K[Bitbucket Integration]
    D --> L[Git Repository Manager]

    E --> M[WhatsApp-Style UI]
    E --> N[File Attachments]
    E --> O[Command Execution]

    I --> P[Build Status]
    J --> Q[Code Quality]
    K --> R[Repository Data]
    L --> S[Git History]

    P --> T[Enhanced AI Context]
    Q --> T
    R --> T
    S --> T

    T --> U[Context-Aware Responses]
```

### 🔧 Core Components

#### **🧠 AI Provider Manager**
- **Multi-provider support** for OpenAI-compatible APIs
- **Dynamic model loading** and validation
- **Secure credential management**
- **Request/response handling** with retry logic

#### **🔗 Integration Manager**
- **Parameterized configuration** system
- **Real-time connection testing**
- **Status monitoring** and health checks
- **Extensible architecture** for new integrations

#### **💬 Enhanced AI Context Manager**
- **Multi-source data aggregation**
- **Context-aware prompt generation**
- **Intelligent response enhancement**
- **Integration status tracking**

#### **🎨 User Interface**
- **WhatsApp-style chat** interface
- **Dynamic configuration panels**
- **File attachment support**
- **Command execution interface**

### 📁 Project Structure

```
eclipse-chatgpt-plugin/
├── src/com/example/fartechplugin/
│   ├── core/
│   │   ├── AIProviderManager.java          # AI provider management
│   │   ├── PreferencesManager.java         # Settings persistence
│   │   └── EnhancedAIContextManager.java   # Context aggregation
│   ├── integrations/
│   │   ├── JenkinsIntegration.java         # Jenkins CI/CD
│   │   ├── SonarQubeIntegration.java       # Code quality
│   │   ├── BitbucketIntegration.java       # Repository
│   │   └── GitRepositoryManager.java      # Git operations
│   ├── config/
│   │   ├── IntegrationConfigManager.java   # Configuration hub
│   │   ├── IntegrationConfig.java          # Config model
│   │   └── IntegrationConfigPanel.java     # Dynamic UI
│   ├── ui/
│   │   ├── ChatGPTView.java               # Main chat interface
│   │   ├── IntegrationsConfigDialog.java   # Settings dialog
│   │   └── FileAttachmentHandler.java      # File operations
│   └── utils/
│       ├── CommandExecutor.java            # Command execution
│       ├── FileIndexer.java               # Directory indexing
│       └── SecurityUtils.java             # Encryption utilities
├── plugin.xml                             # Eclipse plugin manifest
├── META-INF/MANIFEST.MF                   # Plugin metadata
├── README.md                              # This documentation
├── CHANGELOG.md                           # Version history
└── docs/                                  # Additional documentation
    ├── API.md                             # API reference
    ├── CONFIGURATION.md                   # Detailed setup guide
    └── TROUBLESHOOTING.md                 # Common issues
```

## 🛡️ Security & Privacy

- **Secure Storage**: API keys encrypted using Eclipse secure storage
- **Local Processing**: File operations happen locally in your workspace
- **User Confirmation**: Always asks before modifying or deleting files
- **No Data Collection**: Plugin doesn't collect or store your conversations
- **Encrypted Credentials**: All API keys and passwords securely encrypted
- **Connection Validation**: Real-time testing of all integrations

## � API Documentation

### 🔌 Plugin Extension Points

FarTech AI provides several extension points for customization:

#### **AI Provider Extension**
```java
public interface AIProvider {
    String getName();
    boolean isConfigured();
    String sendRequest(String prompt, Map<String, Object> context);
    List<String> getAvailableModels();
    boolean validateConnection();
}
```

#### **Integration Extension**
```java
public interface DevOpsIntegration {
    String getIntegrationType();
    boolean isConfigured();
    String generateContext();
    ConnectionTestResult testConnection();
    void configure(Map<String, String> parameters);
}
```

### 🎯 Usage Examples

#### **Programmatic AI Interaction**
```java
// Get AI provider manager
AIProviderManager aiManager = new AIProviderManager();

// Send enhanced prompt with full context
String response = aiManager.sendEnhancedPrompt(
    "Review this code for security issues",
    "/src/main/java/PaymentController.java",
    "security"
);

// Check integration status
EnhancedAIContextManager.IntegrationStatus status =
    aiManager.getIntegrationStatus();
System.out.println(status.getSummary());
```

#### **Configuration Management**
```java
// Create configuration manager
IntegrationConfigManager configManager = new IntegrationConfigManager();

// Configure Jenkins programmatically
IntegrationConfig jenkinsConfig =
    configManager.getConfig(IntegrationConfigManager.JENKINS_CONFIG);
jenkinsConfig.setParameterValue("url", "https://jenkins.company.com");
jenkinsConfig.setParameterValue("username", "john.doe");
jenkinsConfig.setParameterValue("apiToken", "your-token");

// Save and test
configManager.saveConfig(IntegrationConfigManager.JENKINS_CONFIG, jenkinsConfig);
ConnectionTestResult result = configManager.testConnection("jenkins");
```

## 🤝 Contributing

We welcome contributions to FarTech AI! Here's how to get started:

### 🚀 Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/fartech/eclipse-ai-plugin.git
   cd eclipse-ai-plugin
   ```

2. **Import into Eclipse**
   - Open Eclipse IDE
   - `File` → `Import` → `Existing Projects into Workspace`
   - Select the cloned directory

3. **Set up development environment**
   - Install Eclipse PDE (Plugin Development Environment)
   - Configure target platform for Eclipse 2019-12+

### 📝 Contribution Guidelines

- **Code Style**: Follow Java conventions and existing patterns
- **Testing**: Add unit tests for new features
- **Documentation**: Update README and API docs
- **Commit Messages**: Use conventional commit format

### 🐛 Bug Reports

Please use GitHub Issues with:
- **Clear description** of the problem
- **Steps to reproduce**
- **Expected vs actual behavior**
- **Environment details** (Eclipse version, Java version, OS)

### 💡 Feature Requests

We're always looking for new ideas! Submit feature requests with:
- **Use case description**
- **Proposed solution**
- **Alternative approaches considered**

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 🌟 Support

- **📧 Email**: <EMAIL>
- **💬 Discord**: [FarTech AI Community](https://discord.gg/fartech-ai)
- **📖 Documentation**: [docs.fartech.ai](https://docs.fartech.ai)
- **🐛 Issues**: [GitHub Issues](https://github.com/fartech/eclipse-ai-plugin/issues)

---

**Made with ❤️ by the FarTech AI Team**

*Revolutionizing software development with AI-powered intelligence and comprehensive DevOps integration.*
