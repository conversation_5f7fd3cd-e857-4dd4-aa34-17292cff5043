@echo off
echo 🔧 Building Plugin with Correct JAR Process...
echo ==============================================
echo.

REM Create directories
if not exist bin mkdir bin
if not exist dist mkdir dist

REM Clean previous build
echo 🧹 Cleaning previous build...
if exist dist\fartech-ai-plugin-2.1.0-nocache.jar del dist\fartech-ai-plugin-2.1.0-nocache.jar

REM Check if compiled classes exist
echo 🔍 Checking compiled classes...
if not exist bin\com\example\fartechplugin\FarTechPlugin.class (
    echo ❌ FarTechPlugin.class not found in bin directory!
    pause
    exit /b 1
)

echo ✅ Found compiled classes in bin directory

REM Verify our MANIFEST.MF has the correct content
echo 🔍 Verifying source MANIFEST.MF...
if exist META-INF\MANIFEST.MF (
    echo ✅ MANIFEST.MF found
    echo.
    echo 📄 Current MANIFEST.MF content:
    type META-INF\MANIFEST.MF
    
    echo.
    echo 🔍 Checking for OSGi headers:
    findstr "Bundle-SymbolicName" META-INF\MANIFEST.MF >nul && echo ✅ Bundle-SymbolicName present || echo ❌ Bundle-SymbolicName missing
    findstr "Bundle-Activator" META-INF\MANIFEST.MF >nul && echo ✅ Bundle-Activator present || echo ❌ Bundle-Activator missing
    findstr "Require-Bundle" META-INF\MANIFEST.MF >nul && echo ✅ Require-Bundle present || echo ❌ Require-Bundle missing
) else (
    echo ❌ MANIFEST.MF not found!
    pause
    exit /b 1
)

REM Create JAR using the -m flag to use our manifest
echo.
echo 📦 Creating JAR with our MANIFEST.MF (using -m flag)...

REM First create JAR with our manifest
jar -cmf META-INF\MANIFEST.MF dist\fartech-ai-plugin-2.1.0-nocache.jar plugin.xml build.properties

REM Add all compiled classes
echo 📝 Adding compiled classes...
jar -uf dist\fartech-ai-plugin-2.1.0-nocache.jar -C bin .

REM Add icons if they exist
if exist icons (
    echo 📝 Adding icons...
    jar -uf dist\fartech-ai-plugin-2.1.0-nocache.jar -C . icons
)

REM Verify the JAR was created and has correct MANIFEST
if exist dist\fartech-ai-plugin-2.1.0-nocache.jar (
    echo ✅ Plugin JAR created successfully
    
    echo.
    echo 🔍 Verifying MANIFEST.MF in JAR...
    jar -xf dist\fartech-ai-plugin-2.1.0-nocache.jar META-INF\MANIFEST.MF
    
    if exist META-INF\MANIFEST.MF (
        echo ✅ MANIFEST.MF extracted for verification
        echo.
        echo 📄 MANIFEST.MF contents in JAR:
        type META-INF\MANIFEST.MF
        
        echo.
        echo 🔍 Checking critical entries:
        findstr "Bundle-SymbolicName" META-INF\MANIFEST.MF >nul && echo ✅ Bundle-SymbolicName present || echo ❌ Bundle-SymbolicName missing
        findstr "Bundle-Activator" META-INF\MANIFEST.MF >nul && echo ✅ Bundle-Activator present || echo ❌ Bundle-Activator missing
        findstr "Require-Bundle" META-INF\MANIFEST.MF >nul && echo ✅ Require-Bundle present || echo ❌ Require-Bundle missing
        findstr "com.example.fartechplugin" META-INF\MANIFEST.MF >nul && echo ✅ Plugin package reference present || echo ❌ Plugin package reference missing
        
        REM Clean up
        rmdir /S /Q META-INF
    ) else (
        echo ❌ Failed to extract MANIFEST.MF from JAR
    )
    
    echo.
    echo 📊 JAR Statistics:
    for /f %%i in ('jar -tf dist\fartech-ai-plugin-2.1.0-nocache.jar ^| find ".class" ^| find /c /v ""') do echo - Class files: %%i
    for /f %%i in ('jar -tf dist\fartech-ai-plugin-2.1.0-nocache.jar ^| find /c /v ""') do echo - Total files: %%i
    
    echo.
    echo 🔍 Verifying critical files in JAR:
    jar -tf dist\fartech-ai-plugin-2.1.0-nocache.jar | findstr "META-INF/MANIFEST.MF" >nul && echo ✅ MANIFEST.MF in JAR || echo ❌ MANIFEST.MF missing from JAR
    jar -tf dist\fartech-ai-plugin-2.1.0-nocache.jar | findstr "plugin.xml" >nul && echo ✅ plugin.xml in JAR || echo ❌ plugin.xml missing from JAR
    jar -tf dist\fartech-ai-plugin-2.1.0-nocache.jar | findstr "FarTechPlugin.class" >nul && echo ✅ FarTechPlugin.class in JAR || echo ❌ FarTechPlugin.class missing from JAR
    jar -tf dist\fartech-ai-plugin-2.1.0-nocache.jar | findstr "FarTechView.class" >nul && echo ✅ FarTechView.class in JAR || echo ❌ FarTechView.class missing from JAR
    
    echo.
    echo ✅ Plugin built with correct MANIFEST.MF: dist\fartech-ai-plugin-2.1.0-nocache.jar
    echo.
    echo 🚀 Installation:
    echo 1. Close Eclipse completely
    echo 2. Remove old plugin: del "C:\Dev\APP\eclipse-jee-2019-12\dropins\*fartech*"
    echo 3. Copy new plugin: copy "dist\fartech-ai-plugin-2.1.0-nocache.jar" "C:\Dev\APP\eclipse-jee-2019-12\dropins\"
    echo 4. Clear OSGi cache: rmdir /S /Q "C:\Dev\APP\eclipse-jee-2019-12\configuration\org.eclipse.osgi"
    echo 5. Start Eclipse: "C:\Dev\APP\eclipse-jee-2019-12\eclipse.exe" -clean
    echo.
    echo 🎯 Expected Result:
    echo - Plugin should now load properly with correct OSGi headers
    echo - FarTech AI should appear in Window → Show View → Other → FarTech AI
    echo - FarTech AI menu should appear in menu bar
    echo - No more "Bundle-SymbolicName missing" errors
    
) else (
    echo ❌ JAR creation failed!
    exit /b 1
)

echo.
pause
