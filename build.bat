@echo off
echo Building FarTech AI Eclipse Plugin v2.1.0-NOCACHE...

REM Create dist directory if it doesn't exist
if not exist dist mkdir dist

REM Clean previous build
if exist dist\fartech-ai-plugin-2.1.0-nocache.jar del dist\fartech-ai-plugin-2.1.0-nocache.jar

REM Build the plugin JAR
echo Creating plugin JAR...
jar -cfm dist\fartech-ai-plugin-2.1.0-nocache.jar META-INF\MANIFEST.MF plugin.xml build.properties -C bin .

REM Verify build
if exist dist\fartech-ai-plugin-2.1.0-nocache.jar (
    echo ✅ Plugin built successfully: dist\fartech-ai-plugin-2.1.0-nocache.jar
    echo.
    echo 🚀 Installation:
    echo 1. Copy dist\fartech-ai-plugin-2.1.0-nocache.jar to Eclipse dropins folder
    echo 2. Restart Eclipse with -clean flag
    echo 3. Go to Window → Show View → Other → FarTech AI → FarTech AI Assistant
) else (
    echo ❌ Build failed!
    exit /b 1
)

pause
