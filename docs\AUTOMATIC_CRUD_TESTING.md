# 🧪 Automatic CRUD Testing Guide

This guide provides test scenarios to verify that FarTech AI's automatic CRUD operations work correctly.

## 🎯 Test Setup

1. **Enable Agent Mode** for full automatic capabilities
2. **Index your workspace** for better context awareness
3. **Use a test project** to avoid affecting important files
4. **Have your API key configured** and tested

## 📋 Test Scenarios

### ✅ **Automatic File Creation Tests**

#### Test 1: Java Class Creation
**Input:** "Create a Java service class for user management"

**Expected Behavior:**
- AI provides Java class code in response
- Auto-detection message appears with ~80% confidence
- File path suggested: `src/main/java/UserService.java`
- Confirmation dialog appears
- File created when approved

#### Test 2: Configuration File Creation
**Input:** "Create a properties file for database configuration"

**Expected Behavior:**
- AI provides properties content
- Auto-detection suggests appropriate file path
- File created with proper extension

#### Test 3: Multiple File Creation
**Input:** "Create a complete REST API with controller, service, and repository"

**Expected Behavior:**
- Multiple auto-operations detected
- Each file creation requires separate confirmation
- Proper file paths suggested for each component

### ✅ **Automatic File Reading Tests**

#### Test 4: Explicit File Reading
**Input:** "Show me the UserService.java file"

**Expected Behavior:**
- Auto-detection recognizes file read intent
- File content displayed automatically
- No manual command needed

#### Test 5: Configuration File Reading
**Input:** "What's in the application.properties file?"

**Expected Behavior:**
- AI automatically reads the configuration file
- Content displayed with explanation

#### Test 6: Non-existent File Reading
**Input:** "Show me the NonExistent.java file"

**Expected Behavior:**
- Auto-detection attempts to read file
- Appropriate error message when file not found

### ✅ **Automatic File Modification Tests**

#### Test 7: Adding Methods
**Input:** "Add a login method to my UserService class"

**Expected Behavior:**
- AI reads existing UserService.java
- Provides updated code with new method
- Auto-detection suggests file modification
- Backup created before modification

#### Test 8: Refactoring Code
**Input:** "Refactor my UserController to use dependency injection"

**Expected Behavior:**
- Existing file read and analyzed
- Updated code provided
- Modification detected and confirmed

### ✅ **Automatic Directory Creation Tests**

#### Test 9: Project Structure
**Input:** "Set up a standard Maven project structure"

**Expected Behavior:**
- Multiple directory creation operations detected
- Standard Maven directories suggested
- Each directory creation confirmed

#### Test 10: Package Creation
**Input:** "Create a package structure for com.example.services"

**Expected Behavior:**
- Directory path auto-generated
- Proper package structure created

### ✅ **Context Awareness Tests**

#### Test 11: Project Context
**Input:** "Create a test class for my UserService"

**Expected Behavior:**
- AI understands existing UserService
- Test class created in appropriate test directory
- Proper naming conventions followed

#### Test 12: File Relationships
**Input:** "Create a controller that uses my UserService"

**Expected Behavior:**
- AI references existing UserService
- Proper imports and dependencies included
- Controller created with appropriate methods

### ✅ **Safety Feature Tests**

#### Test 13: Backup Creation
**Input:** "Modify my existing UserService to add error handling"

**Expected Behavior:**
- Backup created before modification
- Backup stored in `.fartech-backups` directory
- Timestamp included in backup filename

#### Test 14: Confirmation Cancellation
**Input:** "Create a new file called test.txt"

**Expected Behavior:**
- Auto-detection works correctly
- User can cancel operation
- No file created when cancelled

#### Test 15: Confidence Levels
**Input:** Various requests with different clarity levels

**Expected Behavior:**
- Clear requests show high confidence (80%+)
- Ambiguous requests show lower confidence
- Very unclear requests may not trigger auto-detection

### ✅ **Error Handling Tests**

#### Test 16: Invalid File Paths
**Input:** "Create a file in /invalid/path/test.txt"

**Expected Behavior:**
- Auto-detection suggests operation
- Appropriate error when path is invalid
- User informed of the issue

#### Test 17: Permission Issues
**Input:** "Modify a read-only file"

**Expected Behavior:**
- Auto-detection works
- Permission error handled gracefully
- User informed of permission issue

#### Test 18: Large File Handling
**Input:** "Show me a very large file"

**Expected Behavior:**
- File size limit respected
- Appropriate message about size limit
- Operation handled safely

## 🔧 Manual Testing Commands

### Test Natural Language Variations

Try these different ways of expressing the same intent:

**File Creation:**
- "Create a Java class for user management"
- "Generate a UserService class"
- "Make a new service for handling users"
- "Write a class to manage user operations"

**File Reading:**
- "Show me the UserService file"
- "Display the contents of UserService.java"
- "What's in the UserService class?"
- "Read the UserService file for me"

**File Modification:**
- "Add logging to UserService"
- "Update UserService with error handling"
- "Modify UserService to include validation"
- "Change UserService to use dependency injection"

## 📊 Test Results Template

```
Test Date: ___________
Plugin Version: ___________
Agent Mode: Enabled/Disabled

Automatic File Creation: ✅ / ❌
- Java class creation: ✅ / ❌
- Configuration files: ✅ / ❌
- Multiple files: ✅ / ❌

Automatic File Reading: ✅ / ❌
- Explicit reading: ✅ / ❌
- Configuration reading: ✅ / ❌
- Error handling: ✅ / ❌

Automatic File Modification: ✅ / ❌
- Method addition: ✅ / ❌
- Code refactoring: ✅ / ❌
- Backup creation: ✅ / ❌

Automatic Directory Creation: ✅ / ❌
- Project structure: ✅ / ❌
- Package creation: ✅ / ❌

Context Awareness: ✅ / ❌
- Project understanding: ✅ / ❌
- File relationships: ✅ / ❌

Safety Features: ✅ / ❌
- User confirmation: ✅ / ❌
- Backup creation: ✅ / ❌
- Cancellation: ✅ / ❌

Error Handling: ✅ / ❌
- Invalid paths: ✅ / ❌
- Permission issues: ✅ / ❌
- Large files: ✅ / ❌

Issues Found:
- 
- 
- 

Overall Rating: ⭐⭐⭐⭐⭐ (1-5 stars)

Notes:
- 
- 
- 
```

## 🚨 Common Issues & Solutions

### **Auto-Detection Not Working**
- Ensure Agent Mode is enabled
- Check if workspace is indexed
- Try more explicit language
- Verify API connection

### **Wrong File Paths**
- Provide more context about project structure
- Mention specific directories
- Reference existing files for context

### **Low Confidence Operations**
- Be more specific in requests
- Use clear action words (create, modify, show)
- Provide file type information

### **Operations Not Executing**
- Check confirmation dialogs
- Verify file permissions
- Ensure workspace is writable

## 🎉 Success Criteria

The automatic CRUD system is working correctly when:

- ✅ Natural language requests trigger appropriate auto-detection
- ✅ Confidence levels are reasonable for request clarity
- ✅ File paths are suggested intelligently
- ✅ All operations require user confirmation
- ✅ Backups are created before modifications
- ✅ Error handling is graceful and informative
- ✅ Context awareness improves suggestions
- ✅ Users can cancel any operation safely

---

**Test thoroughly to ensure a smooth user experience with automatic CRUD operations!**
