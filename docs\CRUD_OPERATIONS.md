# 📁 FarTech AI CRUD File Operations Guide

This comprehensive guide explains how to use FarTech AI's powerful file operations to manage your workspace files through natural language conversations.

## 🎯 Overview

FarTech AI can perform Create, Read, Update, and Delete (CRUD) operations on files in your Eclipse workspace. All operations are executed through special command syntax that the AI generates based on your requests.

## 🔧 How It Works

1. **Chat with AI**: Ask FarTech AI to perform file operations using natural language
2. **AI Generates Commands**: The AI responds with special command syntax
3. **User Confirmation**: For destructive operations, you'll be prompted to confirm
4. **Execution**: The operation is performed and results are displayed
5. **Workspace Update**: The workspace is automatically re-indexed after changes

## 📖 Command Reference

### 🔍 READ Operations

#### Read File Content
**Syntax**: `[READ_FILE:path/to/file.ext]`

**Examples**:
```
User: "Show me the main Java class"
AI: [READ_FILE:src/main/java/Main.java]

User: "What's in the configuration file?"
AI: [READ_FILE:config/application.properties]

User: "Read the package.json file"
AI: [READ_FILE:package.json]
```

**Features**:
- Supports any text file format
- 100KB file size limit for performance
- Displays full file content in chat
- Error handling for missing/inaccessible files

#### List Directory Contents
**Syntax**: `[list_files:path/to/directory]`

**Examples**:
```
User: "What files are in the src directory?"
AI: [list_files:src]

User: "Show me all files in the project root"
AI: [list_files:.]

User: "List contents of the test folder"
AI: [list_files:src/test]
```

**Output Format**:
```
[DIR]  subdirectory/
[FILE] filename.ext (1234 bytes)
[FILE] another-file.txt (567 bytes)
```

### ✏️ CREATE/UPDATE Operations

#### Write File Content
**Syntax**: `[write_file:path/to/file.ext]content[end_file]`

**Examples**:

**Create New File**:
```
User: "Create a new README file"
AI: [write_file:README.md]
# My Project
This is a new project created with FarTech AI.

## Features
- Feature 1
- Feature 2
[end_file]
```

**Update Existing File**:
```
User: "Add a new method to the Utils class"
AI: [write_file:src/Utils.java]
public class Utils {
    public static String formatDate(Date date) {
        return new SimpleDateFormat("yyyy-MM-dd").format(date);
    }
    
    public static String newMethod() {
        return "Hello from FarTech AI!";
    }
}
[end_file]
```

**Create Configuration File**:
```
User: "Create a database configuration file"
AI: [write_file:config/database.properties]
# Database Configuration
db.host=localhost
db.port=5432
db.name=myapp
db.username=admin
db.password=secret
[end_file]
```

**Features**:
- Creates parent directories automatically if they don't exist
- Overwrites existing files (with user confirmation)
- Supports any file format (text, code, config, etc.)
- User confirmation required before writing

### 🗑️ DELETE Operations

#### Delete File
**Syntax**: `[delete_file:path/to/file.ext]`

**Examples**:
```
User: "Remove the old configuration file"
AI: [delete_file:old-config.xml]

User: "Delete the temporary log file"
AI: [delete_file:temp/debug.log]

User: "Remove the unused test file"
AI: [delete_file:src/test/OldTest.java]
```

**Safety Features**:
- **Always requires user confirmation**
- Shows file path in confirmation dialog
- Cannot be undone - permanent deletion
- Error handling for missing files

### 📋 **ADVANCED File Operations**

#### Copy File
**Syntax**: `[copy_file:source/path:destination/path]`

**Examples**:
```
User: "Make a backup copy of the main config file"
AI: [copy_file:config/app.properties:config/app.properties.backup]

User: "Copy the template to create a new service"
AI: [copy_file:templates/ServiceTemplate.java:src/services/UserService.java]

User: "Duplicate the test file for modification"
AI: [copy_file:src/test/BaseTest.java:src/test/UserTest.java]
```

#### Move File
**Syntax**: `[move_file:source/path:destination/path]`

**Examples**:
```
User: "Move the old files to archive folder"
AI: [move_file:old-data.txt:archive/old-data.txt]

User: "Relocate the utility class to the utils package"
AI: [move_file:src/Helper.java:src/utils/Helper.java]

User: "Move configuration to the config directory"
AI: [move_file:database.properties:config/database.properties]
```

#### Rename File
**Syntax**: `[rename_file:old/path:new/path]`

**Examples**:
```
User: "Rename the main class to follow naming conventions"
AI: [rename_file:src/main.java:src/Main.java]

User: "Change the config file extension"
AI: [rename_file:config.txt:config.properties]

User: "Rename the test file to be more descriptive"
AI: [rename_file:test1.java:UserServiceTest.java]
```

#### Create Directory
**Syntax**: `[create_dir:path/to/directory]`

**Examples**:
```
User: "Create a new package for services"
AI: [create_dir:src/main/java/com/company/services]

User: "Set up a resources directory"
AI: [create_dir:src/main/resources]

User: "Create a backup folder"
AI: [create_dir:backup]
```

#### Backup File
**Syntax**: `[backup_file:path/to/file.ext]`

**Examples**:
```
User: "Create a backup of the main configuration file"
AI: [backup_file:config/application.properties]

User: "Backup the database schema before changes"
AI: [backup_file:database/schema.sql]

User: "Save a copy of the important class file"
AI: [backup_file:src/main/java/ImportantClass.java]
```

**Backup Features**:
- **Automatic Timestamping**: Backups include timestamp in filename
- **Organized Storage**: All backups stored in `.fartech-backups` directory
- **Automatic Backup**: File modifications automatically create backups
- **Version History**: Multiple backups preserved for each file

### 💻 Command Execution

#### PowerShell Commands
**Syntax**: `[exec_powershell:command]`

**Examples**:
```
User: "Run Maven build"
AI: [exec_powershell:mvn clean install]

User: "Check Git status"
AI: [exec_powershell:git status]

User: "Install npm dependencies"
AI: [exec_powershell:npm install]
```

#### CMD Commands
**Syntax**: `[exec_cmd:command]`

**Examples**:
```
User: "Check Java version"
AI: [exec_cmd:java -version]

User: "List directory contents"
AI: [exec_cmd:dir]

User: "Show current directory"
AI: [exec_cmd:cd]
```

#### PowerShell with Working Directory
**Syntax**: `[exec_powershell_dir:working/directory:command]`

**Examples**:
```
User: "Run tests in the test directory"
AI: [exec_powershell_dir:src/test:npm test]

User: "Build the frontend in the web folder"
AI: [exec_powershell_dir:frontend:npm run build]
```

## 🛡️ Security & Safety

### 🔐 User Confirmation & Security
All destructive operations require explicit user confirmation:
- **File Writing**: Shows file path and content preview
- **File Deletion**: Shows file path with warning about permanent deletion
- **File Moving**: Shows source and destination paths with warning
- **Command Execution**: Shows command and working directory
- **Enhanced Dialogs**: Clear, detailed confirmation messages with operation details

### 💾 Automatic Backup System
- **Auto-Backup**: Automatically creates backups before modifying existing files
- **Timestamped Backups**: Each backup includes creation timestamp (format: `filename.backup_YYYYMMDD_HHMMSS`)
- **Organized Storage**: All backups stored in `.fartech-backups` directory
- **Version History**: Multiple backup versions preserved for each file
- **Manual Backup**: Use `[backup_file:path]` command for explicit backup creation
- **Backup Notifications**: System messages confirm backup creation with file paths

### 🔍 Enhanced Error Handling & Validation
- **File Size Limits**: Read operations limited to 100KB files for performance
- **Path Validation**: Checks file paths and permissions before operations
- **Comprehensive Error Messages**: Detailed error information for failed operations
- **Operation Status**: Clear success/failure indicators with icons (✅/❌)
- **Progress Indicators**: Real-time feedback during file operations (⏳)

### 🔄 Advanced Workspace Integration
- **Automatic Re-indexing**: Workspace automatically re-indexed after file changes
- **Eclipse Integration**: Seamless integration with Eclipse file system
- **Project Structure Respect**: Operations respect Eclipse project boundaries
- **Real-time Updates**: File explorer and workspace views updated immediately
- **Status Messages**: Enhanced feedback with emojis and formatting

## 💡 Best Practices

### 1. Use Descriptive Requests
```
✅ Good: "Create a new Java class for user authentication"
❌ Poor: "Make a file"
```

### 2. Specify File Paths Clearly
```
✅ Good: "Read the main configuration in src/main/resources/config.properties"
❌ Poor: "Show me the config file"
```

### 3. Review Before Confirming
Always review the confirmation dialog before approving destructive operations.

### 4. Use Relative Paths
Paths are relative to your Eclipse workspace root:
```
✅ Good: src/main/java/Main.java
❌ Poor: C:/Users/<USER>/workspace/project/src/main/java/Main.java
```

### 5. Backup Important Files
Before major changes, ask AI to create backups:
```
User: "Create a backup of the main config file before modifying it"
```

## 🚀 Advanced Usage

### Batch Operations
You can request multiple operations in one conversation:
```
User: "Create a new service class, add it to the main application, and create a test for it"
```

### Context-Aware Operations
FarTech AI understands your project context:
```
User: "Add logging to all the service classes"
AI: (Reads existing service classes and adds appropriate logging)
```

### Integration with Other Features
CRUD operations work seamlessly with:
- Code analysis and suggestions
- Workspace indexing
- Memory management
- Smart Apply functionality

## 🔧 Troubleshooting

### Common Issues

**File Not Found**
- Check file path spelling
- Ensure file exists in workspace
- Use relative paths from workspace root

**Permission Denied**
- Check file permissions
- Ensure Eclipse has write access
- Close file if open in editor

**Command Execution Fails**
- Verify command syntax
- Check if required tools are installed
- Ensure proper working directory

### Getting Help
If you encounter issues:
1. Check the error message in the chat
2. Verify file paths and permissions
3. Try the operation manually to confirm it works
4. Contact support if problems persist

---

**Made with ❤️ by the FarTech AI Team**
