# 🧪 FarTech AI CRUD Operations Testing Guide

This guide provides comprehensive testing procedures for FarTech AI's file operations to ensure they work correctly and safely.

## 🎯 Testing Overview

The CRUD operations should be tested in a safe environment before using them on important files. Always test with sample files first.

## 📋 Test Checklist

### ✅ Basic CRUD Operations

#### 1. READ Operations
- [ ] **Read existing file**: Test with a small text file
- [ ] **Read non-existent file**: Should show appropriate error
- [ ] **Read large file**: Test 100KB+ file (should show size limit error)
- [ ] **Read directory**: Should show error message
- [ ] **List directory contents**: Test with various directories

#### 2. CREATE/WRITE Operations
- [ ] **Create new file**: Test creating a simple text file
- [ ] **Overwrite existing file**: Should create backup automatically
- [ ] **Create file in non-existent directory**: Should create parent directories
- [ ] **Write large content**: Test with substantial content
- [ ] **Write special characters**: Test with Unicode and special characters

#### 3. UPDATE Operations
- [ ] **Modify existing file**: Should create backup before modification
- [ ] **Update file permissions**: Test with read-only files
- [ ] **Update file in use**: Test with files open in Eclipse editor

#### 4. DELETE Operations
- [ ] **Delete existing file**: Should require confirmation
- [ ] **Delete non-existent file**: Should show appropriate error
- [ ] **Delete read-only file**: Should handle permission errors
- [ ] **Delete directory**: Should show error (not supported)

### ✅ Advanced Operations

#### 5. COPY Operations
- [ ] **Copy file to new location**: Test basic copy functionality
- [ ] **Copy with overwrite**: Should handle existing destination files
- [ ] **Copy to non-existent directory**: Should create parent directories
- [ ] **Copy large file**: Test performance with larger files

#### 6. MOVE Operations
- [ ] **Move file to new location**: Test basic move functionality
- [ ] **Move with overwrite**: Should handle existing destination files
- [ ] **Move to non-existent directory**: Should create parent directories
- [ ] **Move file in use**: Test with files open in Eclipse

#### 7. RENAME Operations
- [ ] **Rename file**: Test basic rename functionality
- [ ] **Rename with extension change**: Test changing file extensions
- [ ] **Rename to existing name**: Should show error
- [ ] **Rename with special characters**: Test Unicode support

#### 8. DIRECTORY Operations
- [ ] **Create directory**: Test basic directory creation
- [ ] **Create nested directories**: Test creating directory structure
- [ ] **Create existing directory**: Should show appropriate message

### ✅ Safety Features

#### 9. BACKUP System
- [ ] **Automatic backup creation**: Verify backups are created before modifications
- [ ] **Backup file naming**: Check timestamp format in backup names
- [ ] **Backup directory creation**: Verify `.fartech-backups` directory is created
- [ ] **Manual backup command**: Test explicit backup creation
- [ ] **Multiple backups**: Verify multiple versions are preserved

#### 10. Confirmation Dialogs
- [ ] **Write confirmation**: Verify confirmation dialog appears
- [ ] **Delete confirmation**: Verify warning about permanent deletion
- [ ] **Move confirmation**: Verify warning about file relocation
- [ ] **Command execution confirmation**: Verify command preview

#### 11. Error Handling
- [ ] **Permission errors**: Test with read-only files/directories
- [ ] **Invalid paths**: Test with malformed file paths
- [ ] **Network paths**: Test behavior with network locations
- [ ] **Long file names**: Test with very long file names

### ✅ UI Feedback

#### 12. Progress Indicators
- [ ] **Operation progress**: Verify ⏳ progress messages appear
- [ ] **Success indicators**: Verify ✅ success messages with details
- [ ] **Error indicators**: Verify ❌ error messages with details
- [ ] **System messages**: Verify workspace re-indexing messages

#### 13. Enhanced Feedback
- [ ] **File operation results**: Check detailed result messages
- [ ] **Backup notifications**: Verify backup creation messages
- [ ] **Status formatting**: Check emoji and formatting in messages

## 🧪 Test Scenarios

### Scenario 1: Basic File Lifecycle
1. Create a new test file with content
2. Read the file to verify content
3. Modify the file (should create backup)
4. Copy the file to a new location
5. Rename the original file
6. Delete the copied file
7. Verify backup was created

### Scenario 2: Safety Testing
1. Create a test file with important content
2. Attempt to overwrite it (should create backup)
3. Verify backup exists in `.fartech-backups`
4. Test canceling operations in confirmation dialogs
5. Verify original file is unchanged when operations are canceled

### Scenario 3: Error Handling
1. Try to read a non-existent file
2. Try to delete a non-existent file
3. Try to copy to an invalid path
4. Try to create a file in a read-only directory
5. Verify appropriate error messages for each case

### Scenario 4: Workspace Integration
1. Perform file operations in Eclipse workspace
2. Verify Eclipse file explorer updates
3. Check that workspace indexing occurs
4. Verify project structure is respected

## 🔧 Manual Testing Commands

### Test File Creation
```
User: "Create a test file called test.txt with some sample content"
Expected AI Response: [write_file:test.txt]This is sample content for testing.[end_file]
```

### Test File Reading
```
User: "Read the contents of test.txt"
Expected AI Response: [READ_FILE:test.txt]
```

### Test File Backup
```
User: "Create a backup of test.txt"
Expected AI Response: [backup_file:test.txt]
```

### Test File Copy
```
User: "Copy test.txt to test_copy.txt"
Expected AI Response: [copy_file:test.txt:test_copy.txt]
```

### Test Directory Listing
```
User: "List all files in the current directory"
Expected AI Response: [list_files:.]
```

## 🚨 Safety Reminders

1. **Always test in a safe environment** with non-critical files
2. **Verify backup creation** before performing destructive operations
3. **Check confirmation dialogs** carefully before approving operations
4. **Monitor system messages** for operation results and errors
5. **Keep backups** of important files outside the plugin system

## 📊 Test Results Template

```
Test Date: ___________
Eclipse Version: ___________
Plugin Version: ___________

Basic CRUD Operations: ✅ / ❌
Advanced Operations: ✅ / ❌
Safety Features: ✅ / ❌
UI Feedback: ✅ / ❌

Issues Found:
- 
- 
- 

Notes:
- 
- 
- 
```

---

**Remember**: Thorough testing ensures reliable and safe file operations. Always test new features before using them on important projects.
