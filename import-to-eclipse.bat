@echo off
echo 🎯 FarTech AI Plugin - Eclipse Import Helper
echo ============================================
echo.

echo 📋 ECLIPSE IMPORT INSTRUCTIONS:
echo ===============================
echo.
echo 1. 🚀 Start Eclipse IDE:
echo    "%ECLIPSE_HOME%\eclipse.exe" -clean
echo    (or: "C:\Dev\APP\eclipse-jee-2019-12\eclipse.exe" -clean)
echo.
echo 2. 📁 Import Project:
echo    File → Import → General → Existing Projects into Workspace
echo    Browse to: %CD%
echo    Select "eclipse-fartech-plugin" project
echo    ✅ Check "Copy projects into workspace" (recommended)
echo    Click Finish
echo.
echo 3. 🔧 Verify Project Setup:
echo    - Project should appear in Package Explorer
echo    - No compilation errors should be visible
echo    - All source files should be properly recognized
echo.
echo 4. 📦 Export Plugin:
echo    Right-click project → Export → Plug-in Development → Deployable plug-ins and fragments
echo    Destination: Choose a folder (e.g., C:\temp\plugin-export)
echo    ✅ Check "Use class files compiled in the workspace"
echo    Click Finish
echo.
echo 5. 🚀 Install Plugin:
echo    Copy the generated JAR from export folder to:
echo    "C:\Dev\APP\eclipse-jee-2019-12\dropins\"
echo    Restart Eclipse with -clean flag
echo.

echo.
echo 🔍 TROUBLESHOOTING:
echo ==================
echo.
echo ❌ If compilation errors appear:
echo    - Check that all required Eclipse plugins are installed
echo    - Verify Java Build Path includes Eclipse SDK
echo    - Clean and rebuild project (Project → Clean)
echo.
echo ❌ If plugin doesn't load:
echo    - Check Eclipse Error Log (Window → Show View → Error Log)
echo    - Verify MANIFEST.MF has correct Bundle-SymbolicName
echo    - Clear OSGi cache and restart with -clean
echo.

echo.
echo 📊 CURRENT PROJECT STATUS:
echo ==========================
echo ✅ Source code: Ready
echo ✅ MANIFEST.MF: Configured
echo ✅ Plugin.xml: Valid
echo ✅ Dependencies: Defined
echo ✅ Build structure: Clean
echo.
echo 🎯 Project location: %CD%
echo.

set /p start_eclipse="Start Eclipse now? (y/n): "
if /i "%start_eclipse%"=="y" (
    echo 🚀 Starting Eclipse...
    if exist "C:\Dev\APP\eclipse-jee-2019-12\eclipse.exe" (
        start "" "C:\Dev\APP\eclipse-jee-2019-12\eclipse.exe" -clean
        echo ✅ Eclipse started with -clean flag
    ) else (
        echo ❌ Eclipse not found at expected location
        echo Please start Eclipse manually and follow the import steps above
    )
) else (
    echo 📋 Please start Eclipse manually and follow the import steps above
)

echo.
pause
