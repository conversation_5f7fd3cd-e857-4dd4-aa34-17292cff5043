<?xml version="1.0" encoding="UTF-8"?>
<?eclipse version="3.4"?>
<plugin
   id="com.example.fartechplugin"
   name="FarTech AI Plugin"
   version="1.0.0"
   provider-name="FarTech"
   class="com.example.fartechplugin.FarTechPlugin">

   <!-- Plugin Activator - Removed startup extension to avoid IStartup requirement -->

   <!-- Views -->
   <extension
         point="org.eclipse.ui.views">
      <category
            name="FarTech AI"
            id="com.example.fartechplugin.category">
      </category>
      <view
            name="FarTech AI Assistant"
            category="com.example.fartechplugin.category"
            class="com.example.fartechplugin.FarTechView"
            id="com.example.fartechplugin.FarTechView">
      </view>
   </extension>

   <!-- Preferences - Removed to avoid compilation issues -->
   <!-- Settings are now handled through the FarTechSettingsDialog in the view -->

   <!-- Menu Contributions -->
   <extension
         point="org.eclipse.ui.menus">
      <!-- Main Menu -->
      <menuContribution
            locationURI="menu:org.eclipse.ui.main.menu">
         <menu
               label="FarTech AI"
               mnemonic="F"
               id="com.example.fartechplugin.menu">
            <command
                  commandId="com.example.fartechplugin.commands.openView"
                  label="Open AI Assistant"
                  mnemonic="O"
                  style="push">
            </command>
            <separator
                  name="com.example.fartechplugin.separator1"
                  visible="true">
            </separator>
            <command
                  commandId="com.example.fartechplugin.commands.analyzeCode"
                  label="Analyze Selected Code"
                  mnemonic="A"
                  style="push">
            </command>
            <command
                  commandId="com.example.fartechplugin.commands.generateTests"
                  label="Generate Tests"
                  mnemonic="T"
                  style="push">
            </command>
            <command
                  commandId="com.example.fartechplugin.commands.generateDocs"
                  label="Generate Documentation"
                  mnemonic="D"
                  style="push">
            </command>
            <!-- Preferences removed - settings available in view -->
         </menu>
      </menuContribution>

      <!-- Context Menu for Java Editor -->
      <menuContribution
            locationURI="popup:org.eclipse.jdt.ui.CompilationUnitEditor.EditorContext">
         <menu
               label="FarTech AI"
               id="com.example.fartechplugin.contextmenu">
            <command
                  commandId="com.example.fartechplugin.commands.analyzeCode"
                  label="Analyze with AI"
                  style="push">
            </command>
            <command
                  commandId="com.example.fartechplugin.commands.explainCode"
                  label="Explain Code"
                  style="push">
            </command>
            <command
                  commandId="com.example.fartechplugin.commands.refactorCode"
                  label="Suggest Refactoring"
                  style="push">
            </command>
            <command
                  commandId="com.example.fartechplugin.commands.generateTests"
                  label="Generate Unit Tests"
                  style="push">
            </command>
         </menu>
      </menuContribution>

      <!-- Toolbar Contribution -->
      <menuContribution
            locationURI="toolbar:org.eclipse.ui.main.toolbar">
         <toolbar
               id="com.example.fartechplugin.toolbar">
            <command
                  commandId="com.example.fartechplugin.commands.openView"
                  label="FarTech AI Assistant"
                  style="push"
                  tooltip="Open FarTech AI Assistant">
            </command>
         </toolbar>
      </menuContribution>
   </extension>

   <!-- Commands -->
   <extension
         point="org.eclipse.ui.commands">
      <category
            name="FarTech AI Commands"
            id="com.example.fartechplugin.commands.category">
      </category>
      <command
            name="Open AI Assistant"
            categoryId="com.example.fartechplugin.commands.category"
            id="com.example.fartechplugin.commands.openView">
      </command>
      <command
            name="Analyze Code"
            categoryId="com.example.fartechplugin.commands.category"
            id="com.example.fartechplugin.commands.analyzeCode">
      </command>
      <command
            name="Explain Code"
            categoryId="com.example.fartechplugin.commands.category"
            id="com.example.fartechplugin.commands.explainCode">
      </command>
      <command
            name="Refactor Code"
            categoryId="com.example.fartechplugin.commands.category"
            id="com.example.fartechplugin.commands.refactorCode">
      </command>
      <command
            name="Generate Tests"
            categoryId="com.example.fartechplugin.commands.category"
            id="com.example.fartechplugin.commands.generateTests">
      </command>
      <command
            name="Generate Documentation"
            categoryId="com.example.fartechplugin.commands.category"
            id="com.example.fartechplugin.commands.generateDocs">
      </command>
   </extension>

   <!-- Command Handlers -->
   <extension
         point="org.eclipse.ui.handlers">
      <handler
            commandId="com.example.fartechplugin.commands.openView"
            class="com.example.fartechplugin.handlers.OpenViewHandler">
      </handler>
      <handler
            commandId="com.example.fartechplugin.commands.analyzeCode"
            class="com.example.fartechplugin.handlers.AnalyzeCodeHandler">
      </handler>
      <handler
            commandId="com.example.fartechplugin.commands.explainCode"
            class="com.example.fartechplugin.handlers.ExplainCodeHandler">
      </handler>
      <handler
            commandId="com.example.fartechplugin.commands.refactorCode"
            class="com.example.fartechplugin.handlers.RefactorCodeHandler">
      </handler>
      <handler
            commandId="com.example.fartechplugin.commands.generateTests"
            class="com.example.fartechplugin.handlers.GenerateTestsHandler">
      </handler>
      <handler
            commandId="com.example.fartechplugin.commands.generateDocs"
            class="com.example.fartechplugin.handlers.GenerateDocsHandler">
      </handler>
   </extension>

   <!-- Key Bindings -->
   <extension
         point="org.eclipse.ui.bindings">
      <key
            commandId="com.example.fartechplugin.commands.openView"
            contextId="org.eclipse.ui.contexts.window"
            sequence="Ctrl+Alt+F"
            schemeId="org.eclipse.ui.defaultAcceleratorConfiguration">
      </key>
      <key
            commandId="com.example.fartechplugin.commands.analyzeCode"
            contextId="org.eclipse.ui.textEditorScope"
            sequence="Ctrl+Alt+A"
            schemeId="org.eclipse.ui.defaultAcceleratorConfiguration">
      </key>
      <key
            commandId="com.example.fartechplugin.commands.explainCode"
            contextId="org.eclipse.ui.textEditorScope"
            sequence="Ctrl+Alt+E"
            schemeId="org.eclipse.ui.defaultAcceleratorConfiguration">
      </key>
      <key
            commandId="com.example.fartechplugin.commands.generateTests"
            contextId="org.eclipse.ui.textEditorScope"
            sequence="Ctrl+Alt+T"
            schemeId="org.eclipse.ui.defaultAcceleratorConfiguration">
      </key>
   </extension>

   <!-- Perspective Extensions -->
   <extension
         point="org.eclipse.ui.perspectiveExtensions">
      <perspectiveExtension
            targetID="org.eclipse.jdt.ui.JavaPerspective">
         <view
               ratio="0.3"
               relative="org.eclipse.ui.views.TaskList"
               relationship="right"
               id="com.example.fartechplugin.FarTechView">
         </view>
      </perspectiveExtension>
   </extension>

</plugin>
