@echo off
echo 🔧 Recreating MANIFEST.MF for FarTech AI Plugin...
echo ================================================
echo.

REM Create META-INF directory
if not exist META-INF mkdir META-INF

REM Create the correct MANIFEST.MF with proper OSGi headers
echo 📝 Creating MANIFEST.MF with OSGi headers...
(
echo Manifest-Version: 1.0
echo Bundle-SymbolicName: com.example.fartechplugin;singleton:=true
echo Bundle-Name: FarTech AI Assistant Plugin
echo Bundle-Version: 2.1.0.nocache
echo Bundle-ManifestVersion: 2
echo Bundle-Activator: com.example.fartechplugin.FarTechPlugin
echo Bundle-RequiredExecutionEnvironment: JavaSE-1.8
echo Bundle-ActivationPolicy: lazy
echo Bundle-Vendor: FarTech AI
echo Require-Bundle: org.eclipse.ui;bundle-version="3.0.0",
echo  org.eclipse.core.runtime;bundle-version="3.0.0",
echo  org.eclipse.swt;bundle-version="3.0.0",
echo  org.eclipse.jface;bundle-version="3.0.0",
echo  org.eclipse.ui.workbench;bundle-version="3.0.0",
echo  org.eclipse.core.resources;bundle-version="3.0.0",
echo  org.eclipse.jface.text;bundle-version="3.0.0",
echo  org.eclipse.ui.editors;bundle-version="3.0.0",
echo  org.eclipse.core.jobs;bundle-version="3.0.0",
echo  org.eclipse.ui.ide;bundle-version="3.0.0"
echo Import-Package: org.osgi.framework;version="1.3.0"
echo.
) > META-INF\MANIFEST.MF

echo ✅ MANIFEST.MF created successfully

echo.
echo 📄 MANIFEST.MF contents:
type META-INF\MANIFEST.MF

echo.
echo 🔍 Verifying OSGi headers:
findstr "Bundle-SymbolicName" META-INF\MANIFEST.MF >nul && echo ✅ Bundle-SymbolicName present || echo ❌ Bundle-SymbolicName missing
findstr "Bundle-Activator" META-INF\MANIFEST.MF >nul && echo ✅ Bundle-Activator present || echo ❌ Bundle-Activator missing
findstr "Require-Bundle" META-INF\MANIFEST.MF >nul && echo ✅ Require-Bundle present || echo ❌ Require-Bundle missing

echo.
echo 🚀 Now building plugin with correct MANIFEST.MF...
call build-correct-jar.bat

pause
