package com.example.fartechplugin;

import org.osgi.framework.BundleActivator;
import org.osgi.framework.BundleContext;
import com.example.fartechplugin.core.PreferencesManager;
import com.example.fartechplugin.utils.ErrorHandler;

/**
 * The activator class controls the plug-in life cycle
 * Implements BundleActivator for proper OSGi bundle lifecycle
 */
public class FarTechPlugin implements BundleActivator {

    // The plug-in ID
    public static final String PLUGIN_ID = "com.example.fartechplugin";

    // The shared instance
    private static FarTechPlugin plugin;

    // Plugin services
    private ErrorHandler errorHandler;
    private PreferencesManager preferencesManager;

    // Bundle context
    private BundleContext bundleContext;

    /**
     * The constructor
     */
    public FarTechPlugin() {
        plugin = this;
    }

    /**
     * Called when the bundle is started by the framework
     */
    @Override
    public void start(BundleContext context) throws Exception {
        this.bundleContext = context;
        plugin = this;

        try {
            // Initialize core services
            initializeServices();

            // Log successful startup
            logInfo("FarTech AI Assistant plugin started successfully");

        } catch (Exception e) {
            logError("Failed to start FarTech AI Assistant plugin", e);
            throw e;
        }
    }

    /**
     * Called when the bundle is stopped by the framework
     */
    @Override
    public void stop(BundleContext context) throws Exception {
        try {
            // Cleanup resources
            cleanup();

            logInfo("FarTech AI Assistant plugin stopped successfully");

        } catch (Exception e) {
            logError("Error during plugin shutdown", e);
        } finally {
            plugin = null;
            this.bundleContext = null;
        }
    }

    /**
     * Initialize plugin services
     */
    private void initializeServices() {
        errorHandler = new ErrorHandler();
        preferencesManager = new PreferencesManager();

        // Initialize preferences with defaults
        preferencesManager.initializeDefaults();
    }

    /**
     * Cleanup plugin resources
     */
    private void cleanup() {
        // Cleanup any resources if needed
        if (preferencesManager != null) {
            // Save any pending preferences
            // preferencesManager.save(); // If such method exists
        }

        // Clear references
        errorHandler = null;
        preferencesManager = null;
    }

    /**
     * Returns the shared instance
     */
    public static FarTechPlugin getDefault() {
        return plugin;
    }

    /**
     * Get the plugin's error handler
     */
    public static ErrorHandler getErrorHandler() {
        return getDefault().errorHandler;
    }

    /**
     * Get the plugin's preferences manager
     */
    public static PreferencesManager getPreferencesManager() {
        return getDefault().preferencesManager;
    }

    // Removed getter methods for deleted utility classes

    /**
     * Log an info message (simplified)
     */
    public static void logInfo(String message) {
        System.out.println("[FarTech AI INFO] " + message);
    }

    /**
     * Log an error message (simplified)
     */
    public static void logError(String message, Throwable throwable) {
        System.err.println("[FarTech AI ERROR] " + message);
        if (throwable != null) {
            throwable.printStackTrace();
        }
    }

    /**
     * Log a warning message (simplified)
     */
    public static void logWarning(String message) {
        System.out.println("[FarTech AI WARNING] " + message);
    }

    /**
     * Check if the plugin is properly initialized
     */
    public static boolean isInitialized() {
        return plugin != null && plugin.errorHandler != null;
    }

    /**
     * Get plugin version (simplified)
     */
    public static String getVersion() {
        return "3.1.0.clean";
    }
}
