package com.example.fartechplugin;

import org.eclipse.swt.SWT;
import org.eclipse.swt.events.SelectionAdapter;
import org.eclipse.swt.events.SelectionEvent;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.*;
import com.example.fartechplugin.core.AIProviderManager;

/**
 * FarTech AI Settings Dialog - BULLETPROOF NO BASE URL VERSION
 * This dialog NEVER shows Base URL field and always uses FarTech AI endpoint
 */
public class FarTechSettingsDialog extends Dialog {
    
    private static final String FARTECH_AI_ENDPOINT = "https://ai.ad-ins.com/api";
    
    private AIProviderManager providerManager;
    private Shell shell;
    private Text apiKeyText;
    private Combo modelCombo;
    private Text testResponseText;
    private Button loadModelsButton;
    private Button testConnectionButton;
    
    private String[] availableModels = new String[0];
    private int result = SWT.CANCEL;
    
    public FarTechSettingsDialog(Shell parent, AIProviderManager providerManager) {
        super(parent, SWT.DIALOG_TRIM | SWT.APPLICATION_MODAL | SWT.RESIZE);
        this.providerManager = providerManager;
        setText("FarTech AI Assistant Settings - NO BASE URL");
    }
    
    public int open() {
        createContents();
        shell.open();
        shell.layout();
        
        Display display = getParent().getDisplay();
        while (!shell.isDisposed()) {
            if (!display.readAndDispatch()) {
                display.sleep();
            }
        }
        
        return result;
    }
    
    private void createContents() {
        shell = new Shell(getParent(), getStyle());
        shell.setSize(650, 550);
        shell.setText(getText());
        shell.setLayout(new GridLayout(1, false));
        
        // Center the dialog
        shell.setLocation(
            getParent().getLocation().x + (getParent().getSize().x - shell.getSize().x) / 2,
            getParent().getLocation().y + (getParent().getSize().y - shell.getSize().y) / 2
        );
        
        createMainContent();
        createButtonBar();
        
        // Load current settings and force FarTech AI endpoint
        loadCurrentSettings();
    }
    
    private void createMainContent() {
        Composite mainComposite = new Composite(shell, SWT.NONE);
        mainComposite.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
        mainComposite.setLayout(new GridLayout(1, false));
        
        // Header with FarTech AI branding
        Group headerGroup = new Group(mainComposite, SWT.NONE);
        headerGroup.setText("FarTech AI Configuration");
        headerGroup.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
        headerGroup.setLayout(new GridLayout(1, false));
        
        Label headerLabel = new Label(headerGroup, SWT.WRAP);
        headerLabel.setText("FarTech AI Assistant Settings\n\n" +
                           "Provider: FarTech AI (OpenAI Compatible)\n" +
                           "Endpoint: " + FARTECH_AI_ENDPOINT + "\n" +
                           "No Base URL configuration needed!");
        headerLabel.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
        
        // API Key field
        Group apiGroup = new Group(mainComposite, SWT.NONE);
        apiGroup.setText("API Key Configuration");
        apiGroup.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
        apiGroup.setLayout(new GridLayout(2, false));
        
        Label apiKeyLabel = new Label(apiGroup, SWT.NONE);
        apiKeyLabel.setText("API Key:");
        apiKeyText = new Text(apiGroup, SWT.BORDER | SWT.PASSWORD);
        apiKeyText.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false));
        apiKeyText.setToolTipText("Enter your FarTech AI API key");
        
        // Model selection
        Group modelGroup = new Group(mainComposite, SWT.NONE);
        modelGroup.setText("Model Selection");
        modelGroup.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
        modelGroup.setLayout(new GridLayout(3, false));
        
        Label modelLabel = new Label(modelGroup, SWT.NONE);
        modelLabel.setText("Model:");
        
        modelCombo = new Combo(modelGroup, SWT.DROP_DOWN | SWT.READ_ONLY);
        modelCombo.setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false));
        
        loadModelsButton = new Button(modelGroup, SWT.PUSH);
        loadModelsButton.setText("Load Models");
        loadModelsButton.setToolTipText("Load available models from FarTech AI");
        loadModelsButton.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent e) {
                loadAvailableModels();
            }
        });
        
        // Test connection section
        Group testGroup = new Group(mainComposite, SWT.NONE);
        testGroup.setText("Connection Test");
        testGroup.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
        testGroup.setLayout(new GridLayout(1, false));
        
        testConnectionButton = new Button(testGroup, SWT.PUSH);
        testConnectionButton.setText("Test Connection");
        testConnectionButton.setToolTipText("Test connection to FarTech AI");
        testConnectionButton.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent e) {
                testConnection();
            }
        });
        
        Label responseLabel = new Label(testGroup, SWT.NONE);
        responseLabel.setText("Test Response:");
        
        testResponseText = new Text(testGroup, SWT.BORDER | SWT.MULTI | SWT.V_SCROLL | SWT.WRAP);
        testResponseText.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));
        testResponseText.setText("Click 'Test Connection' to verify your FarTech AI configuration...");
        testResponseText.setEditable(false);
    }
    
    private void createButtonBar() {
        Composite buttonBar = new Composite(shell, SWT.NONE);
        buttonBar.setLayoutData(new GridData(SWT.FILL, SWT.BOTTOM, true, false));
        buttonBar.setLayout(new GridLayout(2, false));
        
        // Spacer
        new Label(buttonBar, SWT.NONE).setLayoutData(new GridData(SWT.FILL, SWT.CENTER, true, false));
        
        Composite buttons = new Composite(buttonBar, SWT.NONE);
        buttons.setLayout(new GridLayout(2, false));
        
        Button okButton = new Button(buttons, SWT.PUSH);
        okButton.setText("OK");
        okButton.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent e) {
                if (saveSettings()) {
                    result = SWT.OK;
                    shell.close();
                }
            }
        });
        
        Button cancelButton = new Button(buttons, SWT.PUSH);
        cancelButton.setText("Cancel");
        cancelButton.addSelectionListener(new SelectionAdapter() {
            @Override
            public void widgetSelected(SelectionEvent e) {
                result = SWT.CANCEL;
                shell.close();
            }
        });
        
        shell.setDefaultButton(okButton);
    }
    
    private void loadCurrentSettings() {
        // FORCE FarTech AI endpoint - this cannot be changed
        providerManager.setBaseUrl("OpenAI Compatible", FARTECH_AI_ENDPOINT);
        
        // Load current API key
        String currentApiKey = providerManager.getApiKey("OpenAI Compatible");
        if (currentApiKey != null) {
            apiKeyText.setText(currentApiKey);
        }
        
        // Load current model
        String currentModel = providerManager.getModel("OpenAI Compatible");
        if (currentModel != null) {
            modelCombo.setText(currentModel);
        }

        // Don't automatically load models - let user trigger manually
        // This prevents the "Loading models..." message on dialog open
    }
    
    private void loadAvailableModels() {
        String apiKey = apiKeyText.getText().trim();
        if (apiKey.isEmpty()) {
            showMessage("Please enter your API key first.", SWT.ICON_WARNING);
            return;
        }
        
        // FORCE FarTech AI endpoint
        providerManager.setApiKey("OpenAI Compatible", apiKey);
        providerManager.setBaseUrl("OpenAI Compatible", FARTECH_AI_ENDPOINT);
        
        try {
            loadModelsButton.setEnabled(false);
            loadModelsButton.setText("Loading...");
            
            // Get available models
            availableModels = providerManager.getAvailableModels();
            
            // Update combo box
            modelCombo.removeAll();
            for (String model : availableModels) {
                modelCombo.add(model);
            }
            
            // Select first model if available
            if (availableModels.length > 0) {
                modelCombo.select(0);
                // Don't show success message - just update the combo silently
            } else {
                showMessage("No models found. Please check your API key.", SWT.ICON_WARNING);
            }
            
        } catch (Exception e) {
            showMessage("Failed to load models: " + e.getMessage(), SWT.ICON_ERROR);
        } finally {
            loadModelsButton.setEnabled(true);
            loadModelsButton.setText("Load Models");
        }
    }
    
    private void testConnection() {
        String apiKey = apiKeyText.getText().trim();
        if (apiKey.isEmpty()) {
            showMessage("Please enter your API key first.", SWT.ICON_WARNING);
            return;
        }
        
        try {
            testConnectionButton.setEnabled(false);
            testConnectionButton.setText("Testing...");
            testResponseText.setText("Testing connection to FarTech AI...");
            
            // FORCE FarTech AI endpoint
            providerManager.setApiKey("OpenAI Compatible", apiKey);
            providerManager.setBaseUrl("OpenAI Compatible", FARTECH_AI_ENDPOINT);

            // Get currently selected model from dropdown
            String selectedModel = modelCombo.getText().trim();
            if (selectedModel.isEmpty() && modelCombo.getItemCount() > 0) {
                selectedModel = modelCombo.getItem(0); // Use first available model if none selected
            }

            // Test connection with selected model
            String response = providerManager.testConnection("OpenAI Compatible", apiKey, FARTECH_AI_ENDPOINT, selectedModel);
            testResponseText.setText("SUCCESS: Connection successful!\n\nResponse: " + response);

        } catch (Exception e) {
            testResponseText.setText("ERROR: Connection failed: " + e.getMessage());
        } finally {
            testConnectionButton.setEnabled(true);
            testConnectionButton.setText("Test Connection");
        }
    }
    
    private boolean saveSettings() {
        String apiKey = apiKeyText.getText().trim();
        if (apiKey.isEmpty()) {
            showMessage("Please enter your API key.", SWT.ICON_WARNING);
            return false;
        }
        
        // Save settings with FORCED FarTech AI endpoint
        providerManager.setApiKey("OpenAI Compatible", apiKey);
        providerManager.setBaseUrl("OpenAI Compatible", FARTECH_AI_ENDPOINT);
        
        if (modelCombo.getSelectionIndex() >= 0) {
            String selectedModel = modelCombo.getText();
            providerManager.setModel("OpenAI Compatible", selectedModel);
        }
        
        return true;
    }
    
    private void showMessage(String message, int iconType) {
        MessageBox messageBox = new MessageBox(shell, SWT.OK | iconType);
        messageBox.setMessage(message);
        messageBox.setText("FarTech AI Assistant");
        messageBox.open();
    }
}
