package com.example.fartechplugin;

import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.SashForm;
import org.eclipse.swt.layout.*;
import org.eclipse.swt.widgets.*;
import org.eclipse.ui.part.ViewPart;
import org.eclipse.core.resources.*;
import java.util.List;

import com.example.fartechplugin.core.AIProviderManager;
import com.example.fartechplugin.core.WorkspaceIndexer;
import com.example.fartechplugin.core.IndexingProgressListener;
import com.example.fartechplugin.core.IndexingFeedbackManager;
import com.example.fartechplugin.core.PreferencesManager;
import com.example.fartechplugin.core.MemoryManager;
import com.example.fartechplugin.core.SmartApplyManager;
import com.example.fartechplugin.core.CheckpointManager;
import com.example.fartechplugin.core.CodeSuggestion;
import com.example.fartechplugin.core.AutoCRUDProcessor;
import com.example.fartechplugin.ui.*;
import com.example.fartechplugin.ui.FileAttachmentManager.AttachedFile;
import com.example.fartechplugin.utils.EclipseCompatibility;
import com.example.fartechplugin.core.FileOperationsAgent;
import com.example.fartechplugin.core.IntelligentCRUDProcessor;

/**
 * Refactored FarTech AI Assistant view with improved architecture
 * Uses separate UI managers for better organization and maintainability
 */
public class FarTechView extends ViewPart implements
    ChatInterface.ChatInterfaceListener,
    FileAttachmentManager.FileAttachmentListener,
    FileExplorerManager.FileExplorerListener,
    ToolbarManager.ToolbarListener {
    
    public static final String ID = "com.example.fartechplugin.FarTechView";

    // UI Components
    private ChatInterface chatInterface;
    private FileAttachmentManager attachmentManager;
    private FileExplorerManager fileExplorerManager;
    private ToolbarManager toolbarManager;
    private Label statusLabel;
    
    // Core services
    private boolean agentMode = false;
    private FileOperationsAgent agent;
    private IntelligentCRUDProcessor intelligentCRUD;
    private AIProviderManager providerManager;
    private PreferencesManager preferencesManager;
    private WorkspaceIndexer workspaceIndexer;
    private MemoryManager memoryManager;
    private SmartApplyManager smartApplyManager;
    private CheckpointManager checkpointManager;
    private AutoCRUDProcessor autoCRUDProcessor;

    @Override
    public void createPartControl(Composite parent) {
        // Initialize core services
        initializeServices();

        // Initialize UI managers
        initializeUIManagers();

        // Create UI layout
        createUILayout(parent);

        // Initialize components
        initializeComponents();
    }
    
    private void initializeServices() {
        agent = new FileOperationsAgent();
        providerManager = new AIProviderManager();
        preferencesManager = new PreferencesManager();
        preferencesManager.loadIntoProviderManager(providerManager);
        workspaceIndexer = new WorkspaceIndexer();
        intelligentCRUD = new IntelligentCRUDProcessor(agent, workspaceIndexer);
        memoryManager = new MemoryManager();
        smartApplyManager = new SmartApplyManager();
        checkpointManager = new CheckpointManager();
        autoCRUDProcessor = new AutoCRUDProcessor(workspaceIndexer, agent);
        agentMode = preferencesManager.getAgentMode();

        // Show configuration status
        showConfigurationStatus();
    }
    
    private void initializeUIManagers() {
        chatInterface = new ChatInterface(this);
        attachmentManager = new FileAttachmentManager(this);
        fileExplorerManager = new FileExplorerManager(this);
        toolbarManager = new ToolbarManager(this);
        toolbarManager.setAgentMode(agentMode);
    }
    
    private void createUILayout(Composite parent) {
        parent.setLayout(new GridLayout(1, false));

        // Create toolbar
        toolbarManager.createToolbar(parent);

        // Create main content area with sash form
        SashForm mainSash = new SashForm(parent, SWT.HORIZONTAL);
        mainSash.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));

        // Left side: File Explorer
        fileExplorerManager.createFileExplorer(mainSash);

        // Right side: Chat interface
        chatInterface.createChatInterface(mainSash);

        // Set sash weights (25% file explorer, 75% chat)
        mainSash.setWeights(new int[]{25, 75});

        // Status bar
        createStatusBar(parent);
    }
    
    private void createStatusBar(Composite parent) {
        statusLabel = new Label(parent, SWT.BORDER);
        statusLabel.setLayoutData(new GridData(SWT.FILL, SWT.BOTTOM, true, false));
        updateStatusLabel();
    }
    
    private void initializeComponents() {
        fileExplorerManager.refreshFileExplorer();
        updateInputPlaceholder();
        updateStatusLabel();
        addWelcomeMessage();

        // Automatically start workspace indexing
        startAutomaticWorkspaceIndexing();
    }

    /**
     * Start automatic workspace indexing with enhanced progress tracking
     */
    private void startAutomaticWorkspaceIndexing() {
        // Add a small delay to let the UI finish initializing
        Thread autoIndexThread = new Thread(() -> {
            try {
                Thread.sleep(2000); // Wait 2 seconds for UI to stabilize

                if (!chatInterface.isDisposed()) {
                    // Set up progress listener for real-time feedback
                    IndexingProgressListener progressListener = new IndexingProgressListener() {
                        @Override
                        public void onIndexingStarted(int totalEstimatedFiles) {
                            if (!chatInterface.isDisposed()) {
                                chatInterface.getDisplay().asyncExec(() -> {
                                    chatInterface.addToConversation("System",
                                        String.format("🔄 Auto-indexing workspace...\n" +
                                                    "📊 Estimated files to process: %d\n" +
                                                    "⏱️ This will help me understand your project structure.",
                                                    totalEstimatedFiles), false);
                                });
                            }
                        }

                        @Override
                        public void onIndexingProgress(String currentFile, int filesProcessed, int totalFiles, int percentage) {
                            // Update progress every 10% or every 50 files
                            if (percentage % 10 == 0 || filesProcessed % 50 == 0) {
                                if (!chatInterface.isDisposed()) {
                                    chatInterface.getDisplay().asyncExec(() -> {
                                        chatInterface.addToConversation("System",
                                            String.format("📈 Indexing progress: %d%% (%d/%d files)",
                                                        percentage, filesProcessed, totalFiles), false);
                                    });
                                }
                            }
                        }

                        @Override
                        public void onDirectoryScanning(String directoryPath, int depth) {
                            // Optional: Show directory scanning progress for deep structures
                        }

                        @Override
                        public void onIndexingCompleted(int totalFiles, int totalDirectories, long durationMs) {
                            if (!chatInterface.isDisposed()) {
                                chatInterface.getDisplay().asyncExec(() -> {
                                    double seconds = durationMs / 1000.0;
                                    if (totalFiles > 0) {
                                        chatInterface.addToConversation("System",
                                            String.format("✅ Auto-indexing completed successfully!\n" +
                                                        "📁 Indexed %d files in %d directories\n" +
                                                        "⏱️ Completed in %.1f seconds\n" +
                                                        "🤖 I now have comprehensive context about your workspace!\n\n" +
                                                        "💡 You can now ask me to:\n" +
                                                        "• Read and analyze your code files\n" +
                                                        "• Create new files with proper structure\n" +
                                                        "• Modify existing code intelligently\n" +
                                                        "• Understand your project architecture",
                                                        totalFiles, totalDirectories, seconds), false);
                                    } else {
                                        chatInterface.addToConversation("System",
                                            String.format("⚠️ Auto-indexing completed but found 0 files (%.1f seconds)\n\n" +
                                                        "🔍 Possible reasons:\n" +
                                                        "• Workspace path detection issue\n" +
                                                        "• No supported file types in workspace\n" +
                                                        "• Files are in filtered directories\n" +
                                                        "• File size limits too restrictive\n\n" +
                                                        "📋 Check Eclipse console for detailed logs\n" +
                                                        "⚙️ Use 'Index Workspace' button to retry manually", seconds), false);
                                    }
                                });
                            }
                        }

                        @Override
                        public void onIndexingFailed(String error, Exception exception) {
                            if (!chatInterface.isDisposed()) {
                                chatInterface.getDisplay().asyncExec(() -> {
                                    chatInterface.addToConversation("System",
                                        String.format("❌ Auto-indexing failed: %s\n\n" +
                                                    "🔧 Troubleshooting:\n" +
                                                    "• Check workspace path: %s\n" +
                                                    "• Verify file permissions\n" +
                                                    "• Try manual indexing with 'Index Workspace' button\n" +
                                                    "• Check Eclipse console for detailed error logs",
                                                    error, workspaceIndexer.getWorkspaceRoot()), false);
                                });
                            }
                        }

                        @Override
                        public void onFileSkipped(String filePath, String reason) {
                            // Optional: Log skipped files for debugging
                        }

                        @Override
                        public void onIndexingCancelled() {
                            if (!chatInterface.isDisposed()) {
                                chatInterface.getDisplay().asyncExec(() -> {
                                    chatInterface.addToConversation("System",
                                        "⏹️ Auto-indexing was cancelled.", false);
                                });
                            }
                        }
                    };

                    // Add progress listener and start indexing
                    workspaceIndexer.addProgressListener(progressListener);

                    try {
                        workspaceIndexer.indexWorkspace(true);
                    } finally {
                        workspaceIndexer.removeProgressListener(progressListener);
                    }
                }
            } catch (Exception e) {
                if (!chatInterface.isDisposed()) {
                    chatInterface.getDisplay().asyncExec(() -> {
                        chatInterface.addToConversation("System",
                            String.format("❌ Auto-indexing encountered an error: %s\n" +
                                        "🔧 You can manually trigger indexing using the 'Index Workspace' button.",
                                        e.getMessage()), false);
                    });
                }
            }
        });

        autoIndexThread.setDaemon(true); // Don't prevent application shutdown
        autoIndexThread.start();
    }
    
    // ChatInterface.ChatInterfaceListener implementation
    @Override
    public void onSendMessage(String message) {
        sendMessage(message);
    }
    
    @Override
    public void onAttachFile() {
        attachmentManager.openFileDialog(chatInterface.getShell());
    }
    
    @Override
    public void onClipboardPaste() {
        attachmentManager.handleClipboardPaste(chatInterface.getDisplay());
    }
    
    // FileAttachmentManager.FileAttachmentListener implementation
    @Override
    public void onFileAttached(AttachedFile file) {
        chatInterface.addToConversation("System", 
            "File attached: " + file.getName() + " (" + attachmentManager.formatFileSize(file.getSize()) + ")", false);
    }
    
    @Override
    public void onAttachmentError(String message) {
        chatInterface.addToConversation("System", message, false);
    }
    
    @Override
    public void onAttachmentsUpdated(List<AttachedFile> files) {
        updateInputPlaceholder();
    }
    
    // FileExplorerManager.FileExplorerListener implementation
    @Override
    public void onFileSelected(Object file) {
        try {
            // Use reflection to get file path from Eclipse IFile
            String filePath = EclipseCompatibility.getFilePath(file);
            if (filePath != null) {
                chatInterface.addToConversation("System", "Selected file: " + filePath, false);
            } else {
                chatInterface.addToConversation("System", "Selected file: " + file.toString(), false);
            }
        } catch (Exception e) {
            chatInterface.addToConversation("System", "Selected file: " + file.toString(), false);
        }
    }
    
    @Override
    public void onExplorerError(String message) {
        chatInterface.addToConversation("System", message, false);
    }
    
    // ToolbarManager.ToolbarListener implementation
    @Override
    public void onSettingsClicked() {
        openSettings();
    }
    
    @Override
    public void onAgentModeToggled(boolean enabled) {
        agentMode = enabled;
        preferencesManager.setAgentMode(agentMode);
        updateStatusLabel();
        updateInputPlaceholder();
    }
    
    @Override
    public void onClearHistoryClicked() {
        chatInterface.clearConversation();
    }
    
    @Override
    public void onRefreshFilesClicked() {
        fileExplorerManager.refreshFileExplorer();
    }
    
    @Override
    public void onIndexWorkspaceClicked() {
        indexWorkspace();
    }
    
    @Override
    public void onShowIndexClicked() {
        showWorkspaceIndex();
    }
    
    // Helper methods
    private void updateInputPlaceholder() {
        String placeholder;
        if (agentMode) {
            placeholder = "Type your message... (Agent mode: I can read, write, and manage your files!)";
        } else {
            placeholder = "Type your message... (Enable Agent Mode for file operations)";
        }
        
        if (attachmentManager.hasAttachments()) {
            placeholder += " | " + attachmentManager.getAttachedFiles().size() + " file(s) attached";
        }
        
        chatInterface.setInputPlaceholder(placeholder);
    }
    
    public void updateStatusLabel() {
        if (statusLabel != null && !statusLabel.isDisposed()) {
            String provider = providerManager.getCurrentProviderName();
            boolean hasApiKey = providerManager.hasValidApiKey();
            String keyStatus = hasApiKey ? "[OK]" : "[NOT SET]";

            String status = String.format("Provider: %s %s | Agent Mode: %s",
                provider, keyStatus, agentMode ? "ENABLED" : "DISABLED");
            statusLabel.setText(status);
        }
    }
    
    private void openSettings() {
        try {
            // Direct creation of settings dialog without reflection
            Shell shell = statusLabel.getShell();
            FarTechSettingsDialog dialog = new FarTechSettingsDialog(shell, providerManager);

            int result = dialog.open();

            if (result == SWT.OK) {
                preferencesManager.saveFromProviderManager(providerManager);
                updateStatusLabel();
                chatInterface.addToConversation("System",
                    "✅ Settings updated successfully!\n" +
                    "Provider: " + providerManager.getCurrentProviderName() + "\n" +
                    "API Key: " + (providerManager.hasValidApiKey() ? "Configured" : "Not configured") + "\n" +
                    "Model: " + (providerManager.getModel("OpenAI Compatible") != null ?
                        providerManager.getModel("OpenAI Compatible") : "Not selected"),
                    false);
            } else {
                chatInterface.addToConversation("System", "Settings dialog cancelled.", false);
            }
        } catch (Exception e) {
            chatInterface.addToConversation("System",
                "❌ Failed to open settings dialog: " + e.getMessage() + "\n" +
                "Falling back to chat-based settings...", false);
            showSettingsInChat();
        }
    }
    
    private void showSettingsInChat() {
        StringBuilder settings = new StringBuilder();
        settings.append("=== FarTech AI Assistant Settings ===\n\n");
        settings.append("Current Provider: ").append(providerManager.getCurrentProviderName()).append("\n");
        
        boolean hasKey = providerManager.getApiKey(providerManager.getCurrentProviderName()) != null;
        String status = hasKey ? "[CONFIGURED]" : "[NOT SET]";
        settings.append("API Key Status: ").append(status).append("\n\n");
        
        settings.append("To configure API key:\n");
        settings.append("1. Get API key from: https://platform.openai.com/api-keys\n");
        settings.append("2. Use Eclipse Preferences > General > Security > Secure Storage\n");
        settings.append("3. Add key under 'com.example.fartechplugin' node\n\n");
        settings.append("The provider supports OpenAI-compatible APIs.");
        
        chatInterface.addToConversation("System", settings.toString(), false);
    }
    
    private void sendMessage(String message) {
        if (message.isEmpty() && !attachmentManager.hasAttachments()) {
            return;
        }

        // Prepare message with attachments
        StringBuilder fullMessage = new StringBuilder(message);
        List<AttachedFile> attachedFiles = attachmentManager.getAttachedFiles();

        if (!attachedFiles.isEmpty()) {
            if (fullMessage.length() > 0) {
                fullMessage.append("\n\n");
            }
            fullMessage.append("📎 Attached Files:\n");

            for (AttachedFile file : attachedFiles) {
                fullMessage.append("• ").append(file.getName())
                          .append(" (").append(attachmentManager.formatFileSize(file.getSize())).append(")\n");

                // Enhanced file content reading
                if (!file.isImage()) {
                    String content = file.getContentAsText();
                    if (!content.startsWith("[Binary file:")) {
                        // Determine file type for better formatting
                        String fileName = file.getName().toLowerCase();
                        String language = "";
                        if (fileName.endsWith(".java")) language = "java";
                        else if (fileName.endsWith(".js")) language = "javascript";
                        else if (fileName.endsWith(".py")) language = "python";
                        else if (fileName.endsWith(".xml")) language = "xml";
                        else if (fileName.endsWith(".json")) language = "json";
                        else if (fileName.endsWith(".sql")) language = "sql";
                        else if (fileName.endsWith(".html")) language = "html";
                        else if (fileName.endsWith(".css")) language = "css";

                        fullMessage.append("\n📄 File Content (").append(file.getName()).append("):\n");
                        fullMessage.append("```").append(language).append("\n");

                        // Limit content size but provide more context
                        String displayContent = content.length() > 5000 ?
                            content.substring(0, 5000) + "\n... (content truncated, " +
                            (content.length() - 5000) + " more characters)" : content;
                        fullMessage.append(displayContent);
                        fullMessage.append("\n```\n");

                        // Add file path for context
                        fullMessage.append("📍 File Path: ").append(file.getPath()).append("\n\n");
                    } else {
                        fullMessage.append("⚠️ Binary file - content not displayed\n\n");
                    }
                } else {
                    fullMessage.append("🖼️ Image file attached\n\n");
                }
            }
        }

        // Add user message to conversation
        chatInterface.addToConversation("You", fullMessage.toString(), false);

        // Clear attachments after sending
        attachmentManager.clearAttachments();

        // Disable send button during processing
        chatInterface.setSendButtonEnabled(false);

        // Process in background thread
        Thread processThread = new Thread(() -> {
            try {
                String currentProject = getCurrentProjectName();
                String fileType = getCurrentFileType(attachedFiles);
                String finalAiResponse;

                if (agentMode) {
                    // First, try automatic CRUD operations based on user message
                    // Execute on UI thread to avoid threading issues
                    String autoCRUDResponse = autoCRUDProcessor.processUserMessage(
                        message, "", this);

                    // If auto CRUD handled the request, use that response
                    if (autoCRUDResponse != null && !autoCRUDResponse.isEmpty() && !autoCRUDResponse.equals("")) {
                        finalAiResponse = autoCRUDResponse;
                    } else {
                        // Get comprehensive workspace context if available
                        String workspaceContext = "";
                        if (workspaceIndexer.getFileCount() > 0) {
                            workspaceContext = "\n\n" + workspaceIndexer.getComprehensiveWorkspaceContext() + "\n";
                        } else {
                            workspaceContext = "\n\n=== WORKSPACE CONTEXT ===\n" +
                                             "No workspace indexed yet. Use 'Index Workspace' to provide AI with project context.\n";
                        }

                        // Enhanced prompt with memory context
                        String memoryEnhancedPrompt = memoryManager.enhancePromptWithMemories(
                            fullMessage.toString(), currentProject, fileType);
                        String agentPrompt = createIntelligentAgentPrompt(memoryEnhancedPrompt, workspaceContext);
                        String rawResponse = providerManager.sendPrompt(agentPrompt);

                        // Process with intelligent CRUD detection
                        String intelligentResponse = intelligentCRUD.processIntelligentCRUD(rawResponse, fullMessage.toString(), this);

                        // Also process any manual agent commands for backward compatibility
                        finalAiResponse = agent.processAgentCommands(intelligentResponse, this, workspaceIndexer);
                    }
                } else {
                    // Enhanced prompt with memory context for regular chat
                    String memoryEnhancedPrompt = memoryManager.enhancePromptWithMemories(
                        fullMessage.toString(), currentProject, fileType);
                    String rawResponse = providerManager.sendPrompt(memoryEnhancedPrompt);

                    // Even in chat mode, use intelligent CRUD for automatic operations
                    finalAiResponse = intelligentCRUD.processIntelligentCRUD(rawResponse, fullMessage.toString(), this);
                }

                // Learn from this interaction (store for potential feedback)
                storeInteractionForLearning(currentProject, fileType, fullMessage.toString(), finalAiResponse);

                // Process code suggestions for Smart Apply
                processCodeSuggestions(finalAiResponse);

                // Update UI on main thread
                if (!chatInterface.isDisposed()) {
                    chatInterface.getDisplay().asyncExec(() -> {
                        chatInterface.addToConversation("FarTech AI", finalAiResponse, true);
                        chatInterface.setSendButtonEnabled(true);
                    });
                }

            } catch (Exception e) {
                if (!chatInterface.isDisposed()) {
                    chatInterface.getDisplay().asyncExec(() -> {
                        chatInterface.addToConversation("Error", "Failed to get response: " + e.getMessage(), true);
                        chatInterface.setSendButtonEnabled(true);
                    });
                }
            }
        });

        processThread.start();
    }

    private String createIntelligentAgentPrompt(String userMessage, String workspaceContext) {
        return "You are FarTech AI assistant with intelligent file operation capabilities. " +
            "You can automatically perform file operations based on conversation context without explicit commands.\n\n" +
            "User's message: " + userMessage + "\n\n" +
            "INTELLIGENT OPERATION GUIDELINES:\n" +
            "- When user asks to create/write code, provide the code in markdown code blocks\n" +
            "- When user asks to read/view files, mention the file paths naturally (use EXACT paths from workspace context)\n" +
            "- When user asks to modify existing code, provide the updated code\n" +
            "- When user asks about project structure, suggest directory organization\n" +
            "- Always provide helpful, natural responses with code examples\n" +
            "- Use the workspace context below to understand the project structure and existing files\n\n" +
            "WORKSPACE AWARENESS:\n" +
            "- Reference actual files and directories from the workspace context\n" +
            "- Suggest realistic file paths based on existing project structure\n" +
            "- Consider file types and patterns already present in the workspace\n" +
            "- When creating new files, follow the existing project conventions\n" +
            "- Use relative paths from the workspace root when possible\n\n" +
            "AUTOMATIC DETECTION:\n" +
            "- Code blocks in your response may be automatically saved as files\n" +
            "- File path references may trigger automatic file reading\n" +
            "- Modification requests may update existing files\n" +
            "- Directory suggestions may create folder structures\n\n" +
            "RESPONSE FORMAT:\n" +
            "- Use markdown code blocks with language specification\n" +
            "- Include file paths when relevant (e.g., 'Here's the UserService.java file:')\n" +
            "- Provide clear, helpful explanations\n" +
            "- Be conversational and natural\n" +
            "- Reference specific files from the workspace when applicable\n\n" +
            "SECURITY:\n" +
            "- All file operations will require user confirmation\n" +
            "- Automatic backups are created before modifications\n" +
            "- Users can cancel any detected operations\n\n" +
            workspaceContext;
    }

    private String createAgentPrompt(String userMessage, String workspaceContext) {
        return "You are FarTech AI assistant with file operation and command execution capabilities. " +
            "The user's message: " + userMessage + "\n\n" +
            "IMPORTANT: Only use commands when the user explicitly requests operations. " +
            "For general questions, respond normally without any commands.\n\n" +
            "Available commands:\n" +
            "FILE OPERATIONS:\n" +
            "- To read a file: [READ_FILE:/actual/path/to/file]\n" +
            "- To write/edit a file: [WRITE_FILE:/actual/path/to/file]\nActual content here...\n[END_FILE]\n" +
            "- To delete a file: [DELETE_FILE:/actual/path/to/file]\n" +
            "- To list files: [LIST_FILES:/actual/path/to/directory]\n\n" +
            "COMMAND EXECUTION:\n" +
            "- To run PowerShell: [EXEC_POWERSHELL:command here]\n" +
            "- To run CMD: [EXEC_CMD:command here]\n" +
            "- To run with working directory: [EXEC_POWERSHELL_DIR:/path/to/directory:command here]\n\n" +
            "SECURITY NOTES:\n" +
            "- Commands will require user confirmation for safety\n" +
            "- Use PowerShell for modern Windows operations\n" +
            "- Use CMD for legacy/compatibility operations\n" +
            "- Always explain what the command will do before suggesting it\n\n" +
            "Use REAL paths from the user's workspace, not examples. " +
            "Do not include these commands in explanations or examples - only use them for actual operations." +
            workspaceContext;
    }

    private void indexWorkspace() {
        chatInterface.addToConversation("System", "Starting workspace indexing... This may take a moment.", false);

        Thread indexThread = new Thread(() -> {
            try {
                workspaceIndexer.indexWorkspace();

                if (!chatInterface.isDisposed()) {
                    chatInterface.getDisplay().asyncExec(() -> {
                        int fileCount = workspaceIndexer.getFileCount();
                        int dirCount = workspaceIndexer.getDirectoryCount();
                        chatInterface.addToConversation("System",
                            String.format("Workspace indexing completed!\n" +
                                        "Indexed %d files in %d directories.\n" +
                                        "AI agent now has context about your workspace.",
                                        fileCount, dirCount), false);
                    });
                }
            } catch (Exception e) {
                if (!chatInterface.isDisposed()) {
                    chatInterface.getDisplay().asyncExec(() -> {
                        chatInterface.addToConversation("System",
                            "Indexing failed: " + e.getMessage(), false);
                    });
                }
            }
        });

        indexThread.start();
    }

    private void showWorkspaceIndex() {
        // Create feedback manager for enhanced reporting
        IndexingFeedbackManager feedbackManager = new IndexingFeedbackManager(workspaceIndexer);

        if (workspaceIndexer.getFileCount() == 0) {
            // Show detailed troubleshooting for zero files
            String report = feedbackManager.generateIndexingReport();
            chatInterface.addToConversation("System", report, false);

            // Add actionable suggestions
            List<String> suggestions = feedbackManager.getActionableSuggestions();
            if (!suggestions.isEmpty()) {
                StringBuilder suggestionText = new StringBuilder();
                suggestionText.append("💡 IMMEDIATE ACTIONS TO TRY:\n");
                for (int i = 0; i < suggestions.size(); i++) {
                    suggestionText.append(String.format("%d. %s\n", i + 1, suggestions.get(i)));
                }
                chatInterface.addToConversation("System", suggestionText.toString(), false);
            }
            return;
        }

        // Show comprehensive indexing report
        String report = feedbackManager.generateIndexingReport();
        chatInterface.addToConversation("System", report, false);

        // Add quick tips for using the indexed workspace
        String usageTips = "💡 HOW TO USE YOUR INDEXED WORKSPACE:\n\n" +
                          "🤖 AI CAPABILITIES NOW AVAILABLE:\n" +
                          "• \"Show me the main configuration file\"\n" +
                          "• \"Create a new service class for user management\"\n" +
                          "• \"What patterns do you see in my code?\"\n" +
                          "• \"Add error handling to my UserController\"\n" +
                          "• \"Explain the architecture of this project\"\n\n" +
                          "📁 FILE OPERATIONS:\n" +
                          "• Read and analyze any indexed file\n" +
                          "• Create new files following project patterns\n" +
                          "• Modify existing code intelligently\n" +
                          "• Understand relationships between files\n\n" +
                          "🔍 PROJECT INSIGHTS:\n" +
                          "• Code quality analysis\n" +
                          "• Architecture recommendations\n" +
                          "• Best practices suggestions\n" +
                          "• Refactoring opportunities";

        chatInterface.addToConversation("System", usageTips, false);
    }

    private void addWelcomeMessage() {
        String welcomeMessage = "🎉 Welcome to FarTech AI Assistant v4.0 with Intelligent Auto-CRUD!\n\n" +
                              "🚀 NEW INTELLIGENT FEATURES:\n" +
                              "🤖 Auto-CRUD: I automatically detect and perform file operations based on conversation\n" +
                              "🧠 Memory System: I learn your coding patterns and remember project context\n" +
                              "🔧 Smart Apply: I can detect and apply code suggestions automatically\n" +
                              "📸 Code Checkpoints: Safe coding with automatic backups and rollbacks\n" +
                              "🎯 Enhanced Context: Better understanding across files and projects\n\n" +
                              "💡 INTELLIGENT CAPABILITIES:\n" +
                              "* Auto-Create: Just ask me to create files - I'll detect and do it automatically\n" +
                              "* Auto-Read: Mention files you want to see - I'll read them for you\n" +
                              "* Auto-Modify: Ask for changes - I'll update files intelligently\n" +
                              "* Auto-Structure: Request project organization - I'll create directories\n" +
                              "* Smart Backup: Automatic backups before any modifications\n" +
                              "* Context-Aware: I understand your project and coding patterns\n\n" +
                              "🎯 TRY THESE NATURAL COMMANDS:\n" +
                              "- \"Create a Java service class for user management\"\n" +
                              "- \"Show me the main configuration file\"\n" +
                              "- \"Add logging to my UserController class\"\n" +
                              "- \"Set up a standard Maven project structure\"\n" +
                              "- \"Create a REST API for user operations\"\n" +
                              "- \"What patterns do you remember about my coding style?\"\n\n" +
                              "🛡️ SAFETY FEATURES:\n" +
                              "- All operations require your confirmation\n" +
                              "- Automatic backups before modifications\n" +
                              "- You can cancel any detected operation\n\n" +
                              "🏆 Eclipse-native AI with intelligent automation - completely FREE!\n" +
                              "Just chat naturally - I'll handle the file operations! 🚀";

        chatInterface.addToConversation("FarTech AI", welcomeMessage, true);
    }

    // Public methods for external access (used by command handlers and agent)
    public void sendMessageToAI(String message) {
        sendMessage(message);
    }

    public void addAIResponse(String response) {
        chatInterface.addToConversation("FarTech AI", response, true);
    }

    public void addErrorResponse(String errorMessage) {
        chatInterface.addToConversation("Error", errorMessage, true);
    }

    public void addSystemMessage(String message) {
        chatInterface.addToConversation("System", message, false);
    }

    /**
     * Add enhanced file operation feedback with icons and formatting
     */
    public void addFileOperationResult(String operation, String filePath, boolean success, String details) {
        String icon = success ? "✅" : "❌";
        String status = success ? "SUCCESS" : "FAILED";
        String message = String.format("%s File %s %s\n📁 Path: %s\n📝 Details: %s",
            icon, operation.toUpperCase(), status, filePath, details);
        chatInterface.addToConversation("File Operation", message, false);
    }

    /**
     * Add file operation progress indicator
     */
    public void addFileOperationProgress(String operation, String filePath) {
        String message = String.format("⏳ Processing file %s...\n📁 Path: %s",
            operation.toLowerCase(), filePath);
        chatInterface.addToConversation("System", message, false);
    }

    /**
     * Add file operation confirmation request with enhanced formatting
     */
    public void addFileOperationConfirmation(String operation, String details) {
        String message = String.format("🔐 CONFIRMATION REQUIRED\n\n%s\n\n%s",
            operation.toUpperCase(), details);
        chatInterface.addToConversation("Security", message, false);
    }

    public void enableSendButton() {
        chatInterface.setSendButtonEnabled(true);
    }

    public boolean isDisposed() {
        return chatInterface == null || chatInterface.isDisposed();
    }

    public boolean isAgentMode() {
        return agentMode;
    }

    public void showConfirmationDialog(String message, Runnable onConfirm) {
        if (!chatInterface.isDisposed()) {
            chatInterface.getDisplay().asyncExec(() -> {
                boolean result = EclipseCompatibility.showConfirmDialog(
                    chatInterface.getShell(),
                    "Confirm Action",
                    message
                );
                if (result && onConfirm != null) {
                    onConfirm.run();
                }
            });
        }
    }

    /**
     * Show configuration status in chat
     */
    private void showConfigurationStatus() {
        if (chatInterface != null && providerManager != null) {
            String configSummary = providerManager.getConfigurationSummary();
            String memoryStats = memoryManager.getMemoryStats();
            chatInterface.addToConversation("System",
                "🎉 FarTech AI Eclipse Plugin Initialized!\n\n" +
                "Configuration Status:\n" + configSummary + "\n\n" +
                "🧠 " + memoryStats +
                "\n✅ Ready to chat with AI! Type your message below.", false);
        }
    }

    /**
     * Get current project name for memory context
     */
    private String getCurrentProjectName() {
        try {
            // Try to get project name from workspace
            if (workspaceIndexer != null && workspaceIndexer.getFileCount() > 0) {
                // Extract project name from workspace context
                String context = workspaceIndexer.getWorkspaceContext();
                if (context.contains("Project:")) {
                    String[] lines = context.split("\n");
                    for (String line : lines) {
                        if (line.trim().startsWith("Project:")) {
                            return line.substring(line.indexOf(":") + 1).trim();
                        }
                    }
                }
            }
            return "default_project";
        } catch (Exception e) {
            return "default_project";
        }
    }

    /**
     * Get current file type from attached files
     */
    private String getCurrentFileType(List<AttachedFile> attachedFiles) {
        if (attachedFiles != null && !attachedFiles.isEmpty()) {
            String fileName = attachedFiles.get(0).getName().toLowerCase();
            if (fileName.endsWith(".java")) return "java";
            if (fileName.endsWith(".js")) return "javascript";
            if (fileName.endsWith(".py")) return "python";
            if (fileName.endsWith(".xml")) return "xml";
            if (fileName.endsWith(".json")) return "json";
            if (fileName.endsWith(".sql")) return "sql";
            if (fileName.endsWith(".html")) return "html";
            if (fileName.endsWith(".css")) return "css";
            if (fileName.endsWith(".ts")) return "typescript";
            if (fileName.endsWith(".php")) return "php";
        }
        return "general";
    }

    /**
     * Store interaction for learning (will be enhanced with feedback later)
     */
    private void storeInteractionForLearning(String projectName, String fileType, String userQuery, String aiResponse) {
        try {
            // For now, store with neutral feedback - can be enhanced later with user feedback mechanisms
            memoryManager.learnFromInteraction(projectName, fileType, userQuery, aiResponse, "neutral");
        } catch (Exception e) {
            System.err.println("Failed to store interaction for learning: " + e.getMessage());
        }
    }

    /**
     * Method to handle explicit user feedback (can be called from UI feedback buttons)
     */
    public void handleUserFeedback(String feedback) {
        try {
            // This could be enhanced to track the last interaction and update it with feedback
            chatInterface.addToConversation("System",
                "📝 Thank you for your feedback! FarTech AI is learning from your preferences.", false);
        } catch (Exception e) {
            System.err.println("Failed to handle user feedback: " + e.getMessage());
        }
    }

    /**
     * Process AI response for code suggestions and Smart Apply opportunities
     */
    private void processCodeSuggestions(String aiResponse) {
        try {
            List<CodeSuggestion> suggestions = smartApplyManager.parseCodeSuggestions(aiResponse);

            if (!suggestions.isEmpty()) {
                StringBuilder applyMessage = new StringBuilder();
                applyMessage.append("🔧 Smart Apply detected ").append(suggestions.size()).append(" code suggestion(s):\n\n");

                for (int i = 0; i < suggestions.size(); i++) {
                    CodeSuggestion suggestion = suggestions.get(i);
                    applyMessage.append(String.format("%d. %s (%s)\n",
                        i + 1,
                        suggestion.filePath != null ? suggestion.filePath : "New code",
                        suggestion.type.toString().toLowerCase().replace("_", " ")
                    ));
                }

                applyMessage.append("\n💡 Use Agent Mode to automatically apply these suggestions to your files!");

                chatInterface.addToConversation("System", applyMessage.toString(), false);
            }
        } catch (Exception e) {
            System.err.println("Failed to process code suggestions: " + e.getMessage());
        }
    }

    @Override
    public void setFocus() {
        if (chatInterface != null) {
            chatInterface.setFocus();
        }
    }
}
