package com.example.fartechplugin.core;

import java.util.*;
import java.time.LocalDateTime;

/**
 * Enhanced AI Agent Core for Production
 * Implements planning, reasoning, context management, and self-correction
 * Based on BRD requirements for IDE-integrated CRUDF operations
 */
public class AIAgentCore {
    
    private final PlanningEngine planningEngine;
    private final ContextManager contextManager;
    private final ExecutionEngine executionEngine;
    private final FeedbackLoop feedbackLoop;
    private final ToolRegistry toolRegistry;
    
    // Current task state
    private AgentTask currentTask;
    private TaskStatus taskStatus;
    private List<String> executionLog;
    
    public AIAgentCore() {
        this.planningEngine = new PlanningEngine();
        this.contextManager = new ContextManager();
        this.executionEngine = new ExecutionEngine();
        this.feedbackLoop = new FeedbackLoop();
        this.toolRegistry = new ToolRegistry();
        this.executionLog = new ArrayList<>();
        this.taskStatus = TaskStatus.IDLE;
        
        // Initialize available tools
        initializeTools();
    }
    
    /**
     * Main entry point for processing user requests
     * FR.A.1: Understand natural language user prompts for development tasks
     */
    public AgentResponse processUserRequest(String userPrompt, IDEContext ideContext) {
        try {
            logExecution("Processing user request: " + userPrompt);
            
            // Step 1: Update context with current IDE state
            contextManager.updateContext(ideContext);
            
            // Step 2: Parse and understand user intent
            UserIntent intent = parseUserIntent(userPrompt);
            logExecution("Parsed intent: " + intent.getType() + " - " + intent.getDescription());
            
            // Step 3: Create task and plan execution
            currentTask = new AgentTask(intent, contextManager.getCurrentContext());
            taskStatus = TaskStatus.PLANNING;
            
            // Step 4: Generate execution plan
            ExecutionPlan plan = planningEngine.createPlan(intent, contextManager.getCurrentContext(), toolRegistry);
            currentTask.setExecutionPlan(plan);
            logExecution("Generated plan with " + plan.getSteps().size() + " steps");
            
            // Step 5: Execute plan with user approval for modifications
            taskStatus = TaskStatus.EXECUTING;
            AgentResponse response = executionEngine.executePlan(plan, this);
            
            // Step 6: Process feedback and self-correct if needed
            if (response.requiresCorrection()) {
                response = handleSelfCorrection(response);
            }
            
            taskStatus = TaskStatus.COMPLETED;
            logExecution("Task completed successfully");
            
            return response;
            
        } catch (Exception e) {
            taskStatus = TaskStatus.FAILED;
            logExecution("Task failed: " + e.getMessage());
            return AgentResponse.error("Failed to process request: " + e.getMessage());
        }
    }
    
    /**
     * FR.A.2: Perform task decomposition and planning based on user intent
     */
    private UserIntent parseUserIntent(String userPrompt) {
        String lower = userPrompt.toLowerCase();
        
        // Analyze prompt to determine intent type and extract parameters
        if (lower.contains("create") || lower.contains("generate") || lower.contains("add")) {
            return new UserIntent(IntentType.CREATE, userPrompt, extractCreateParameters(userPrompt));
        } else if (lower.contains("read") || lower.contains("show") || lower.contains("explain") || lower.contains("analyze")) {
            return new UserIntent(IntentType.READ, userPrompt, extractReadParameters(userPrompt));
        } else if (lower.contains("update") || lower.contains("modify") || lower.contains("refactor") || lower.contains("fix")) {
            return new UserIntent(IntentType.UPDATE, userPrompt, extractUpdateParameters(userPrompt));
        } else if (lower.contains("delete") || lower.contains("remove")) {
            return new UserIntent(IntentType.DELETE, userPrompt, extractDeleteParameters(userPrompt));
        } else if (lower.contains("commit") || lower.contains("push") || lower.contains("git")) {
            return new UserIntent(IntentType.GIT_OPERATION, userPrompt, extractGitParameters(userPrompt));
        } else if (lower.contains("test") || lower.contains("run")) {
            return new UserIntent(IntentType.EXECUTE, userPrompt, extractExecuteParameters(userPrompt));
        } else {
            return new UserIntent(IntentType.GENERAL, userPrompt, new HashMap<>());
        }
    }
    
    /**
     * FR.A.6: Self-correct by re-planning or trying alternative approaches
     */
    private AgentResponse handleSelfCorrection(AgentResponse failedResponse) {
        logExecution("Attempting self-correction for failed response");
        
        // Analyze failure reason
        String failureReason = failedResponse.getErrorMessage();
        
        // Update context with failure information
        contextManager.addFailureContext(failureReason);
        
        // Generate alternative plan
        ExecutionPlan alternativePlan = planningEngine.createAlternativePlan(
            currentTask.getIntent(), 
            contextManager.getCurrentContext(), 
            toolRegistry,
            failureReason
        );
        
        if (alternativePlan != null) {
            logExecution("Generated alternative plan with " + alternativePlan.getSteps().size() + " steps");
            return executionEngine.executePlan(alternativePlan, this);
        } else {
            logExecution("No alternative plan available");
            return AgentResponse.error("Unable to complete task after self-correction attempt");
        }
    }
    
    /**
     * Initialize available tools for the agent
     */
    private void initializeTools() {
        // File system tools
        toolRegistry.registerTool(new ReadFileTool());
        toolRegistry.registerTool(new WriteFileTool());
        toolRegistry.registerTool(new DeleteFileTool());
        toolRegistry.registerTool(new CreateDirectoryTool());
        
        // Editor tools
        toolRegistry.registerTool(new InsertCodeTool());
        toolRegistry.registerTool(new ReplaceCodeTool());
        toolRegistry.registerTool(new FormatCodeTool());
        
        // Terminal tools
        toolRegistry.registerTool(new ExecuteCommandTool());
        toolRegistry.registerTool(new ReadTerminalOutputTool());
        
        // Git tools
        toolRegistry.registerTool(new GitStatusTool());
        toolRegistry.registerTool(new GitCommitTool());
        toolRegistry.registerTool(new GitPushTool());
        
        // Analysis tools
        toolRegistry.registerTool(new AnalyzeCodeTool());
        toolRegistry.registerTool(new FindReferencesTool());
        toolRegistry.registerTool(new GetTypeInfoTool());
        
        logExecution("Initialized " + toolRegistry.getToolCount() + " tools");
    }
    
    // Helper methods for parameter extraction
    private Map<String, Object> extractCreateParameters(String prompt) {
        Map<String, Object> params = new HashMap<>();
        // Extract file type, name, location, etc.
        if (prompt.contains(".java")) params.put("fileType", "java");
        if (prompt.contains(".js")) params.put("fileType", "javascript");
        if (prompt.contains(".py")) params.put("fileType", "python");
        // Add more extraction logic as needed
        return params;
    }
    
    private Map<String, Object> extractReadParameters(String prompt) {
        Map<String, Object> params = new HashMap<>();
        // Extract file names, line numbers, etc.
        return params;
    }
    
    private Map<String, Object> extractUpdateParameters(String prompt) {
        Map<String, Object> params = new HashMap<>();
        // Extract target files, modification type, etc.
        return params;
    }
    
    private Map<String, Object> extractDeleteParameters(String prompt) {
        Map<String, Object> params = new HashMap<>();
        // Extract target files/directories
        return params;
    }
    
    private Map<String, Object> extractGitParameters(String prompt) {
        Map<String, Object> params = new HashMap<>();
        // Extract commit message, branch, etc.
        return params;
    }
    
    private Map<String, Object> extractExecuteParameters(String prompt) {
        Map<String, Object> params = new HashMap<>();
        // Extract command, arguments, etc.
        return params;
    }
    
    // Utility methods
    public void logExecution(String message) {
        String logEntry = LocalDateTime.now() + ": " + message;
        executionLog.add(logEntry);
        System.out.println("FarTech AI Agent: " + logEntry);
    }
    
    public List<String> getExecutionLog() {
        return new ArrayList<>(executionLog);
    }
    
    public TaskStatus getTaskStatus() {
        return taskStatus;
    }
    
    public AgentTask getCurrentTask() {
        return currentTask;
    }
    
    public void interruptTask() {
        if (taskStatus == TaskStatus.EXECUTING || taskStatus == TaskStatus.PLANNING) {
            taskStatus = TaskStatus.INTERRUPTED;
            logExecution("Task interrupted by user");
        }
    }

    public ExecutionEngine getExecutionEngine() {
        return executionEngine;
    }
}
