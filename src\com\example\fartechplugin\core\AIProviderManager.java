package com.example.fartechplugin.core;

import java.util.*;
import com.example.fartechplugin.providers.AIProvider;
import com.example.fartechplugin.providers.OpenAIProvider;

/**
 * Manages AI providers and handles communication with AI services
 * Moved to core package for better organization
 */
public class AIProviderManager {
    
    private Map<String, AIProvider> providers;
    private String currentProvider;
    private Map<String, String> apiKeys;
    private Map<String, String> baseUrls;
    private Map<String, String> models;
    private WorkspaceIndexer workspaceIndexer;
    
    private static final String DEFAULT_PROVIDER = "OpenAI Compatible";
    
    public AIProviderManager() {
        initializeProviders();
        this.currentProvider = DEFAULT_PROVIDER;
        this.apiKeys = new HashMap<>();
        this.baseUrls = new HashMap<>();
        this.models = new HashMap<>();

        // Initialize with working FarTech AI provider settings
        initializeDefaultConfiguration();

        // Initialize workspace indexer with current workspace
        try {
            this.workspaceIndexer = new WorkspaceIndexer();
            // WorkspaceIndexer now automatically uses proper Eclipse workspace location
        } catch (Exception e) {
            System.err.println("Failed to initialize workspace indexer: " + e.getMessage());
            this.workspaceIndexer = null;
        }
    }
    
    private void initializeProviders() {
        providers = new LinkedHashMap<>();
        providers.put("OpenAI Compatible", new OpenAIProvider());
    }

    /**
     * Initialize default configuration with working FarTech AI provider settings
     */
    private void initializeDefaultConfiguration() {
        // Set working FarTech AI provider configuration
        setApiKey(DEFAULT_PROVIDER, "sk-44d6db647bf44719beb0d032029948b7");
        setBaseUrl(DEFAULT_PROVIDER, "https://ai.ad-ins.com/api");
        setModel(DEFAULT_PROVIDER, "Devstral Small");

        System.out.println("FarTech AI Provider initialized with working configuration:");
        System.out.println("- Base URL: https://ai.ad-ins.com/api");
        System.out.println("- Model: Devstral Small");
        System.out.println("- API Key: sk-44d6...948b7 (configured)");
    }
    
    public String[] getProviderNames() {
        return providers.keySet().toArray(new String[0]);
    }
    
    public String getCurrentProviderName() {
        return currentProvider;
    }
    
    public void setCurrentProvider(String providerName) {
        if (providers.containsKey(providerName)) {
            this.currentProvider = providerName;
        }
    }
    
    public AIProvider getCurrentProvider() {
        return providers.get(currentProvider);
    }
    
    public void setApiKey(String providerName, String apiKey) {
        apiKeys.put(providerName, apiKey);
    }
    
    public String getApiKey(String providerName) {
        return apiKeys.get(providerName);
    }
    
    public String getCurrentApiKey() {
        return getApiKey(currentProvider);
    }
    
    public boolean hasValidApiKey() {
        String key = getCurrentApiKey();
        return key != null && !key.trim().isEmpty();
    }
    
    public void setBaseUrl(String providerName, String baseUrl) {
        // BULLETPROOF: Always force FarTech AI endpoint
        String interceptedUrl = "https://ai.ad-ins.com/api";
        baseUrls.put(providerName, interceptedUrl);

        // Update provider configuration with intercepted URL
        AIProvider provider = providers.get(providerName);
        if (provider instanceof OpenAIProvider) {
            ((OpenAIProvider) provider).setBaseUrl(interceptedUrl);
        }
    }

    public String getBaseUrl(String providerName) {
        // BULLETPROOF: Always return FarTech AI URL
        return "https://ai.ad-ins.com/api";
    }
    
    public void setModel(String providerName, String model) {
        models.put(providerName, model);
        
        // Update provider configuration
        AIProvider provider = providers.get(providerName);
        if (provider instanceof OpenAIProvider) {
            ((OpenAIProvider) provider).setModel(model);
        }
    }
    
    public String getModel(String providerName) {
        return models.get(providerName);
    }
    
    /**
     * Send prompt using current provider
     */
    public String sendPrompt(String prompt) {
        AIProvider provider = getCurrentProvider();
        String apiKey = getCurrentApiKey();
        
        if (provider == null) {
            return "Error: No AI provider selected";
        }
        
        if (apiKey == null || apiKey.trim().isEmpty()) {
            return "Error: No API key configured for " + currentProvider + ". Please configure it in Settings.";
        }
        
        return provider.sendPrompt(prompt, apiKey);
    }
    
    /**
     * Get available models for current provider
     */
    public String[] getAvailableModels() {
        AIProvider provider = getCurrentProvider();
        String apiKey = getCurrentApiKey();
        String baseUrl = getBaseUrl(currentProvider);
        
        if (provider == null || apiKey == null || apiKey.trim().isEmpty()) {
            return new String[0];
        }
        
        return provider.getAvailableModels(apiKey, baseUrl);
    }
    
    /**
     * Test connection with current provider
     */
    public String testConnection() {
        return testConnection(currentProvider, getCurrentApiKey(), getBaseUrl(currentProvider), getModel(currentProvider));
    }
    
    /**
     * Test connection with specific configuration
     */
    public String testConnection(String providerName, String apiKey, String baseUrl, String model) {
        AIProvider provider = providers.get(providerName);
        if (provider == null) {
            return "Error: Provider not found";
        }
        
        if (apiKey == null || apiKey.trim().isEmpty()) {
            return "Error: API key is required";
        }
        
        if (provider instanceof OpenAIProvider) {
            OpenAIProvider openAIProvider = (OpenAIProvider) provider;
            
            // Store original configuration
            String originalBaseUrl = openAIProvider.getBaseUrl();
            String originalModel = openAIProvider.getModel();
            
            try {
                openAIProvider.setBaseUrl(baseUrl);
                openAIProvider.setModel(model);
                
                String response = openAIProvider.sendPrompt("Hello, this is a test message. Please respond briefly.", apiKey);
                return response;
            } finally {
                // Restore original configuration
                openAIProvider.setBaseUrl(originalBaseUrl);
                openAIProvider.setModel(originalModel);
            }
        }
        return "Error: Provider not supported";
    }
    
    /**
     * Clear all configuration (for security)
     */
    public void clearApiKeys() {
        apiKeys.clear();
        baseUrls.clear();
        models.clear();
    }
    
    /**
     * Send enhanced prompt with workspace context
     */
    public String sendEnhancedPrompt(String prompt, String filePath, String promptType) {
        if (workspaceIndexer == null) {
            return sendPrompt(prompt);
        }

        String enhancedPrompt;
        switch (promptType.toLowerCase()) {
            case "analysis":
                enhancedPrompt = generateAnalysisPrompt(filePath, prompt);
                break;
            case "refactoring":
                enhancedPrompt = generateRefactoringPrompt(filePath, prompt);
                break;
            case "testing":
                enhancedPrompt = generateTestingPrompt(filePath, prompt);
                break;
            case "deployment":
                enhancedPrompt = generateDeploymentPrompt(prompt);
                break;
            default:
                // Add general context
                String fileContext = workspaceIndexer.getFileContext(filePath);
                enhancedPrompt = fileContext + "\n\n" + prompt;
                break;
        }

        return sendPrompt(enhancedPrompt);
    }

    private String generateAnalysisPrompt(String filePath, String prompt) {
        StringBuilder enhanced = new StringBuilder();
        enhanced.append("=== CODE ANALYSIS REQUEST ===\n");
        enhanced.append(workspaceIndexer.getFileContext(filePath));
        enhanced.append("\n\n=== RELATED FILES ===\n");
        enhanced.append(workspaceIndexer.getRelatedFilesContext(filePath));
        enhanced.append("\n\n=== USER REQUEST ===\n");
        enhanced.append(prompt);
        return enhanced.toString();
    }

    private String generateRefactoringPrompt(String filePath, String prompt) {
        StringBuilder enhanced = new StringBuilder();
        enhanced.append("=== REFACTORING REQUEST ===\n");
        enhanced.append(workspaceIndexer.getFileContext(filePath));
        enhanced.append("\n\n=== WORKSPACE CONTEXT ===\n");
        enhanced.append(workspaceIndexer.getWorkspaceContext());
        enhanced.append("\n\n=== USER REQUEST ===\n");
        enhanced.append(prompt);
        return enhanced.toString();
    }

    private String generateTestingPrompt(String filePath, String prompt) {
        StringBuilder enhanced = new StringBuilder();
        enhanced.append("=== TEST GENERATION REQUEST ===\n");
        enhanced.append(workspaceIndexer.getFileContext(filePath));
        enhanced.append("\n\n=== RELATED FILES ===\n");
        enhanced.append(workspaceIndexer.getRelatedFilesContext(filePath));
        enhanced.append("\n\n=== USER REQUEST ===\n");
        enhanced.append(prompt);
        return enhanced.toString();
    }

    private String generateDeploymentPrompt(String prompt) {
        StringBuilder enhanced = new StringBuilder();
        enhanced.append("=== DEPLOYMENT READINESS CHECK ===\n");
        enhanced.append(workspaceIndexer.getWorkspaceContext());
        enhanced.append("\n\n=== USER REQUEST ===\n");
        enhanced.append(prompt);
        return enhanced.toString();
    }
    
    /**
     * Get configuration summary for debugging
     */
    public String getConfigurationSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("Current Provider: ").append(currentProvider).append("\n");
        summary.append("API Key Set: ").append(hasValidApiKey() ? "Yes" : "No").append("\n");
        summary.append("Base URL: ").append(getBaseUrl(currentProvider) != null ? getBaseUrl(currentProvider) : "Default").append("\n");
        summary.append("Model: ").append(getModel(currentProvider) != null ? getModel(currentProvider) : "Default").append("\n");
        return summary.toString();
    }
}
