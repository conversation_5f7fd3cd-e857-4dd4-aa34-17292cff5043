package com.example.fartechplugin.core;

import java.util.*;

/**
 * Response from the AI Agent
 */
public class AgentResponse {
    private final boolean success;
    private final String message;
    private final String result;
    private final String errorMessage;
    private final List<String> executionLog;
    private final boolean requiresUserApproval;
    private final Map<String, Object> metadata;
    
    private AgentResponse(boolean success, String message, String result, String errorMessage, 
                         List<String> executionLog, boolean requiresUserApproval, Map<String, Object> metadata) {
        this.success = success;
        this.message = message;
        this.result = result;
        this.errorMessage = errorMessage;
        this.executionLog = executionLog != null ? executionLog : new ArrayList<>();
        this.requiresUserApproval = requiresUserApproval;
        this.metadata = metadata != null ? metadata : new HashMap<>();
    }
    
    public static AgentResponse success(String message, String result) {
        return new AgentResponse(true, message, result, null, null, false, null);
    }
    
    public static AgentResponse error(String errorMessage) {
        return new AgentResponse(false, null, null, errorMessage, null, false, null);
    }
    
    public static AgentResponse requiresApproval(String message, String result, Map<String, Object> metadata) {
        return new AgentResponse(true, message, result, null, null, true, metadata);
    }
    
    // Getters
    public boolean isSuccess() { return success; }
    public String getMessage() { return message; }
    public String getResult() { return result; }
    public String getErrorMessage() { return errorMessage; }
    public List<String> getExecutionLog() { return new ArrayList<>(executionLog); }
    public boolean requiresUserApproval() { return requiresUserApproval; }
    public Map<String, Object> getMetadata() { return new HashMap<>(metadata); }
    
    public boolean requiresCorrection() {
        return !success && errorMessage != null;
    }
}
