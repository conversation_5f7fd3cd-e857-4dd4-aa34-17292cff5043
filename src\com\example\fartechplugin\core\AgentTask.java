package com.example.fartechplugin.core;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Represents an agent task with execution plan
 */
public class AgentTask {
    private final String taskId;
    private final UserIntent intent;
    private final IDEContext context;
    private ExecutionPlan executionPlan;
    private final LocalDateTime createdAt;
    private LocalDateTime completedAt;
    private TaskStatus status;
    
    public AgentTask(UserIntent intent, IDEContext context) {
        this.taskId = UUID.randomUUID().toString();
        this.intent = intent;
        this.context = context;
        this.createdAt = LocalDateTime.now();
        this.status = TaskStatus.PLANNING;
    }
    
    // Getters and setters
    public String getTaskId() { return taskId; }
    public UserIntent getIntent() { return intent; }
    public IDEContext getContext() { return context; }
    public ExecutionPlan getExecutionPlan() { return executionPlan; }
    public void setExecutionPlan(ExecutionPlan executionPlan) { this.executionPlan = executionPlan; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public LocalDateTime getCompletedAt() { return completedAt; }
    public void setCompletedAt(LocalDateTime completedAt) { this.completedAt = completedAt; }
    public TaskStatus getStatus() { return status; }
    public void setStatus(TaskStatus status) { this.status = status; }
}
