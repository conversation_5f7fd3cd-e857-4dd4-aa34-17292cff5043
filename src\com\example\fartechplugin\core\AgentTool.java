package com.example.fartechplugin.core;

import java.util.List;
import java.util.Map;

/**
 * Base interface for all agent tools
 */
public interface AgentTool {
    String getName();
    String getDescription();
    ToolCapability getCapability();
    ToolExecutionResult execute(Map<String, Object> parameters);
    boolean requiresUserApproval();
    List<String> getRequiredParameters();
    List<String> getOptionalParameters();
}
