package com.example.fartechplugin.core;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Core data types for the AI Agent system
 */

/**
 * Represents the current status of an agent task
 */
public enum TaskStatus {
    IDLE,
    PLANNING,
    EXECUTING,
    WAITING_FOR_APPROVAL,
    COMPLETED,
    FAILED,
    INTERRUPTED
}

/**
 * Types of user intents the agent can handle
 */
public enum IntentType {
    CREATE,        // Create new files, directories, code
    READ,          // Read and analyze existing code
    UPDATE,        // Modify existing files/code
    DELETE,        // Remove files, directories, code
    GIT_OPERATION, // Git commands (commit, push, etc.)
    EXECUTE,       // Run commands, tests, etc.
    GENERAL        // General queries and assistance
}

/**
 * Represents a user's intent parsed from their prompt
 */
public class UserIntent {
    private final IntentType type;
    private final String originalPrompt;
    private final String description;
    private final Map<String, Object> parameters;
    private final LocalDateTime timestamp;
    
    public UserIntent(IntentType type, String originalPrompt, Map<String, Object> parameters) {
        this.type = type;
        this.originalPrompt = originalPrompt;
        this.description = generateDescription();
        this.parameters = parameters != null ? parameters : new HashMap<>();
        this.timestamp = LocalDateTime.now();
    }
    
    private String generateDescription() {
        switch (type) {
            case CREATE: return "Create new content";
            case READ: return "Read and analyze content";
            case UPDATE: return "Update existing content";
            case DELETE: return "Delete content";
            case GIT_OPERATION: return "Perform Git operation";
            case EXECUTE: return "Execute command or test";
            default: return "General assistance";
        }
    }
    
    // Getters
    public IntentType getType() { return type; }
    public String getOriginalPrompt() { return originalPrompt; }
    public String getDescription() { return description; }
    public Map<String, Object> getParameters() { return new HashMap<>(parameters); }
    public LocalDateTime getTimestamp() { return timestamp; }
}

/**
 * Represents the current IDE context
 */
public class IDEContext {
    private String currentFile;
    private String selectedText;
    private String projectRoot;
    private List<String> openFiles;
    private String terminalOutput;
    private Map<String, Object> debuggerState;
    private String gitStatus;
    private Map<String, String> environmentVariables;
    
    public IDEContext() {
        this.openFiles = new ArrayList<>();
        this.debuggerState = new HashMap<>();
        this.environmentVariables = new HashMap<>();
    }
    
    // Getters and setters
    public String getCurrentFile() { return currentFile; }
    public void setCurrentFile(String currentFile) { this.currentFile = currentFile; }
    
    public String getSelectedText() { return selectedText; }
    public void setSelectedText(String selectedText) { this.selectedText = selectedText; }
    
    public String getProjectRoot() { return projectRoot; }
    public void setProjectRoot(String projectRoot) { this.projectRoot = projectRoot; }
    
    public List<String> getOpenFiles() { return new ArrayList<>(openFiles); }
    public void setOpenFiles(List<String> openFiles) { this.openFiles = new ArrayList<>(openFiles); }
    
    public String getTerminalOutput() { return terminalOutput; }
    public void setTerminalOutput(String terminalOutput) { this.terminalOutput = terminalOutput; }
    
    public Map<String, Object> getDebuggerState() { return new HashMap<>(debuggerState); }
    public void setDebuggerState(Map<String, Object> debuggerState) { this.debuggerState = new HashMap<>(debuggerState); }
    
    public String getGitStatus() { return gitStatus; }
    public void setGitStatus(String gitStatus) { this.gitStatus = gitStatus; }
    
    public Map<String, String> getEnvironmentVariables() { return new HashMap<>(environmentVariables); }
    public void setEnvironmentVariables(Map<String, String> environmentVariables) { this.environmentVariables = new HashMap<>(environmentVariables); }
}

/**
 * Represents an agent task with execution plan
 */
public class AgentTask {
    private final String taskId;
    private final UserIntent intent;
    private final IDEContext context;
    private ExecutionPlan executionPlan;
    private final LocalDateTime createdAt;
    private LocalDateTime completedAt;
    private TaskStatus status;
    
    public AgentTask(UserIntent intent, IDEContext context) {
        this.taskId = UUID.randomUUID().toString();
        this.intent = intent;
        this.context = context;
        this.createdAt = LocalDateTime.now();
        this.status = TaskStatus.PLANNING;
    }
    
    // Getters and setters
    public String getTaskId() { return taskId; }
    public UserIntent getIntent() { return intent; }
    public IDEContext getContext() { return context; }
    public ExecutionPlan getExecutionPlan() { return executionPlan; }
    public void setExecutionPlan(ExecutionPlan executionPlan) { this.executionPlan = executionPlan; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public LocalDateTime getCompletedAt() { return completedAt; }
    public void setCompletedAt(LocalDateTime completedAt) { this.completedAt = completedAt; }
    public TaskStatus getStatus() { return status; }
    public void setStatus(TaskStatus status) { this.status = status; }
}

/**
 * Represents a plan for executing a task
 */
public class ExecutionPlan {
    private final List<ExecutionStep> steps;
    private final Map<String, Object> metadata;
    private int currentStepIndex;
    
    public ExecutionPlan() {
        this.steps = new ArrayList<>();
        this.metadata = new HashMap<>();
        this.currentStepIndex = 0;
    }
    
    public void addStep(ExecutionStep step) {
        steps.add(step);
    }
    
    public List<ExecutionStep> getSteps() { return new ArrayList<>(steps); }
    public Map<String, Object> getMetadata() { return new HashMap<>(metadata); }
    public int getCurrentStepIndex() { return currentStepIndex; }
    public void setCurrentStepIndex(int index) { this.currentStepIndex = index; }
    
    public ExecutionStep getCurrentStep() {
        if (currentStepIndex < steps.size()) {
            return steps.get(currentStepIndex);
        }
        return null;
    }
    
    public boolean hasNextStep() {
        return currentStepIndex < steps.size() - 1;
    }
    
    public void nextStep() {
        if (hasNextStep()) {
            currentStepIndex++;
        }
    }
}

/**
 * Represents a single step in an execution plan
 */
public class ExecutionStep {
    private final String stepId;
    private final String description;
    private final String toolName;
    private final Map<String, Object> parameters;
    private final boolean requiresUserApproval;
    private StepStatus status;
    private String result;
    private String errorMessage;
    
    public ExecutionStep(String description, String toolName, Map<String, Object> parameters, boolean requiresUserApproval) {
        this.stepId = UUID.randomUUID().toString();
        this.description = description;
        this.toolName = toolName;
        this.parameters = parameters != null ? parameters : new HashMap<>();
        this.requiresUserApproval = requiresUserApproval;
        this.status = StepStatus.PENDING;
    }
    
    // Getters and setters
    public String getStepId() { return stepId; }
    public String getDescription() { return description; }
    public String getToolName() { return toolName; }
    public Map<String, Object> getParameters() { return new HashMap<>(parameters); }
    public boolean requiresUserApproval() { return requiresUserApproval; }
    public StepStatus getStatus() { return status; }
    public void setStatus(StepStatus status) { this.status = status; }
    public String getResult() { return result; }
    public void setResult(String result) { this.result = result; }
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
}

/**
 * Status of an execution step
 */
public enum StepStatus {
    PENDING,
    EXECUTING,
    WAITING_FOR_APPROVAL,
    COMPLETED,
    FAILED,
    SKIPPED
}

/**
 * Response from the AI Agent
 */
public class AgentResponse {
    private final boolean success;
    private final String message;
    private final String result;
    private final String errorMessage;
    private final List<String> executionLog;
    private final boolean requiresUserApproval;
    private final Map<String, Object> metadata;
    
    private AgentResponse(boolean success, String message, String result, String errorMessage, 
                         List<String> executionLog, boolean requiresUserApproval, Map<String, Object> metadata) {
        this.success = success;
        this.message = message;
        this.result = result;
        this.errorMessage = errorMessage;
        this.executionLog = executionLog != null ? executionLog : new ArrayList<>();
        this.requiresUserApproval = requiresUserApproval;
        this.metadata = metadata != null ? metadata : new HashMap<>();
    }
    
    public static AgentResponse success(String message, String result) {
        return new AgentResponse(true, message, result, null, null, false, null);
    }
    
    public static AgentResponse error(String errorMessage) {
        return new AgentResponse(false, null, null, errorMessage, null, false, null);
    }
    
    public static AgentResponse requiresApproval(String message, String result, Map<String, Object> metadata) {
        return new AgentResponse(true, message, result, null, null, true, metadata);
    }
    
    // Getters
    public boolean isSuccess() { return success; }
    public String getMessage() { return message; }
    public String getResult() { return result; }
    public String getErrorMessage() { return errorMessage; }
    public List<String> getExecutionLog() { return new ArrayList<>(executionLog); }
    public boolean requiresUserApproval() { return requiresUserApproval; }
    public Map<String, Object> getMetadata() { return new HashMap<>(metadata); }
    
    public boolean requiresCorrection() {
        return !success && errorMessage != null;
    }
}
