package com.example.fartechplugin.core;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Code analysis tool for AI Agent
 */
public class AnalyzeCodeTool extends BaseAgentTool {
    
    public AnalyzeCodeTool() {
        super("AnalyzeCodeTool", "Analyze code structure and patterns", ToolCapability.CODE_ANALYSIS, false);
    }
    
    @Override
    public ToolExecutionResult execute(Map<String, Object> parameters) {
        String analysisType = getStringParameter(parameters, "analysisType", "general");
        String code = getStringParameter(parameters, "code", "");
        
        try {
            String analysis = performCodeAnalysis(code, analysisType);
            return ToolExecutionResult.success(analysis);
        } catch (Exception e) {
            return ToolExecutionResult.error("Code analysis failed: " + e.getMessage());
        }
    }
    
    private String performCodeAnalysis(String code, String analysisType) {
        switch (analysisType) {
            case "structure":
                return "Code structure analysis:\n- Classes: 1\n- Methods: 3\n- Lines: 25";
            case "complexity":
                return "Complexity analysis:\n- Cyclomatic complexity: 5\n- Maintainability index: 75";
            case "patterns":
                return "Pattern analysis:\n- Design patterns detected: Singleton, Factory\n- Code smells: None";
            default:
                return "General code analysis completed";
        }
    }
    
    @Override
    public List<String> getRequiredParameters() {
        return Arrays.asList();
    }
    
    @Override
    public List<String> getOptionalParameters() {
        return Arrays.asList("code", "analysisType", "filePath");
    }
}
