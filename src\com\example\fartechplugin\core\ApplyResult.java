package com.example.fartechplugin.core;

/**
 * Result of applying a code suggestion
 */
public class ApplyResult {
    public final boolean success;
    public final String message;
    public final String filePath;
    public final Exception error;
    
    public ApplyResult(boolean success, String message) {
        this(success, message, null, null);
    }
    
    public ApplyResult(boolean success, String message, String filePath) {
        this(success, message, filePath, null);
    }
    
    public ApplyResult(boolean success, String message, String filePath, Exception error) {
        this.success = success;
        this.message = message;
        this.filePath = filePath;
        this.error = error;
    }
    
    @Override
    public String toString() {
        return String.format("ApplyResult[%s] %s%s", 
            success ? "SUCCESS" : "FAILED",
            message,
            filePath != null ? " (" + filePath + ")" : "");
    }
}
