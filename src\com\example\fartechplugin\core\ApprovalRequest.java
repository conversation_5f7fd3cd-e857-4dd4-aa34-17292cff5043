package com.example.fartechplugin.core;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * Approval Request
 */
public class ApprovalRequest {
    private String approvalId;
    private String description;
    private String proposedChanges;
    private Map<String, Object> metadata;
    private String diffView;
    private LocalDateTime timestamp;
    private LocalDateTime completedAt;
    private boolean approved;
    private String userFeedback;
    
    public ApprovalRequest(String description, String proposedChanges, Map<String, Object> metadata) {
        this.description = description;
        this.proposedChanges = proposedChanges;
        this.metadata = metadata != null ? metadata : new HashMap<>();
    }
    
    // Getters and setters
    public String getApprovalId() { return approvalId; }
    public void setApprovalId(String approvalId) { this.approvalId = approvalId; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getProposedChanges() { return proposedChanges; }
    public void setProposedChanges(String proposedChanges) { this.proposedChanges = proposedChanges; }
    
    public Map<String, Object> getMetadata() { return metadata; }
    public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    
    public String getDiffView() { return diffView; }
    public void setDiffView(String diffView) { this.diffView = diffView; }
    
    public LocalDateTime getTimestamp() { return timestamp; }
    public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
    
    public LocalDateTime getCompletedAt() { return completedAt; }
    public void setCompletedAt(LocalDateTime completedAt) { this.completedAt = completedAt; }
    
    public boolean isApproved() { return approved; }
    public void setApproved(boolean approved) { this.approved = approved; }
    
    public String getUserFeedback() { return userFeedback; }
    public void setUserFeedback(String userFeedback) { this.userFeedback = userFeedback; }
}
