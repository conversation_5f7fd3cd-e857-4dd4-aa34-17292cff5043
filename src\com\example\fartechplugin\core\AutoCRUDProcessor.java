package com.example.fartechplugin.core;

import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.Path;

import com.example.fartechplugin.FarTechView;
import com.example.fartechplugin.utils.EclipseCompatibility;

/**
 * Automatically processes CRUD operations when agent mode is enabled
 * Detects file operations in AI responses and executes them without user confirmation
 */
public class AutoCRUDProcessor {
    
    // Patterns for detecting file operations in AI responses
    private static final Pattern READ_REQUEST_PATTERN = Pattern.compile(
        "(?:read|show|display|examine|view|check|analyze)\\s+(?:the\\s+)?(?:file|content|code)\\s*:?\\s*([^\\n]+)",
        Pattern.CASE_INSENSITIVE
    );

    private static final Pattern FILE_COUNT_PATTERN = Pattern.compile(
        "(?:count|how\\s+many)\\s+files?",
        Pattern.CASE_INSENSITIVE
    );

    private static final Pattern FILE_LIST_PATTERN = Pattern.compile(
        "(?:list|show)\\s+(?:all\\s+)?files?",
        Pattern.CASE_INSENSITIVE
    );

    // Enhanced patterns for extracting file names from user messages
    private static final Pattern SPECIFIC_FILE_PATTERN = Pattern.compile(
        "(?:analyze|read|show|examine|view|check)\\s+(?:my\\s+)?(?:code\\s+in\\s+|file\\s+)?([\\w\\.\\-]+\\.java|[\\w\\.\\-]+\\.js|[\\w\\.\\-]+\\.py|[\\w\\.\\-]+\\.xml|[\\w\\.\\-]+\\.json|[\\w\\.\\-]+\\.txt|[\\w\\.\\-]+)",
        Pattern.CASE_INSENSITIVE
    );

    private static final Pattern JAVA_FILE_PATTERN = Pattern.compile(
        "([\\w\\.\\-]+)(?:\\.java)?",
        Pattern.CASE_INSENSITIVE
    );

    // Patterns for detecting file creation requests
    private static final Pattern CREATE_PROJECT_PATTERN = Pattern.compile(
        "(?:create|make|generate|setup|build)\\s+(?:a\\s+)?(?:simple\\s+|basic\\s+)?(?:java\\s+)?(?:project|structure)",
        Pattern.CASE_INSENSITIVE
    );

    private static final Pattern CREATE_SPRING_PATTERN = Pattern.compile(
        "(?:create|make|generate|setup|build)\\s+(?:a\\s+)?(?:basic\\s+|simple\\s+)?(?:java\\s+)?(?:project|app|application)\\s+(?:with\\s+)?(?:spring|spring\\s+boot|spring\\s+framework)",
        Pattern.CASE_INSENSITIVE
    );

    private static final Pattern CREATE_FILE_PATTERN = Pattern.compile(
        "(?:create|make|generate|write)\\s+(?:a\\s+)?(?:file|class|java\\s+file)",
        Pattern.CASE_INSENSITIVE
    );
    
    private final WorkspaceIndexer workspaceIndexer;
    private final FileOperationsAgent fileAgent;
    
    public AutoCRUDProcessor(WorkspaceIndexer workspaceIndexer, FileOperationsAgent fileAgent) {
        this.workspaceIndexer = workspaceIndexer;
        this.fileAgent = fileAgent;
    }
    
    /**
     * Process user message and automatically perform CRUD operations if agent mode is enabled
     */
    public String processUserMessage(String userMessage, String aiResponse, FarTechView view) {
        if (!view.isAgentMode()) {
            return aiResponse; // Not in agent mode, return original response
        }
        
        StringBuilder enhancedResponse = new StringBuilder();
        boolean operationPerformed = false;
        
        // Check for file reading requests
        if (detectsFileReadRequest(userMessage)) {
            String readResult = performAutomaticFileRead(userMessage, view);
            if (readResult != null) {
                enhancedResponse.append("[AGENT] **Agent Mode: Automatically reading files**\n\n");
                enhancedResponse.append(readResult);
                operationPerformed = true;
            }
        }
        
        // Check for file counting requests
        if (detectsFileCountRequest(userMessage)) {
            String countResult = performAutomaticFileCount(view);
            if (countResult != null) {
                enhancedResponse.append("[AGENT] **Agent Mode: Automatically counting files**\n\n");
                enhancedResponse.append(countResult);
                operationPerformed = true;
            }
        }
        
        // Check for file listing requests
        if (detectsFileListRequest(userMessage)) {
            String listResult = performAutomaticFileList(view);
            if (listResult != null) {
                enhancedResponse.append("[AGENT] **Agent Mode: Automatically listing files**\n\n");
                enhancedResponse.append(listResult);
                operationPerformed = true;
            }
        }

        // Check for file/project creation requests
        if (detectsFileCreationRequest(userMessage)) {
            String createResult = performAutomaticFileCreation(userMessage, view);
            if (createResult != null) {
                enhancedResponse.append("[AGENT] **Agent Mode: Automatically creating files**\n\n");
                enhancedResponse.append(createResult);
                operationPerformed = true;
            }
        }
        
        // If we performed operations, append the original AI response
        if (operationPerformed) {
            enhancedResponse.append("\n\n---\n\n");
            enhancedResponse.append("**AI Analysis:**\n");
            enhancedResponse.append(aiResponse);
            return enhancedResponse.toString();
        }
        
        return aiResponse; // No operations detected, return original
    }
    
    /**
     * Detect if user is requesting to read files
     */
    private boolean detectsFileReadRequest(String message) {
        String lowerMessage = message.toLowerCase();

        // Check for explicit file reading requests
        if (READ_REQUEST_PATTERN.matcher(message).find() ||
            lowerMessage.contains("read") && lowerMessage.contains("file") ||
            lowerMessage.contains("examine") && lowerMessage.contains("file") ||
            lowerMessage.contains("show") && lowerMessage.contains("content")) {
            return true;
        }

        // Check for analysis requests with specific file names
        if ((lowerMessage.contains("analyze") || lowerMessage.contains("analysis")) &&
            (lowerMessage.contains(".java") || lowerMessage.contains(".xml") ||
             lowerMessage.contains(".json") || lowerMessage.contains(".js") ||
             lowerMessage.contains(".py") || lowerMessage.contains(".txt"))) {
            return true;
        }

        // Check for "my code in [filename]" pattern
        if (lowerMessage.contains("my code") && lowerMessage.contains(" in ")) {
            return true;
        }

        return false;
    }

    /**
     * Detect if user is requesting to create files or projects
     */
    private boolean detectsFileCreationRequest(String message) {
        String lowerMessage = message.toLowerCase();

        // Check for Spring Framework project creation requests
        if (CREATE_SPRING_PATTERN.matcher(message).find()) {
            return true;
        }

        // Check for project creation requests
        if (CREATE_PROJECT_PATTERN.matcher(message).find()) {
            return true;
        }

        // Check for file creation requests
        if (CREATE_FILE_PATTERN.matcher(message).find()) {
            return true;
        }

        // Check for specific creation patterns
        if ((lowerMessage.contains("create") || lowerMessage.contains("make") || lowerMessage.contains("generate")) &&
            (lowerMessage.contains("project") || lowerMessage.contains("structure") ||
             lowerMessage.contains("java") || lowerMessage.contains("file"))) {
            return true;
        }

        // Check for Spring-specific patterns
        if ((lowerMessage.contains("create") || lowerMessage.contains("make") || lowerMessage.contains("generate")) &&
            (lowerMessage.contains("spring") || lowerMessage.contains("spring boot") || lowerMessage.contains("spring framework"))) {
            return true;
        }

        return false;
    }
    
    /**
     * Detect if user is requesting file count
     */
    private boolean detectsFileCountRequest(String message) {
        return FILE_COUNT_PATTERN.matcher(message).find() ||
               message.toLowerCase().contains("count") && message.toLowerCase().contains("file") ||
               message.toLowerCase().contains("how many") && message.toLowerCase().contains("file");
    }
    
    /**
     * Detect if user is requesting file list
     */
    private boolean detectsFileListRequest(String message) {
        return FILE_LIST_PATTERN.matcher(message).find() ||
               message.toLowerCase().contains("list") && message.toLowerCase().contains("file") ||
               message.toLowerCase().contains("show") && message.toLowerCase().contains("file");
    }

    /**
     * Automatically create files/projects based on user request
     */
    private String performAutomaticFileCreation(String userMessage, FarTechView view) {
        try {
            String lowerMessage = userMessage.toLowerCase();

            // Detect if user wants a Spring Framework project
            if ((lowerMessage.contains("spring") || lowerMessage.contains("spring boot") || lowerMessage.contains("spring framework")) &&
                (lowerMessage.contains("project") || lowerMessage.contains("app") || lowerMessage.contains("application"))) {
                return createSpringBootProjectStructure(userMessage, view);
            }

            // Detect if user wants a Java project structure
            if (lowerMessage.contains("java") &&
                (lowerMessage.contains("project") || lowerMessage.contains("structure"))) {
                return createJavaProjectStructure(view);
            }

            // Detect if user wants specific files
            if (lowerMessage.contains("file") || lowerMessage.contains("class")) {
                return createSpecificFiles(userMessage, view);
            }

            return null; // No creation pattern detected

        } catch (Exception e) {
            return "[ERROR] Error creating files: " + e.getMessage();
        }
    }

    /**
     * Create a standard Java project structure
     */
    private String createJavaProjectStructure(FarTechView view) {
        try {
            StringBuilder result = new StringBuilder();
            result.append("[PROJECT] **Creating Java Project Structure**\n\n");

            // Define the project structure
            String[] directories = {
                "my-java-project",
                "my-java-project/src",
                "my-java-project/src/main",
                "my-java-project/src/main/java",
                "my-java-project/src/main/java/com",
                "my-java-project/src/main/java/com/example",
                "my-java-project/src/main/java/com/example/myproject"
            };

            // Create directories
            for (String dir : directories) {
                boolean success = createDirectory(dir);
                if (success) {
                    result.append("[DIR] Created directory: `").append(dir).append("`\n");
                } else {
                    result.append("[WARNING] Directory may already exist: `").append(dir).append("`\n");
                }
            }

            result.append("\n");

            // Create main application file
            String appFilePath = "my-java-project/src/main/java/com/example/myproject/Application.java";
            String appContent = generateApplicationJavaContent();
            boolean appCreated = createFile(appFilePath, appContent);
            if (appCreated) {
                result.append("[FILE] Created: `").append(appFilePath).append("`\n");
            } else {
                result.append("[ERROR] Failed to create: `").append(appFilePath).append("`\n");
            }

            // Create pom.xml
            String pomFilePath = "my-java-project/pom.xml";
            String pomContent = generatePomXmlContent();
            boolean pomCreated = createFile(pomFilePath, pomContent);
            if (pomCreated) {
                result.append("[FILE] Created: `").append(pomFilePath).append("`\n");
            } else {
                result.append("[ERROR] Failed to create: `").append(pomFilePath).append("`\n");
            }

            // Create README.md
            String readmeFilePath = "my-java-project/README.md";
            String readmeContent = generateReadmeContent();
            boolean readmeCreated = createFile(readmeFilePath, readmeContent);
            if (readmeCreated) {
                result.append("[FILE] Created: `").append(readmeFilePath).append("`\n");
            } else {
                result.append("[ERROR] Failed to create: `").append(readmeFilePath).append("`\n");
            }

            result.append("\n[SUCCESS] Java project structure created successfully!\n");
            result.append("[INFO] Project location: `").append(getWorkspaceDirectory()).append("/my-java-project/`\n");
            result.append("[INFO] Main class: `com.example.myproject.Application`\n");
            result.append("[INFO] To run: `cd ").append(getWorkspaceDirectory()).append("/my-java-project && mvn compile exec:java`");

            return result.toString();

        } catch (Exception e) {
            return "[ERROR] Failed to create Java project structure: " + e.getMessage();
        }
    }

    /**
     * Create a Spring Boot project structure
     */
    private String createSpringBootProjectStructure(String userMessage, FarTechView view) {
        try {
            StringBuilder result = new StringBuilder();
            result.append("[PROJECT] **Creating Spring Boot Project Structure**\n\n");

            // Define the Spring Boot project structure
            String[] directories = {
                "my-spring-app",
                "my-spring-app/src",
                "my-spring-app/src/main",
                "my-spring-app/src/main/java",
                "my-spring-app/src/main/java/com",
                "my-spring-app/src/main/java/com/fartech",
                "my-spring-app/src/main/java/com/fartech/myapp",
                "my-spring-app/src/main/java/com/fartech/myapp/controller",
                "my-spring-app/src/main/resources",
                "my-spring-app/src/test",
                "my-spring-app/src/test/java"
            };

            // Create directories
            for (String dir : directories) {
                boolean success = createDirectory(dir);
                if (success) {
                    result.append("[DIR] Created directory: `").append(dir).append("`\n");
                } else {
                    result.append("[WARNING] Directory may already exist: `").append(dir).append("`\n");
                }
            }

            result.append("\n");

            // Create main Spring Boot application file
            String appFilePath = "my-spring-app/src/main/java/com/fartech/myapp/MySpringAppApplication.java";
            String appContent = generateSpringBootApplicationContent();
            boolean appCreated = createFile(appFilePath, appContent);
            if (appCreated) {
                result.append("[FILE] Created: `").append(appFilePath).append("`\n");
            } else {
                result.append("[ERROR] Failed to create: `").append(appFilePath).append("`\n");
            }

            // Create REST controller
            String controllerFilePath = "my-spring-app/src/main/java/com/fartech/myapp/controller/HelloController.java";
            String controllerContent = generateSpringBootControllerContent();
            boolean controllerCreated = createFile(controllerFilePath, controllerContent);
            if (controllerCreated) {
                result.append("[FILE] Created: `").append(controllerFilePath).append("`\n");
            } else {
                result.append("[ERROR] Failed to create: `").append(controllerFilePath).append("`\n");
            }

            // Create pom.xml for Spring Boot
            String pomFilePath = "my-spring-app/pom.xml";
            String pomContent = generateSpringBootPomXmlContent();
            boolean pomCreated = createFile(pomFilePath, pomContent);
            if (pomCreated) {
                result.append("[FILE] Created: `").append(pomFilePath).append("`\n");
            } else {
                result.append("[ERROR] Failed to create: `").append(pomFilePath).append("`\n");
            }

            // Create application.properties
            String propsFilePath = "my-spring-app/src/main/resources/application.properties";
            String propsContent = generateSpringBootPropertiesContent();
            boolean propsCreated = createFile(propsFilePath, propsContent);
            if (propsCreated) {
                result.append("[FILE] Created: `").append(propsFilePath).append("`\n");
            } else {
                result.append("[ERROR] Failed to create: `").append(propsFilePath).append("`\n");
            }

            // Create README.md
            String readmeFilePath = "my-spring-app/README.md";
            String readmeContent = generateSpringBootReadmeContent();
            boolean readmeCreated = createFile(readmeFilePath, readmeContent);
            if (readmeCreated) {
                result.append("[FILE] Created: `").append(readmeFilePath).append("`\n");
            } else {
                result.append("[ERROR] Failed to create: `").append(readmeFilePath).append("`\n");
            }

            result.append("\n[SUCCESS] Spring Boot project structure created successfully!\n");
            result.append("[INFO] Project location: `").append(getWorkspaceDirectory()).append("/my-spring-app/`\n");
            result.append("[INFO] Main class: `com.fartech.myapp.MySpringAppApplication`\n");
            result.append("[INFO] API endpoint: `http://localhost:8080/api/hello`\n");
            result.append("[INFO] To run: `cd ").append(getWorkspaceDirectory()).append("/my-spring-app && mvn spring-boot:run`");

            return result.toString();

        } catch (Exception e) {
            return "[ERROR] Failed to create Spring Boot project structure: " + e.getMessage();
        }
    }

    /**
     * Automatically read files based on user request
     */
    private String performAutomaticFileRead(String userMessage, FarTechView view) {
        try {
            if (workspaceIndexer == null || workspaceIndexer.getFileCount() == 0) {
                return "[ERROR] No workspace files available to read. Please index your workspace first.";
            }

            // Get all indexed files
            String[] indexedFiles = workspaceIndexer.getIndexedFiles();
            if (indexedFiles.length == 0) {
                return "[ERROR] No files found in workspace index.";
            }

            // Try to extract specific file name from user message
            String targetFileName = extractFileNameFromMessage(userMessage);
            String[] matchingFiles = findMatchingFiles(indexedFiles, targetFileName);

            StringBuilder result = new StringBuilder();

            // If we found specific matching files, read them
            if (matchingFiles.length > 0) {
                result.append("[AGENT] **Found matching files for: ").append(targetFileName != null ? targetFileName : "your request").append("**\n\n");

                // Read the first matching file (or all if few)
                int filesToRead = Math.min(matchingFiles.length, 3); // Limit to 3 files max
                for (int i = 0; i < filesToRead; i++) {
                    String filePath = matchingFiles[i];
                    result.append("[FILE] **Reading file ").append(i + 1).append("/").append(matchingFiles.length).append(":** `").append(filePath).append("`\n\n");

                    String content = readFileContent(filePath);
                    result.append("```java\n").append(content).append("\n```\n\n");
                }

                if (matchingFiles.length > filesToRead) {
                    result.append("[INFO] Found ").append(matchingFiles.length - filesToRead).append(" more matching files. Ask me to read specific ones if needed.\n\n");
                }

                result.append("[SUCCESS] File(s) read successfully!");
                return result.toString();
            }

            // If no specific file found, fall back to original behavior
            result.append("[FILES] **Available Files in Workspace:**\n\n");

            // If there's only one file, read it automatically
            if (indexedFiles.length == 1) {
                String filePath = indexedFiles[0];
                result.append("[FILE] **Reading the only file in workspace:** `").append(filePath).append("`\n\n");

                String content = readFileContent(filePath);
                result.append("```\n").append(content).append("\n```\n\n");
                result.append("[SUCCESS] File read successfully!");

                // Note: System message will be added by caller to avoid threading issues
                return result.toString();
            }
            
            // Multiple files - show list and read the first few
            result.append("Found ").append(indexedFiles.length).append(" files:\n\n");
            for (int i = 0; i < Math.min(indexedFiles.length, 5); i++) {
                result.append("• `").append(indexedFiles[i]).append("`\n");
            }
            
            if (indexedFiles.length > 5) {
                result.append("• ... and ").append(indexedFiles.length - 5).append(" more files\n");
            }
            
            // Read the first file as an example
            result.append("\n[FILE] **Reading first file as example:** `").append(indexedFiles[0]).append("`\n\n");
            String content = readFileContent(indexedFiles[0]);
            result.append("```\n").append(content).append("\n```\n\n");
            result.append("[SUCCESS] File read successfully! Ask me to read specific files if needed.");

            // Note: System message will be added by caller to avoid threading issues
            return result.toString();

        } catch (Exception e) {
            return "[ERROR] Error reading files: " + e.getMessage();
        }
    }
    
    /**
     * Automatically count files in workspace
     */
    private String performAutomaticFileCount(FarTechView view) {
        try {
            if (workspaceIndexer == null) {
                return "[ERROR] Workspace indexer not available.";
            }

            int fileCount = workspaceIndexer.getFileCount();
            int dirCount = workspaceIndexer.getDirectoryCount();

            StringBuilder result = new StringBuilder();
            result.append("[COUNT] **Workspace File Count:**\n\n");
            result.append("[FILES] **Files:** ").append(fileCount).append("\n");
            result.append("[DIRS] **Directories:** ").append(dirCount).append("\n");
            result.append("[TOTAL] **Total Items:** ").append(fileCount + dirCount).append("\n\n");

            if (fileCount == 0) {
                result.append("[WARNING] No files are currently indexed. Try running 'Index Workspace' first.");
            } else {
                result.append("[SUCCESS] Workspace successfully indexed and ready for AI operations!");
            }

            // Note: System message will be added by caller to avoid threading issues
            return result.toString();

        } catch (Exception e) {
            return "[ERROR] Error counting files: " + e.getMessage();
        }
    }
    
    /**
     * Automatically list files in workspace
     */
    private String performAutomaticFileList(FarTechView view) {
        try {
            if (workspaceIndexer == null || workspaceIndexer.getFileCount() == 0) {
                return "[ERROR] No workspace files available. Please index your workspace first.";
            }

            String[] indexedFiles = workspaceIndexer.getIndexedFiles();

            StringBuilder result = new StringBuilder();
            result.append("[FILES] **Complete File List:**\n\n");

            if (indexedFiles.length == 0) {
                result.append("[ERROR] No files found in workspace index.");
                return result.toString();
            }

            for (int i = 0; i < indexedFiles.length; i++) {
                result.append(String.format("%d. `%s`\n", i + 1, indexedFiles[i]));
            }

            result.append("\n[SUMMARY] **Summary:** ").append(indexedFiles.length).append(" files total\n");
            result.append("[SUCCESS] All files listed successfully!");

            // Note: System message will be added by caller to avoid threading issues
            return result.toString();

        } catch (Exception e) {
            return "[ERROR] Error listing files: " + e.getMessage();
        }
    }
    
    /**
     * Extract file name from user message using patterns
     */
    private String extractFileNameFromMessage(String message) {
        // Try to extract specific file name patterns
        java.util.regex.Matcher matcher = SPECIFIC_FILE_PATTERN.matcher(message);
        if (matcher.find()) {
            String fileName = matcher.group(1);
            if (fileName != null && !fileName.trim().isEmpty()) {
                return fileName.trim();
            }
        }

        // Look for common file patterns in the message
        String lowerMessage = message.toLowerCase();

        // Check for specific patterns like "in GenericSubmitTaskLogic.java"
        if (lowerMessage.contains(" in ") && lowerMessage.contains(".java")) {
            int inIndex = lowerMessage.lastIndexOf(" in ");
            String afterIn = message.substring(inIndex + 4).trim();
            String[] parts = afterIn.split("\\s+");
            if (parts.length > 0) {
                String candidate = parts[0].replaceAll("[,;:!?\"'\\s]", "");
                if (candidate.length() > 0) {
                    return candidate;
                }
            }
        }

        // Extract words that might be file names (containing dots or common extensions)
        String[] words = message.split("\\s+");
        for (String word : words) {
            // Remove common punctuation
            word = word.replaceAll("[,;:!?\"']", "");

            // Check if it looks like a file name
            if (word.contains(".") ||
                word.toLowerCase().endsWith("java") ||
                word.toLowerCase().endsWith("xml") ||
                word.toLowerCase().endsWith("json") ||
                word.toLowerCase().endsWith("txt") ||
                word.toLowerCase().endsWith("js") ||
                word.toLowerCase().endsWith("py")) {
                return word;
            }
        }

        return null;
    }

    /**
     * Find files that match the target file name (fuzzy matching)
     */
    private String[] findMatchingFiles(String[] allFiles, String targetFileName) {
        if (targetFileName == null || targetFileName.trim().isEmpty()) {
            return new String[0];
        }

        java.util.List<String> matches = new java.util.ArrayList<>();
        String target = targetFileName.toLowerCase();

        // Remove common extensions for better matching
        String targetBase = target.replaceAll("\\.(java|xml|json|txt|js|py)$", "");

        for (String filePath : allFiles) {
            String fileName = filePath.toLowerCase();
            String fileBase = fileName.replaceAll("\\.(java|xml|json|txt|js|py)$", "");

            // Exact match (highest priority)
            if (fileName.equals(target) || fileName.endsWith("/" + target)) {
                matches.add(0, filePath); // Add to beginning
                continue;
            }

            // Base name match (without extension)
            if (fileBase.endsWith(targetBase) || fileName.contains(targetBase)) {
                matches.add(filePath);
                continue;
            }

            // Partial match
            if (fileName.contains(target)) {
                matches.add(filePath);
                continue;
            }

            // Fuzzy match (check if target is contained in file name)
            String[] targetParts = targetBase.split("[\\-_\\.]");
            boolean allPartsMatch = true;
            for (String part : targetParts) {
                if (part.length() > 2 && !fileName.contains(part.toLowerCase())) {
                    allPartsMatch = false;
                    break;
                }
            }
            if (allPartsMatch && targetParts.length > 0) {
                matches.add(filePath);
            }
        }

        return matches.toArray(new String[0]);
    }

    /**
     * Read file content safely
     */
    private String readFileContent(String filePath) {
        try {
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                return "[Error: File not found: " + filePath + "]";
            }

            if (Files.isDirectory(path)) {
                return "[Error: Path is a directory, not a file: " + filePath + "]";
            }

            // Check file size (limit to 50KB for auto-read)
            long size = Files.size(path);
            if (size > 50 * 1024) {
                return "[File too large for auto-read (" + size + " bytes). Maximum size is 50KB]";
            }

            return new String(Files.readAllBytes(path));

        } catch (Exception e) {
            return "[Error reading file: " + e.getMessage() + "]";
        }
    }

    /**
     * Create specific files based on user request
     */
    private String createSpecificFiles(String userMessage, FarTechView view) {
        // For now, just return a simple message
        return "[INFO] Specific file creation not yet implemented. Use Java project structure creation instead.";
    }

    /**
     * Create a directory in the workspace
     */
    private boolean createDirectory(String dirPath) {
        try {
            // Get the workspace directory
            String workspaceDir = getWorkspaceDirectory();
            Path fullPath = Paths.get(workspaceDir, dirPath);

            if (!Files.exists(fullPath)) {
                Files.createDirectories(fullPath);
                return true;
            }
            return false; // Already exists
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Create a file with content in the workspace
     */
    private boolean createFile(String filePath, String content) {
        try {
            // Get the workspace directory
            String workspaceDir = getWorkspaceDirectory();
            Path fullPath = Paths.get(workspaceDir, filePath);

            // Create parent directories if they don't exist
            if (fullPath.getParent() != null) {
                Files.createDirectories(fullPath.getParent());
            }
            Files.write(fullPath, content.getBytes());
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Get the workspace directory
     */
    private String getWorkspaceDirectory() {
        // Try to get workspace directory from WorkspaceIndexer first
        if (workspaceIndexer != null) {
            String workspaceRoot = workspaceIndexer.getWorkspaceRoot();
            if (workspaceRoot != null && !workspaceRoot.isEmpty()) {
                return workspaceRoot;
            }
        }

        // Fallback to EclipseCompatibility
        String workspaceLocation = EclipseCompatibility.getWorkspaceLocation();
        if (workspaceLocation != null && !workspaceLocation.isEmpty()) {
            return workspaceLocation;
        }

        // Final fallback to current directory
        return System.getProperty("user.dir");
    }

    /**
     * Generate Application.java content
     */
    private String generateApplicationJavaContent() {
        return "package com.example.myproject;\n\n" +
               "/**\n" +
               " * Main application class for the simple Java project.\n" +
               " * This class serves as the entry point for the application.\n" +
               " */\n" +
               "public class Application {\n\n" +
               "    /**\n" +
               "     * The main method, which is the entry point of the Java application.\n" +
               "     *\n" +
               "     * @param args Command line arguments (not used in this simple example).\n" +
               "     */\n" +
               "    public static void main(String[] args) {\n" +
               "        System.out.println(\"Hello, FarTech AI! Your Java project is set up.\");\n" +
               "        System.out.println(\"Project created successfully!\");\n" +
               "    }\n" +
               "}\n";
    }

    /**
     * Generate pom.xml content
     */
    private String generatePomXmlContent() {
        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
               "<project xmlns=\"http://maven.apache.org/POM/4.0.0\"\n" +
               "         xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n" +
               "         xsi:schemaLocation=\"http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd\">\n" +
               "    <modelVersion>4.0.0</modelVersion>\n\n" +
               "    <groupId>com.example</groupId>\n" +
               "    <artifactId>my-java-project</artifactId>\n" +
               "    <version>1.0-SNAPSHOT</version>\n" +
               "    <packaging>jar</packaging>\n\n" +
               "    <properties>\n" +
               "        <maven.compiler.source>17</maven.compiler.source>\n" +
               "        <maven.compiler.target>17</maven.compiler.target>\n" +
               "        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>\n" +
               "    </properties>\n\n" +
               "    <build>\n" +
               "        <plugins>\n" +
               "            <plugin>\n" +
               "                <groupId>org.apache.maven.plugins</groupId>\n" +
               "                <artifactId>maven-jar-plugin</artifactId>\n" +
               "                <version>3.2.0</version>\n" +
               "                <configuration>\n" +
               "                    <archive>\n" +
               "                        <manifest>\n" +
               "                            <addClasspath>true</addClasspath>\n" +
               "                            <mainClass>com.example.myproject.Application</mainClass>\n" +
               "                        </manifest>\n" +
               "                    </archive>\n" +
               "                </configuration>\n" +
               "            </plugin>\n" +
               "        </plugins>\n" +
               "    </build>\n" +
               "</project>\n";
    }

    /**
     * Generate README.md content
     */
    private String generateReadmeContent() {
        return "# My Simple Java Project\n\n" +
               "This is a basic Java project created with FarTech AI.\n\n" +
               "## Project Structure\n\n" +
               "- `src/main/java/com/example/myproject/Application.java`: The main application entry point.\n" +
               "- `pom.xml`: Maven project configuration.\n\n" +
               "## How to Run (with Maven)\n\n" +
               "1. Navigate to the project root directory (where `pom.xml` is located).\n" +
               "2. Compile the project: `mvn compile`\n" +
               "3. Run the application: `mvn exec:java -Dexec.mainClass=\"com.example.myproject.Application\"`\n\n" +
               "Alternatively, you can build a runnable JAR:\n\n" +
               "1. Build the JAR: `mvn package`\n" +
               "2. Run the JAR: `java -jar target/my-java-project-1.0-SNAPSHOT.jar`\n\n" +
               "## Created by FarTech AI\n\n" +
               "This project was automatically generated by FarTech AI Eclipse Plugin.\n";
    }

    /**
     * Generate Spring Boot Application.java content
     */
    private String generateSpringBootApplicationContent() {
        return "package com.fartech.myapp;\n\n" +
               "import org.springframework.boot.SpringApplication;\n" +
               "import org.springframework.boot.autoconfigure.SpringBootApplication;\n\n" +
               "/**\n" +
               " * Main entry point for the MySpringApp Spring Boot application.\n" +
               " * This class uses the {@code @SpringBootApplication} annotation, which is a convenience annotation\n" +
               " * that adds:\n" +
               " * <ul>\n" +
               " *     <li>{@code @Configuration}: Tags the class as a source of bean definitions for the application context.</li>\n" +
               " *     <li>{@code @EnableAutoConfiguration}: Tells Spring Boot to start adding beans based on classpath settings, other beans, and various property settings.</li>\n" +
               " *     <li>{@code @ComponentScan}: Tells Spring to look for other components, configurations, and services in the 'com.fartech.myapp' package, allowing it to find controllers and other components.</li>\n" +
               " * </ul>\n" +
               " *\n" +
               " * This application will run on an embedded Tomcat server by default.\n" +
               " */\n" +
               "@SpringBootApplication\n" +
               "public class MySpringAppApplication {\n\n" +
               "    /**\n" +
               "     * The main method that starts the Spring Boot application.\n" +
               "     *\n" +
               "     * @param args Command line arguments passed to the application.\n" +
               "     */\n" +
               "    public static void main(String[] args) {\n" +
               "        SpringApplication.run(MySpringAppApplication.class, args);\n" +
               "        System.out.println(\"Spring Boot application started successfully!\");\n" +
               "        System.out.println(\"API available at: http://localhost:8080/api/hello\");\n" +
               "    }\n" +
               "}\n";
    }

    /**
     * Generate Spring Boot Controller content
     */
    private String generateSpringBootControllerContent() {
        return "package com.fartech.myapp.controller;\n\n" +
               "import org.springframework.web.bind.annotation.GetMapping;\n" +
               "import org.springframework.web.bind.annotation.RequestMapping;\n" +
               "import org.springframework.web.bind.annotation.RequestParam;\n" +
               "import org.springframework.web.bind.annotation.RestController;\n\n" +
               "/**\n" +
               " * REST Controller for handling greetings.\n" +
               " * This class demonstrates a basic API endpoint using Spring Web.\n" +
               " * The {@code @RestController} annotation combines {@code @Controller} and {@code @ResponseBody},\n" +
               " * which means every method returns a domain object instead of a view, and the domain object is\n" +
               " * converted directly into JSON/XML.\n" +
               " * The {@code @RequestMapping(\"/api\")} annotation maps all requests starting with \"/api\" to this controller.\n" +
               " */\n" +
               "@RestController\n" +
               "@RequestMapping(\"/api\")\n" +
               "public class HelloController {\n\n" +
               "    /**\n" +
               "     * Handles GET requests to \"/api/hello\".\n" +
               "     * This method returns a simple greeting message.\n" +
               "     * It can optionally take a 'name' parameter from the request.\n" +
               "     *\n" +
               "     * @param name The name to greet, defaults to \"World\" if not provided.\n" +
               "     * @return A greeting string.\n" +
               "     */\n" +
               "    @GetMapping(\"/hello\")\n" +
               "    public String hello(@RequestParam(value = \"name\", defaultValue = \"World\") String name) {\n" +
               "        return String.format(\"Hello, %s! Welcome to FarTech AI Spring Boot API.\", name);\n" +
               "    }\n" +
               "    \n" +
               "    /**\n" +
               "     * Handles GET requests to \"/api/status\".\n" +
               "     * This method returns the application status.\n" +
               "     *\n" +
               "     * @return A status message.\n" +
               "     */\n" +
               "    @GetMapping(\"/status\")\n" +
               "    public String status() {\n" +
               "        return \"FarTech AI Spring Boot application is running successfully!\";\n" +
               "    }\n" +
               "}\n";
    }

    /**
     * Generate Spring Boot pom.xml content
     */
    private String generateSpringBootPomXmlContent() {
        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
               "<project xmlns=\"http://maven.apache.org/POM/4.0.0\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"\n" +
               "         xsi:schemaLocation=\"http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd\">\n" +
               "    <modelVersion>4.0.0</modelVersion>\n" +
               "    <parent>\n" +
               "        <groupId>org.springframework.boot</groupId>\n" +
               "        <artifactId>spring-boot-starter-parent</artifactId>\n" +
               "        <version>3.2.5</version>\n" +
               "        <relativePath/>\n" +
               "    </parent>\n\n" +
               "    <groupId>com.fartech</groupId>\n" +
               "    <artifactId>my-spring-app</artifactId>\n" +
               "    <version>0.0.1-SNAPSHOT</version>\n" +
               "    <name>my-spring-app</name>\n" +
               "    <description>Basic Spring Boot project for API integration test</description>\n\n" +
               "    <properties>\n" +
               "        <java.version>17</java.version>\n" +
               "    </properties>\n\n" +
               "    <dependencies>\n" +
               "        <dependency>\n" +
               "            <groupId>org.springframework.boot</groupId>\n" +
               "            <artifactId>spring-boot-starter-web</artifactId>\n" +
               "        </dependency>\n" +
               "        <dependency>\n" +
               "            <groupId>org.springframework.boot</groupId>\n" +
               "            <artifactId>spring-boot-starter-test</artifactId>\n" +
               "            <scope>test</scope>\n" +
               "        </dependency>\n" +
               "    </dependencies>\n\n" +
               "    <build>\n" +
               "        <plugins>\n" +
               "            <plugin>\n" +
               "                <groupId>org.springframework.boot</groupId>\n" +
               "                <artifactId>spring-boot-maven-plugin</artifactId>\n" +
               "            </plugin>\n" +
               "        </plugins>\n" +
               "    </build>\n" +
               "</project>\n";
    }

    /**
     * Generate Spring Boot application.properties content
     */
    private String generateSpringBootPropertiesContent() {
        return "# Spring Boot Application Configuration\n" +
               "# Created by FarTech AI\n\n" +
               "server.port=8080\n" +
               "spring.application.name=my-spring-app\n" +
               "logging.level.com.fartech.myapp=INFO\n";
    }

    /**
     * Generate Spring Boot README.md content
     */
    private String generateSpringBootReadmeContent() {
        return "# My Spring Boot Application\n\n" +
               "This is a basic Spring Boot project created with FarTech AI for API integration testing.\n\n" +
               "## API Endpoints\n\n" +
               "- GET `/api/hello` - Returns a greeting\n" +
               "- GET `/api/hello?name=John` - Returns personalized greeting\n" +
               "- GET `/api/status` - Returns application status\n\n" +
               "## How to Run\n\n" +
               "```bash\n" +
               "cd my-spring-app\n" +
               "mvn spring-boot:run\n" +
               "```\n\n" +
               "Application will be available at: http://localhost:8080\n\n" +
               "## Created by FarTech AI\n\n" +
               "This project was automatically generated by FarTech AI Eclipse Plugin.\n";
    }
}
