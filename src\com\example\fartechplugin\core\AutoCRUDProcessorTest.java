package com.example.fartechplugin.core;

/**
 * Simple test class to verify AutoCRUDProcessor functionality
 * This helps ensure the automatic CRUD operations work correctly
 */
public class AutoCRUDProcessorTest {
    
    /**
     * Test the pattern detection methods
     */
    public static void testPatternDetection() {
        System.out.println("=== AutoCRUDProcessor Pattern Detection Test ===");
        
        // Create a mock workspace indexer for testing
        WorkspaceIndexer mockIndexer = new WorkspaceIndexer();
        FileOperationsAgent mockAgent = new FileOperationsAgent();
        AutoCRUDProcessor processor = new AutoCRUDProcessor(mockIndexer, mockAgent);
        
        // Test cases for file reading detection
        String[] readTestCases = {
            "can you read my file in my workspace?",
            "show me the content of the file",
            "examine the file please",
            "display the file content",
            "view my code files",
            "check the file"
        };
        
        System.out.println("\n--- File Read Detection Tests ---");
        for (String testCase : readTestCases) {
            boolean detected = testDetectsFileReadRequest(processor, testCase);
            System.out.println("\"" + testCase + "\" -> " + (detected ? "DETECTED" : "NOT DETECTED"));
        }
        
        // Test cases for file counting detection
        String[] countTestCases = {
            "how many files in my project?",
            "count the files",
            "how many files do I have?",
            "file count please",
            "show file statistics"
        };
        
        System.out.println("\n--- File Count Detection Tests ---");
        for (String testCase : countTestCases) {
            boolean detected = testDetectsFileCountRequest(processor, testCase);
            System.out.println("\"" + testCase + "\" -> " + (detected ? "DETECTED" : "NOT DETECTED"));
        }
        
        // Test cases for file listing detection
        String[] listTestCases = {
            "list all files",
            "show me all files",
            "list files in workspace",
            "show file list",
            "display all files"
        };
        
        System.out.println("\n--- File List Detection Tests ---");
        for (String testCase : listTestCases) {
            boolean detected = testDetectsFileListRequest(processor, testCase);
            System.out.println("\"" + testCase + "\" -> " + (detected ? "DETECTED" : "NOT DETECTED"));
        }
        
        System.out.println("\n=== Test Complete ===");
    }
    
    /**
     * Test file read request detection using reflection
     */
    private static boolean testDetectsFileReadRequest(AutoCRUDProcessor processor, String message) {
        try {
            java.lang.reflect.Method method = AutoCRUDProcessor.class.getDeclaredMethod("detectsFileReadRequest", String.class);
            method.setAccessible(true);
            return (Boolean) method.invoke(processor, message);
        } catch (Exception e) {
            System.err.println("Error testing file read detection: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Test file count request detection using reflection
     */
    private static boolean testDetectsFileCountRequest(AutoCRUDProcessor processor, String message) {
        try {
            java.lang.reflect.Method method = AutoCRUDProcessor.class.getDeclaredMethod("detectsFileCountRequest", String.class);
            method.setAccessible(true);
            return (Boolean) method.invoke(processor, message);
        } catch (Exception e) {
            System.err.println("Error testing file count detection: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Test file list request detection using reflection
     */
    private static boolean testDetectsFileListRequest(AutoCRUDProcessor processor, String message) {
        try {
            java.lang.reflect.Method method = AutoCRUDProcessor.class.getDeclaredMethod("detectsFileListRequest", String.class);
            method.setAccessible(true);
            return (Boolean) method.invoke(processor, message);
        } catch (Exception e) {
            System.err.println("Error testing file list detection: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Main method for running tests
     */
    public static void main(String[] args) {
        testPatternDetection();
    }
}
