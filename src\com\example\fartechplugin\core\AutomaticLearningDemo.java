package com.example.fartechplugin.core;

/**
 * Demonstration of the Automatic Learning System for FarTech AI
 * Shows how the AI learns from user interactions automatically
 */
public class AutomaticLearningDemo {
    
    public static void main(String[] args) {
        System.out.println("=== FarTech AI Automatic Learning System Demo ===\n");
        
        // Initialize the learning system
        MemoryManager memoryManager = new MemoryManager();
        AutomaticLearningEngine learningEngine = new AutomaticLearningEngine(memoryManager);
        
        System.out.println("🧠 Learning Engine Initialized\n");
        
        // Simulate user interactions
        simulateUserInteractions(learningEngine);
        
        // Show learning results
        showLearningResults(learningEngine);
    }
    
    private static void simulateUserInteractions(AutomaticLearningEngine learningEngine) {
        System.out.println("📚 Simulating User Interactions...\n");
        
        // Interaction 1: User creates a Java class
        System.out.println("1. User: 'Create a Java service class for user management'");
        String response1 = "Here's a UserService class:\n```java\npublic class UserService {\n    // Implementation\n}\n```";
        learningEngine.learnFromInteraction("MyProject", 
            "Create a Java service class for user management", 
            response1, 
            "Perfect! Exactly what I needed.", 
            1500);
        System.out.println("   AI Response: [UserService class created]");
        System.out.println("   Follow-up: 'Perfect! Exactly what I needed.'");
        System.out.println("   ✅ Learning: Positive feedback detected\n");
        
        // Interaction 2: User asks for documentation
        System.out.println("2. User: 'Add JavaDoc comments to the UserService class'");
        String response2 = "I'll add comprehensive JavaDoc comments:\n```java\n/**\n * Service for user management\n */\npublic class UserService {\n}\n```";
        learningEngine.learnFromInteraction("MyProject", 
            "Add JavaDoc comments to the UserService class", 
            response2, 
            "Great! I like detailed documentation.", 
            2000);
        System.out.println("   AI Response: [JavaDoc comments added]");
        System.out.println("   Follow-up: 'Great! I like detailed documentation.'");
        System.out.println("   ✅ Learning: User prefers detailed documentation\n");
        
        // Interaction 3: User requests error handling
        System.out.println("3. User: 'Add error handling to the user creation method'");
        String response3 = "Here's the method with error handling:\n```java\npublic void createUser(User user) throws UserException {\n    // Implementation with try-catch\n}\n```";
        learningEngine.learnFromInteraction("MyProject", 
            "Add error handling to the user creation method", 
            response3, 
            "Good, but I prefer runtime exceptions.", 
            1800);
        System.out.println("   AI Response: [Error handling with checked exceptions]");
        System.out.println("   Follow-up: 'Good, but I prefer runtime exceptions.'");
        System.out.println("   ✅ Learning: User prefers runtime exceptions\n");
        
        // Interaction 4: User asks for tests
        System.out.println("4. User: 'Create unit tests for UserService'");
        String response4 = "Here are comprehensive unit tests:\n```java\n@Test\npublic void testCreateUser() {\n    // Test implementation\n}\n```";
        learningEngine.learnFromInteraction("MyProject", 
            "Create unit tests for UserService", 
            response4, 
            "Excellent! I love thorough testing.", 
            2200);
        System.out.println("   AI Response: [Unit tests created]");
        System.out.println("   Follow-up: 'Excellent! I love thorough testing.'");
        System.out.println("   ✅ Learning: User values comprehensive testing\n");
        
        // Interaction 5: User requests refactoring
        System.out.println("5. User: 'Refactor the UserService to use dependency injection'");
        String response5 = "Here's the refactored service with DI:\n```java\n@Service\npublic class UserService {\n    @Autowired\n    private UserRepository userRepository;\n}\n```";
        learningEngine.learnFromInteraction("MyProject", 
            "Refactor the UserService to use dependency injection", 
            response5, 
            "Perfect! I always use Spring annotations.", 
            1600);
        System.out.println("   AI Response: [Dependency injection added]");
        System.out.println("   Follow-up: 'Perfect! I always use Spring annotations.'");
        System.out.println("   ✅ Learning: User prefers Spring framework patterns\n");
    }
    
    private static void showLearningResults(AutomaticLearningEngine learningEngine) {
        System.out.println("🎯 Learning Results:\n");
        
        // Get learning statistics
        LearningStatistics stats = learningEngine.getLearningStats();
        System.out.println(stats.toString());
        
        System.out.println("\n🧠 What the AI has learned about the user:\n");
        
        System.out.println("📋 CODING PREFERENCES:");
        System.out.println("• Prefers detailed JavaDoc documentation");
        System.out.println("• Likes runtime exceptions over checked exceptions");
        System.out.println("• Values comprehensive unit testing");
        System.out.println("• Uses Spring framework patterns");
        System.out.println("• Appreciates clean, well-structured code");
        
        System.out.println("\n🎯 BEHAVIORAL PATTERNS:");
        System.out.println("• Provides clear, positive feedback");
        System.out.println("• Requests specific improvements");
        System.out.println("• Follows consistent coding standards");
        System.out.println("• Prefers enterprise Java patterns");
        
        System.out.println("\n🚀 ADAPTIVE RESPONSES:");
        System.out.println("• Future Java code will include detailed JavaDoc");
        System.out.println("• Error handling will use runtime exceptions");
        System.out.println("• Test suggestions will be comprehensive");
        System.out.println("• Spring annotations will be preferred");
        
        System.out.println("\n💡 NEXT INTERACTION PREDICTIONS:");
        System.out.println("• If user asks for a new service class:");
        System.out.println("  → AI will automatically include JavaDoc");
        System.out.println("  → AI will use Spring annotations");
        System.out.println("  → AI will suggest comprehensive tests");
        System.out.println("  → AI will use runtime exceptions");
        
        System.out.println("\n✨ This is how FarTech AI learns automatically from your interactions!");
        System.out.println("   No explicit training needed - just natural conversation! 🎉");
    }
    
    /**
     * Demonstrate adaptive prompt generation
     */
    public static String demonstrateAdaptivePrompt(AutomaticLearningEngine learningEngine) {
        String originalPrompt = "Create a new service class for order management";
        String adaptedPrompt = learningEngine.getAdaptivePrompt(originalPrompt, "MyProject", "java");
        
        System.out.println("📝 ADAPTIVE PROMPT DEMONSTRATION:\n");
        System.out.println("Original Prompt:");
        System.out.println(originalPrompt);
        System.out.println("\nAdapted Prompt (with learned preferences):");
        System.out.println(adaptedPrompt);
        
        return adaptedPrompt;
    }
}
