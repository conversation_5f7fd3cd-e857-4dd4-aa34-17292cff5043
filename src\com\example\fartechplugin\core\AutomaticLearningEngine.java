package com.example.fartechplugin.core;

import java.time.LocalDateTime;
import java.time.Duration;
import java.util.*;
import java.util.regex.Pattern;

/**
 * Enhanced Automatic Learning Engine for FarTech AI
 * Learns from user interactions, behaviors, and implicit feedback automatically
 */
public class AutomaticLearningEngine {
    
    private final MemoryManager memoryManager;
    private final Map<String, UserBehaviorPattern> behaviorPatterns;
    private final List<InteractionSession> recentSessions;
    private final FeedbackDetector feedbackDetector;
    private final PatternRecognizer patternRecognizer;
    
    // Learning configuration
    private static final int MAX_RECENT_SESSIONS = 50;
    private static final double POSITIVE_FEEDBACK_THRESHOLD = 0.7;
    private static final double NEGATIVE_FEEDBACK_THRESHOLD = 0.3;
    
    public AutomaticLearningEngine(MemoryManager memoryManager) {
        this.memoryManager = memoryManager;
        this.behaviorPatterns = new HashMap<>();
        this.recentSessions = new ArrayList<>();
        this.feedbackDetector = new FeedbackDetector();
        this.patternRecognizer = new PatternRecognizer();
    }
    
    /**
     * Learn from user interaction automatically
     */
    public void learnFromInteraction(String projectName, String userInput, String aiResponse, 
                                   String followUpInput, long responseTime) {
        
        // Create interaction session
        InteractionSession session = new InteractionSession(
            projectName, userInput, aiResponse, followUpInput, responseTime
        );
        
        // Detect implicit feedback from user's follow-up
        FeedbackSignal feedback = feedbackDetector.detectFeedback(userInput, aiResponse, followUpInput);
        session.setFeedbackSignal(feedback);
        
        // Learn from this interaction
        learnFromSession(session);
        
        // Store session for pattern analysis
        addSession(session);
        
        // Update behavior patterns
        updateBehaviorPatterns(session);
        
        // Trigger memory learning
        memoryManager.learnFromInteraction(
            projectName, 
            extractFileType(userInput), 
            userInput, 
            aiResponse, 
            feedback.getType().getValue()
        );
        
        System.out.println("FarTech AI: Learned from interaction - " + feedback.getType());
    }
    
    /**
     * Learn from user behavior patterns
     */
    public void learnFromBehavior(String userId, String actionType, String context, boolean successful) {
        String patternKey = userId + "_" + actionType;
        UserBehaviorPattern pattern = behaviorPatterns.getOrDefault(
            patternKey, new UserBehaviorPattern(userId, actionType)
        );
        
        pattern.addAction(context, successful);
        behaviorPatterns.put(patternKey, pattern);
        
        // If pattern shows consistent success/failure, update preferences
        if (pattern.getActionCount() >= 5) {
            double successRate = pattern.getSuccessRate();
            if (successRate > 0.8) {
                // User likes this pattern
                updateUserPreferences(actionType, context, true);
            } else if (successRate < 0.3) {
                // User dislikes this pattern
                updateUserPreferences(actionType, context, false);
            }
        }
    }
    
    /**
     * Get adaptive prompt based on learned patterns
     */
    public String getAdaptivePrompt(String originalPrompt, String projectName, String context) {
        StringBuilder adaptedPrompt = new StringBuilder();
        
        // Add learned user preferences
        UserPreferences prefs = memoryManager.getRelevantMemories(projectName, "java", originalPrompt).userPreferences;
        if (prefs != null) {
            adaptedPrompt.append("User Preferences:\n");
            adaptedPrompt.append("- Coding Style: ").append(prefs.codingStyle).append("\n");
            adaptedPrompt.append("- Verbose Explanations: ").append(prefs.verboseExplanations ? "Yes" : "No").append("\n");
            adaptedPrompt.append("- Include Comments: ").append(prefs.includeComments ? "Yes" : "No").append("\n");
            adaptedPrompt.append("\n");
        }
        
        // Add behavioral insights
        String behaviorInsights = getBehaviorInsights(context);
        if (!behaviorInsights.isEmpty()) {
            adaptedPrompt.append("Behavioral Insights:\n");
            adaptedPrompt.append(behaviorInsights).append("\n\n");
        }
        
        // Add pattern-based suggestions
        String patternSuggestions = getPatternSuggestions(originalPrompt, projectName);
        if (!patternSuggestions.isEmpty()) {
            adaptedPrompt.append("Pattern-Based Suggestions:\n");
            adaptedPrompt.append(patternSuggestions).append("\n\n");
        }
        
        adaptedPrompt.append("Original Request:\n");
        adaptedPrompt.append(originalPrompt);
        
        return adaptedPrompt.toString();
    }
    
    /**
     * Get learning statistics
     */
    public LearningStatistics getLearningStats() {
        LearningStatistics stats = new LearningStatistics();
        stats.totalSessions = recentSessions.size();
        stats.behaviorPatterns = behaviorPatterns.size();
        stats.memoryStats = memoryManager.getMemoryStats();

        // Calculate feedback distribution (Java 8 compatible)
        Map<FeedbackType, Integer> feedbackCounts = new HashMap<>();
        for (InteractionSession session : recentSessions) {
            FeedbackType type = session.getFeedbackSignal().getType();
            feedbackCounts.put(type, feedbackCounts.getOrDefault(type, 0) + 1);
        }

        if (recentSessions.size() > 0) {
            stats.positiveFeedbackRate = feedbackCounts.getOrDefault(FeedbackType.POSITIVE, 0) / (double) recentSessions.size();
            stats.negativeFeedbackRate = feedbackCounts.getOrDefault(FeedbackType.NEGATIVE, 0) / (double) recentSessions.size();
        } else {
            stats.positiveFeedbackRate = 0.0;
            stats.negativeFeedbackRate = 0.0;
        }

        return stats;
    }
    
    // Private helper methods
    
    private void learnFromSession(InteractionSession session) {
        FeedbackSignal feedback = session.getFeedbackSignal();
        
        // Learn from response time (user satisfaction indicator)
        if (session.getResponseTime() < 2000) { // Fast response
            feedback.addSignal("quick_response", 0.2);
        } else if (session.getResponseTime() > 10000) { // Slow response
            feedback.addSignal("slow_response", -0.1);
        }
        
        // Learn from follow-up patterns
        if (session.getFollowUpInput() != null) {
            String followUp = session.getFollowUpInput().toLowerCase();
            
            if (followUp.contains("perfect") || followUp.contains("exactly") || followUp.contains("great")) {
                feedback.addSignal("explicit_positive", 0.5);
            } else if (followUp.contains("wrong") || followUp.contains("not what") || followUp.contains("try again")) {
                feedback.addSignal("explicit_negative", -0.5);
            } else if (followUp.contains("but") || followUp.contains("however") || followUp.contains("actually")) {
                feedback.addSignal("correction_needed", -0.2);
            }
        }
    }
    
    private void addSession(InteractionSession session) {
        recentSessions.add(session);
        
        // Keep only recent sessions
        if (recentSessions.size() > MAX_RECENT_SESSIONS) {
            recentSessions.remove(0);
        }
    }
    
    private void updateBehaviorPatterns(InteractionSession session) {
        String actionType = extractActionType(session.getUserInput());
        String context = session.getProjectName() + "_" + actionType;
        
        boolean successful = session.getFeedbackSignal().getOverallScore() > POSITIVE_FEEDBACK_THRESHOLD;
        learnFromBehavior("default_user", actionType, context, successful);
    }
    
    private void updateUserPreferences(String actionType, String context, boolean positive) {
        // This would update user preferences based on successful/unsuccessful patterns
        // For now, we'll use the existing memory manager
        String feedback = positive ? "good pattern" : "avoid this pattern";
        memoryManager.updateUserPreferences(feedback, actionType, Arrays.asList(context));
    }
    
    private String extractFileType(String userInput) {
        if (userInput.toLowerCase().contains(".java")) return "java";
        if (userInput.toLowerCase().contains(".js")) return "javascript";
        if (userInput.toLowerCase().contains(".py")) return "python";
        if (userInput.toLowerCase().contains(".xml")) return "xml";
        return "general";
    }
    
    private String extractActionType(String userInput) {
        String lower = userInput.toLowerCase();
        if (lower.contains("create") || lower.contains("generate")) return "creation";
        if (lower.contains("read") || lower.contains("show") || lower.contains("display")) return "reading";
        if (lower.contains("update") || lower.contains("modify") || lower.contains("change")) return "updating";
        if (lower.contains("delete") || lower.contains("remove")) return "deletion";
        if (lower.contains("explain") || lower.contains("analyze")) return "analysis";
        if (lower.contains("test") || lower.contains("junit")) return "testing";
        return "general";
    }
    
    private String getBehaviorInsights(String context) {
        StringBuilder insights = new StringBuilder();
        
        for (UserBehaviorPattern pattern : behaviorPatterns.values()) {
            if (pattern.getActionCount() >= 3) {
                double successRate = pattern.getSuccessRate();
                if (successRate > 0.8) {
                    insights.append("- User prefers ").append(pattern.getActionType()).append(" operations\n");
                } else if (successRate < 0.3) {
                    insights.append("- User has difficulty with ").append(pattern.getActionType()).append(" operations\n");
                }
            }
        }
        
        return insights.toString();
    }
    
    private String getPatternSuggestions(String prompt, String projectName) {
        // Use pattern recognizer to suggest improvements
        return patternRecognizer.getSuggestions(prompt, projectName);
    }
}
