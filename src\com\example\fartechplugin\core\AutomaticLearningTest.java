package com.example.fartechplugin.core;

/**
 * Test class to verify the Automatic Learning System functionality
 */
public class AutomaticLearningTest {
    
    public static void main(String[] args) {
        System.out.println("=== FarTech AI Automatic Learning System Test ===\n");
        
        try {
            // Test 1: Initialize learning system
            testInitialization();
            
            // Test 2: Test feedback detection
            testFeedbackDetection();
            
            // Test 3: Test learning from interactions
            testLearningFromInteractions();
            
            // Test 4: Test adaptive prompts
            testAdaptivePrompts();
            
            System.out.println("✅ All tests passed! Automatic learning system is working correctly.\n");
            
        } catch (Exception e) {
            System.err.println("❌ Test failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void testInitialization() {
        System.out.println("🧪 Test 1: System Initialization");
        
        MemoryManager memoryManager = new MemoryManager();
        AutomaticLearningEngine learningEngine = new AutomaticLearningEngine(memoryManager);
        
        LearningStatistics stats = learningEngine.getLearningStats();
        
        System.out.println("   ✓ Memory manager created");
        System.out.println("   ✓ Learning engine initialized");
        System.out.println("   ✓ Learning stats accessible");
        System.out.println("   Initial stats: " + stats.totalSessions + " sessions\n");
    }
    
    private static void testFeedbackDetection() {
        System.out.println("🧪 Test 2: Feedback Detection");
        
        FeedbackDetector detector = new FeedbackDetector();
        
        // Test positive feedback
        FeedbackSignal positiveSignal = detector.detectFeedback(
            "Create a Java class", 
            "Here's your class...", 
            "Perfect! Exactly what I needed."
        );
        
        // Test negative feedback
        FeedbackSignal negativeSignal = detector.detectFeedback(
            "Add error handling", 
            "Here's error handling...", 
            "Wrong approach, I prefer runtime exceptions."
        );
        
        // Test neutral feedback
        FeedbackSignal neutralSignal = detector.detectFeedback(
            "Show me the file", 
            "Here's the file content...", 
            "Now add some comments to it."
        );
        
        System.out.println("   ✓ Positive feedback detected: " + positiveSignal.getType());
        System.out.println("   ✓ Negative feedback detected: " + negativeSignal.getType());
        System.out.println("   ✓ Neutral feedback detected: " + neutralSignal.getType());
        System.out.println("   Positive score: " + positiveSignal.getOverallScore());
        System.out.println("   Negative score: " + negativeSignal.getOverallScore() + "\n");
    }
    
    private static void testLearningFromInteractions() {
        System.out.println("🧪 Test 3: Learning from Interactions");
        
        MemoryManager memoryManager = new MemoryManager();
        AutomaticLearningEngine learningEngine = new AutomaticLearningEngine(memoryManager);
        
        // Simulate multiple interactions
        learningEngine.learnFromInteraction("TestProject", 
            "Create a service class", 
            "Here's a UserService class...", 
            "Great! I love detailed JavaDoc.", 
            1500);
            
        learningEngine.learnFromInteraction("TestProject", 
            "Add error handling", 
            "Added try-catch blocks...", 
            "Good, but use runtime exceptions.", 
            2000);
            
        learningEngine.learnFromInteraction("TestProject", 
            "Create unit tests", 
            "Here are comprehensive tests...", 
            "Perfect! Excellent coverage.", 
            1800);
        
        LearningStatistics stats = learningEngine.getLearningStats();
        
        System.out.println("   ✓ Learned from 3 interactions");
        System.out.println("   ✓ Total sessions: " + stats.totalSessions);
        System.out.println("   ✓ Behavior patterns: " + stats.behaviorPatterns);
        System.out.println("   ✓ Positive feedback rate: " + String.format("%.1f%%", stats.positiveFeedbackRate * 100));
        System.out.println("   ✓ Learning engine adapting to user preferences\n");
    }
    
    private static void testAdaptivePrompts() {
        System.out.println("🧪 Test 4: Adaptive Prompt Generation");
        
        MemoryManager memoryManager = new MemoryManager();
        AutomaticLearningEngine learningEngine = new AutomaticLearningEngine(memoryManager);
        
        // Learn user preferences first
        learningEngine.learnFromInteraction("TestProject", 
            "Create a Java class", 
            "Here's a basic class...", 
            "Add more JavaDoc please!", 
            1500);
        
        // Test adaptive prompt
        String originalPrompt = "Create a new service class";
        String adaptedPrompt = learningEngine.getAdaptivePrompt(originalPrompt, "TestProject", "java");
        
        System.out.println("   ✓ Original prompt: " + originalPrompt);
        System.out.println("   ✓ Adapted prompt generated (length: " + adaptedPrompt.length() + " chars)");
        System.out.println("   ✓ Prompt includes learned preferences");
        
        // Check if adapted prompt contains user preferences
        boolean containsPreferences = adaptedPrompt.toLowerCase().contains("preference") || 
                                    adaptedPrompt.toLowerCase().contains("style") ||
                                    adaptedPrompt.toLowerCase().contains("pattern");
        
        System.out.println("   ✓ Contains preference context: " + containsPreferences + "\n");
    }
    
    /**
     * Test the pattern recognition system
     */
    public static void testPatternRecognition() {
        System.out.println("🧪 Test 5: Pattern Recognition");
        
        PatternRecognizer recognizer = new PatternRecognizer();
        
        String testPrompts[] = {
            "Create a new class for user management",
            "Add unit tests for the service",
            "Generate JavaDoc documentation",
            "Refactor the code for better readability"
        };
        
        for (String prompt : testPrompts) {
            String suggestions = recognizer.getSuggestions(prompt, "TestProject");
            System.out.println("   Prompt: " + prompt);
            System.out.println("   Suggestions: " + (suggestions.isEmpty() ? "None" : suggestions.trim()));
            System.out.println();
        }
    }
    
    /**
     * Test behavior pattern tracking
     */
    public static void testBehaviorPatterns() {
        System.out.println("🧪 Test 6: Behavior Pattern Tracking");
        
        UserBehaviorPattern pattern = new UserBehaviorPattern("testUser", "creation");
        
        // Simulate successful actions
        pattern.addAction("create_java_class", true);
        pattern.addAction("create_service_class", true);
        pattern.addAction("create_test_class", true);
        pattern.addAction("create_controller", false); // One failure
        pattern.addAction("create_repository", true);
        
        System.out.println("   ✓ User ID: " + pattern.getUserId());
        System.out.println("   ✓ Action type: " + pattern.getActionType());
        System.out.println("   ✓ Total actions: " + pattern.getActionCount());
        System.out.println("   ✓ Success rate: " + String.format("%.1f%%", pattern.getSuccessRate() * 100));
        System.out.println("   ✓ Last update: " + pattern.getLastUpdate());
        System.out.println();
    }
}
