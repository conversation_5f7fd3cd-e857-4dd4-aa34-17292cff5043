package com.example.fartechplugin.core;

import java.util.List;
import java.util.Map;

/**
 * Abstract base class for agent tools
 */
public abstract class BaseAgentTool implements AgentTool {
    
    protected final String name;
    protected final String description;
    protected final ToolCapability capability;
    protected final boolean requiresApproval;
    
    public BaseAgentTool(String name, String description, ToolCapability capability, boolean requiresApproval) {
        this.name = name;
        this.description = description;
        this.capability = capability;
        this.requiresApproval = requiresApproval;
    }
    
    @Override
    public String getName() {
        return name;
    }
    
    @Override
    public String getDescription() {
        return description;
    }
    
    @Override
    public ToolCapability getCapability() {
        return capability;
    }
    
    @Override
    public boolean requiresUserApproval() {
        return requiresApproval;
    }
    
    /**
     * Validate required parameters
     */
    protected boolean validateParameters(Map<String, Object> parameters) {
        List<String> required = getRequiredParameters();
        for (String param : required) {
            if (!parameters.containsKey(param) || parameters.get(param) == null) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Get parameter value with default
     */
    protected Object getParameter(Map<String, Object> parameters, String key, Object defaultValue) {
        return parameters.getOrDefault(key, defaultValue);
    }
    
    /**
     * Get string parameter
     */
    protected String getStringParameter(Map<String, Object> parameters, String key, String defaultValue) {
        Object value = parameters.get(key);
        return value != null ? value.toString() : defaultValue;
    }
    
    /**
     * Get boolean parameter
     */
    protected boolean getBooleanParameter(Map<String, Object> parameters, String key, boolean defaultValue) {
        Object value = parameters.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return defaultValue;
    }
}
