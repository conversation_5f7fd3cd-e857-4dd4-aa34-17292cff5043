package com.example.fartechplugin.core;

import java.time.LocalDateTime;

/**
 * Represents a single behavior action
 */
public class BehaviorAction {
    public final String context;
    public final boolean successful;
    public final LocalDateTime timestamp;
    
    public BehaviorAction(String context, boolean successful, LocalDateTime timestamp) {
        this.context = context;
        this.successful = successful;
        this.timestamp = timestamp;
    }
}
