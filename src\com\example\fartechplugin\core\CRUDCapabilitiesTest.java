package com.example.fartechplugin.core;

/**
 * Test class to demonstrate enhanced CRUD capabilities
 * Shows how the AI can now automatically create files and projects
 */
public class CRUDCapabilitiesTest {
    
    public static void main(String[] args) {
        testFileCreationDetection();
        testProjectCreationCapabilities();
    }
    
    /**
     * Test file creation request detection
     */
    public static void testFileCreationDetection() {
        System.out.println("=== File Creation Detection Test ===");
        
        String[] testMessages = {
            "I want to create A simple java project structure can you make it for me ?",
            "create a java project",
            "make a simple project structure",
            "generate a java project",
            "setup a basic java project",
            "build a java project structure",
            "create a file for me",
            "make a java class",
            "generate some files",
            "just chat with me"
        };
        
        WorkspaceIndexer mockIndexer = new WorkspaceIndexer();
        FileOperationsAgent mockAgent = new FileOperationsAgent();
        AutoCRUDProcessor processor = new AutoCRUDProcessor(mockIndexer, mockAgent);
        
        for (String message : testMessages) {
            try {
                // Use reflection to test the private method
                java.lang.reflect.Method method = AutoCRUDProcessor.class.getDeclaredMethod("detectsFileCreationRequest", String.class);
                method.setAccessible(true);
                boolean detected = (Boolean) method.invoke(processor, message);
                
                System.out.println("Message: \"" + message + "\"");
                System.out.println("Creation Request Detected: " + (detected ? "YES" : "NO"));
                System.out.println();
                
            } catch (Exception e) {
                System.err.println("Error testing message: " + message + " - " + e.getMessage());
            }
        }
    }
    
    /**
     * Test project creation capabilities
     */
    public static void testProjectCreationCapabilities() {
        System.out.println("=== Project Creation Capabilities ===");
        
        System.out.println("When user asks: \"I want to create A simple java project structure can you make it for me ?\"");
        System.out.println();
        System.out.println("Expected AI Response:");
        System.out.println("[AGENT] **Agent Mode: Automatically creating files**");
        System.out.println();
        System.out.println("[PROJECT] **Creating Java Project Structure**");
        System.out.println();
        System.out.println("[DIR] Created directory: `my-java-project`");
        System.out.println("[DIR] Created directory: `my-java-project/src`");
        System.out.println("[DIR] Created directory: `my-java-project/src/main`");
        System.out.println("[DIR] Created directory: `my-java-project/src/main/java`");
        System.out.println("[DIR] Created directory: `my-java-project/src/main/java/com`");
        System.out.println("[DIR] Created directory: `my-java-project/src/main/java/com/example`");
        System.out.println("[DIR] Created directory: `my-java-project/src/main/java/com/example/myproject`");
        System.out.println();
        System.out.println("[FILE] Created: `my-java-project/src/main/java/com/example/myproject/Application.java`");
        System.out.println("[FILE] Created: `my-java-project/pom.xml`");
        System.out.println("[FILE] Created: `my-java-project/README.md`");
        System.out.println();
        System.out.println("[SUCCESS] Java project structure created successfully!");
        System.out.println("[INFO] Project location: `my-java-project/`");
        System.out.println("[INFO] Main class: `com.example.myproject.Application`");
        System.out.println("[INFO] To run: `cd my-java-project && mvn compile exec:java`");
        System.out.println();
        System.out.println("=== Files Created ===");
        System.out.println();
        System.out.println("1. Application.java:");
        System.out.println("   - Package: com.example.myproject");
        System.out.println("   - Main method with Hello World");
        System.out.println("   - Proper Java documentation");
        System.out.println();
        System.out.println("2. pom.xml:");
        System.out.println("   - Maven configuration");
        System.out.println("   - Java 17 target");
        System.out.println("   - Executable JAR configuration");
        System.out.println();
        System.out.println("3. README.md:");
        System.out.println("   - Project documentation");
        System.out.println("   - Build and run instructions");
        System.out.println("   - FarTech AI attribution");
        System.out.println();
        System.out.println("=== Key Benefits ===");
        System.out.println();
        System.out.println("✓ No more chat-only responses");
        System.out.println("✓ Actual files created in workspace");
        System.out.println("✓ Complete project structure");
        System.out.println("✓ Ready-to-run Maven project");
        System.out.println("✓ Proper Java conventions");
        System.out.println("✓ Automatic directory creation");
        System.out.println("✓ Professional file templates");
    }
}
