package com.example.fartechplugin.core;

import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.security.MessageDigest;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Code Checkpoint Manager - Inspired by Augment's Code Checkpoints
 * Automatic change tracking with easy rollbacks for safe AI-assisted coding
 */
public class CheckpointManager {
    
    private static final String CHECKPOINT_DIR = System.getProperty("user.home") + "/.fartech-ai/checkpoints";
    private static final int MAX_CHECKPOINTS_PER_FILE = 10;
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss");
    
    private final Map<String, List<Checkpoint>> fileCheckpoints;
    private final Map<String, String> currentFileHashes;
    
    public CheckpointManager() {
        this.fileCheckpoints = new ConcurrentHashMap<>();
        this.currentFileHashes = new ConcurrentHashMap<>();
        initializeCheckpointDirectory();
        loadExistingCheckpoints();
    }
    
    private void initializeCheckpointDirectory() {
        try {
            Files.createDirectories(Paths.get(CHECKPOINT_DIR));
        } catch (IOException e) {
            System.err.println("Failed to create checkpoint directory: " + e.getMessage());
        }
    }
    
    /**
     * Create a checkpoint before making changes to a file
     */
    public CheckpointResult createCheckpoint(String filePath, String description) {
        try {
            Path file = Paths.get(filePath);
            if (!Files.exists(file)) {
                return new CheckpointResult(false, "File does not exist: " + filePath);
            }
            
            String content = new String(Files.readAllBytes(file));
            String contentHash = calculateHash(content);
            
            // Check if content has changed since last checkpoint
            String lastHash = currentFileHashes.get(filePath);
            if (contentHash.equals(lastHash)) {
                return new CheckpointResult(false, "No changes detected since last checkpoint");
            }
            
            // Create checkpoint
            Checkpoint checkpoint = new Checkpoint(
                filePath,
                content,
                contentHash,
                description,
                LocalDateTime.now()
            );
            
            // Save checkpoint to disk
            String checkpointId = saveCheckpointToDisk(checkpoint);
            checkpoint.setId(checkpointId);
            
            // Add to memory
            List<Checkpoint> checkpoints = fileCheckpoints.getOrDefault(filePath, new ArrayList<>());
            checkpoints.add(checkpoint);
            
            // Maintain max checkpoints limit
            if (checkpoints.size() > MAX_CHECKPOINTS_PER_FILE) {
                Checkpoint oldestCheckpoint = checkpoints.remove(0);
                deleteCheckpointFromDisk(oldestCheckpoint.getId());
            }
            
            fileCheckpoints.put(filePath, checkpoints);
            currentFileHashes.put(filePath, contentHash);
            
            return new CheckpointResult(true, "Checkpoint created: " + checkpointId);
            
        } catch (Exception e) {
            return new CheckpointResult(false, "Failed to create checkpoint: " + e.getMessage());
        }
    }
    
    /**
     * Automatically create checkpoint before AI modifications
     */
    public CheckpointResult autoCheckpoint(String filePath) {
        return createCheckpoint(filePath, "Auto-checkpoint before AI modification");
    }
    
    /**
     * Restore a file to a specific checkpoint
     */
    public CheckpointResult restoreCheckpoint(String filePath, String checkpointId) {
        try {
            List<Checkpoint> checkpoints = fileCheckpoints.get(filePath);
            if (checkpoints == null) {
                return new CheckpointResult(false, "No checkpoints found for file: " + filePath);
            }
            
            Checkpoint targetCheckpoint = checkpoints.stream()
                .filter(cp -> cp.getId().equals(checkpointId))
                .findFirst()
                .orElse(null);
            
            if (targetCheckpoint == null) {
                return new CheckpointResult(false, "Checkpoint not found: " + checkpointId);
            }
            
            // Create a checkpoint of current state before restoring
            createCheckpoint(filePath, "Before restore to " + checkpointId);
            
            // Restore the file content
            Files.write(Paths.get(filePath), targetCheckpoint.getContent().getBytes());
            currentFileHashes.put(filePath, targetCheckpoint.getContentHash());
            
            return new CheckpointResult(true, "File restored to checkpoint: " + checkpointId);
            
        } catch (Exception e) {
            return new CheckpointResult(false, "Failed to restore checkpoint: " + e.getMessage());
        }
    }
    
    /**
     * Get all checkpoints for a file
     */
    public List<Checkpoint> getCheckpoints(String filePath) {
        return new ArrayList<>(fileCheckpoints.getOrDefault(filePath, new ArrayList<>()));
    }
    
    /**
     * Get recent checkpoints across all files
     */
    public List<Checkpoint> getRecentCheckpoints(int limit) {
        return fileCheckpoints.values().stream()
            .flatMap(List::stream)
            .sorted((cp1, cp2) -> cp2.getTimestamp().compareTo(cp1.getTimestamp()))
            .limit(limit)
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    /**
     * Delete a specific checkpoint
     */
    public CheckpointResult deleteCheckpoint(String filePath, String checkpointId) {
        try {
            List<Checkpoint> checkpoints = fileCheckpoints.get(filePath);
            if (checkpoints == null) {
                return new CheckpointResult(false, "No checkpoints found for file: " + filePath);
            }
            
            boolean removed = checkpoints.removeIf(cp -> cp.getId().equals(checkpointId));
            if (removed) {
                deleteCheckpointFromDisk(checkpointId);
                return new CheckpointResult(true, "Checkpoint deleted: " + checkpointId);
            } else {
                return new CheckpointResult(false, "Checkpoint not found: " + checkpointId);
            }
            
        } catch (Exception e) {
            return new CheckpointResult(false, "Failed to delete checkpoint: " + e.getMessage());
        }
    }
    
    /**
     * Compare current file with a checkpoint
     */
    public String compareWithCheckpoint(String filePath, String checkpointId) {
        try {
            List<Checkpoint> checkpoints = fileCheckpoints.get(filePath);
            if (checkpoints == null) {
                return "No checkpoints found for file: " + filePath;
            }
            
            Checkpoint checkpoint = checkpoints.stream()
                .filter(cp -> cp.getId().equals(checkpointId))
                .findFirst()
                .orElse(null);
            
            if (checkpoint == null) {
                return "Checkpoint not found: " + checkpointId;
            }
            
            String currentContent = new String(Files.readAllBytes(Paths.get(filePath)));
            return generateDiff(checkpoint.getContent(), currentContent);
            
        } catch (Exception e) {
            return "Failed to compare with checkpoint: " + e.getMessage();
        }
    }
    
    /**
     * Check if file has unsaved changes since last checkpoint
     */
    public boolean hasUnsavedChanges(String filePath) {
        try {
            String currentContent = new String(Files.readAllBytes(Paths.get(filePath)));
            String currentHash = calculateHash(currentContent);
            String lastHash = currentFileHashes.get(filePath);
            
            return !currentHash.equals(lastHash);
            
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Get checkpoint statistics
     */
    public CheckpointStats getStats() {
        int totalCheckpoints = fileCheckpoints.values().stream()
            .mapToInt(List::size)
            .sum();
        
        int totalFiles = fileCheckpoints.size();
        
        LocalDateTime oldestCheckpoint = fileCheckpoints.values().stream()
            .flatMap(List::stream)
            .map(Checkpoint::getTimestamp)
            .min(LocalDateTime::compareTo)
            .orElse(null);
        
        LocalDateTime newestCheckpoint = fileCheckpoints.values().stream()
            .flatMap(List::stream)
            .map(Checkpoint::getTimestamp)
            .max(LocalDateTime::compareTo)
            .orElse(null);
        
        return new CheckpointStats(totalCheckpoints, totalFiles, oldestCheckpoint, newestCheckpoint);
    }
    
    // Private utility methods
    private String saveCheckpointToDisk(Checkpoint checkpoint) throws IOException {
        String timestamp = checkpoint.getTimestamp().format(TIMESTAMP_FORMAT);
        String fileName = sanitizeFileName(checkpoint.getFilePath()) + "_" + timestamp + ".checkpoint";
        Path checkpointFile = Paths.get(CHECKPOINT_DIR, fileName);
        
        // Save checkpoint metadata and content
        Properties props = new Properties();
        props.setProperty("filePath", checkpoint.getFilePath());
        props.setProperty("description", checkpoint.getDescription());
        props.setProperty("timestamp", checkpoint.getTimestamp().toString());
        props.setProperty("contentHash", checkpoint.getContentHash());
        
        try (FileOutputStream fos = new FileOutputStream(checkpointFile.toString() + ".meta")) {
            props.store(fos, "Checkpoint metadata");
        }
        
        Files.write(Paths.get(checkpointFile.toString() + ".content"), checkpoint.getContent().getBytes());
        
        return fileName;
    }
    
    private void deleteCheckpointFromDisk(String checkpointId) {
        try {
            Files.deleteIfExists(Paths.get(CHECKPOINT_DIR, checkpointId + ".meta"));
            Files.deleteIfExists(Paths.get(CHECKPOINT_DIR, checkpointId + ".content"));
        } catch (IOException e) {
            System.err.println("Failed to delete checkpoint files: " + e.getMessage());
        }
    }
    
    private void loadExistingCheckpoints() {
        try {
            Files.list(Paths.get(CHECKPOINT_DIR))
                .filter(path -> path.toString().endsWith(".meta"))
                .forEach(this::loadCheckpointFromDisk);
        } catch (IOException e) {
            System.err.println("Failed to load existing checkpoints: " + e.getMessage());
        }
    }
    
    private void loadCheckpointFromDisk(Path metaFile) {
        try {
            Properties props = new Properties();
            try (FileInputStream fis = new FileInputStream(metaFile.toFile())) {
                props.load(fis);
            }
            
            String filePath = props.getProperty("filePath");
            String description = props.getProperty("description");
            String timestamp = props.getProperty("timestamp");
            String contentHash = props.getProperty("contentHash");
            
            String contentFile = metaFile.toString().replace(".meta", ".content");
            String content = new String(Files.readAllBytes(Paths.get(contentFile)));
            
            Checkpoint checkpoint = new Checkpoint(
                filePath,
                content,
                contentHash,
                description,
                LocalDateTime.parse(timestamp)
            );
            
            String checkpointId = metaFile.getFileName().toString().replace(".meta", "");
            checkpoint.setId(checkpointId);
            
            List<Checkpoint> checkpoints = fileCheckpoints.getOrDefault(filePath, new ArrayList<>());
            checkpoints.add(checkpoint);
            fileCheckpoints.put(filePath, checkpoints);
            currentFileHashes.put(filePath, contentHash);
            
        } catch (Exception e) {
            System.err.println("Failed to load checkpoint from " + metaFile + ": " + e.getMessage());
        }
    }
    
    private String calculateHash(String content) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(content.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            return String.valueOf(content.hashCode());
        }
    }
    
    private String sanitizeFileName(String filePath) {
        return filePath.replaceAll("[^a-zA-Z0-9._-]", "_");
    }
    
    private String generateDiff(String original, String current) {
        // Simple diff implementation
        String[] originalLines = original.split("\n");
        String[] currentLines = current.split("\n");
        
        StringBuilder diff = new StringBuilder();
        diff.append("=== DIFF ===\n");
        
        int maxLines = Math.max(originalLines.length, currentLines.length);
        for (int i = 0; i < maxLines; i++) {
            String originalLine = i < originalLines.length ? originalLines[i] : "";
            String currentLine = i < currentLines.length ? currentLines[i] : "";
            
            if (!originalLine.equals(currentLine)) {
                if (!originalLine.isEmpty()) {
                    diff.append("- ").append(originalLine).append("\n");
                }
                if (!currentLine.isEmpty()) {
                    diff.append("+ ").append(currentLine).append("\n");
                }
            }
        }
        
        return diff.toString();
    }
}

/**
 * Represents a code checkpoint
 */
class Checkpoint {
    private String id;
    private final String filePath;
    private final String content;
    private final String contentHash;
    private final String description;
    private final LocalDateTime timestamp;
    
    public Checkpoint(String filePath, String content, String contentHash, 
                     String description, LocalDateTime timestamp) {
        this.filePath = filePath;
        this.content = content;
        this.contentHash = contentHash;
        this.description = description;
        this.timestamp = timestamp;
    }
    
    // Getters
    public String getId() { return id; }
    public String getFilePath() { return filePath; }
    public String getContent() { return content; }
    public String getContentHash() { return contentHash; }
    public String getDescription() { return description; }
    public LocalDateTime getTimestamp() { return timestamp; }
    
    public void setId(String id) { this.id = id; }
    
    @Override
    public String toString() {
        return String.format("Checkpoint[%s] %s - %s", 
            id != null ? id.substring(0, Math.min(8, id.length())) : "new",
            timestamp.format(DateTimeFormatter.ofPattern("MM-dd HH:mm")),
            description);
    }
}

/**
 * Result of checkpoint operations
 */
class CheckpointResult {
    public final boolean success;
    public final String message;
    
    public CheckpointResult(boolean success, String message) {
        this.success = success;
        this.message = message;
    }
}

/**
 * Checkpoint statistics
 */
class CheckpointStats {
    public final int totalCheckpoints;
    public final int totalFiles;
    public final LocalDateTime oldestCheckpoint;
    public final LocalDateTime newestCheckpoint;
    
    public CheckpointStats(int totalCheckpoints, int totalFiles, 
                          LocalDateTime oldestCheckpoint, LocalDateTime newestCheckpoint) {
        this.totalCheckpoints = totalCheckpoints;
        this.totalFiles = totalFiles;
        this.oldestCheckpoint = oldestCheckpoint;
        this.newestCheckpoint = newestCheckpoint;
    }
    
    @Override
    public String toString() {
        return String.format(
            "Checkpoint Stats: %d checkpoints across %d files\n" +
            "Oldest: %s, Newest: %s",
            totalCheckpoints, totalFiles,
            oldestCheckpoint != null ? oldestCheckpoint.format(DateTimeFormatter.ofPattern("MM-dd HH:mm")) : "None",
            newestCheckpoint != null ? newestCheckpoint.format(DateTimeFormatter.ofPattern("MM-dd HH:mm")) : "None"
        );
    }
}
