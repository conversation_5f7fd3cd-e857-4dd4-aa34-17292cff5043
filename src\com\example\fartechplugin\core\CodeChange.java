package com.example.fartechplugin.core;

/**
 * Represents a tracked code change for rollback purposes
 */
public class CodeChange {
    public final String originalContent;
    public final String modifiedContent;
    public final CodeSuggestion suggestion;
    public final java.time.LocalDateTime timestamp;
    public final String changeId;
    
    public CodeChange(String originalContent, String modifiedContent, 
                     CodeSuggestion suggestion, java.time.LocalDateTime timestamp) {
        this.originalContent = originalContent;
        this.modifiedContent = modifiedContent;
        this.suggestion = suggestion;
        this.timestamp = timestamp;
        this.changeId = generateChangeId();
    }
    
    private String generateChangeId() {
        return "change_" + timestamp.format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
    }
    
    public String getChangeDescription() {
        return String.format("%s at %s", 
            suggestion.type.getDescription(),
            timestamp.format(java.time.format.DateTimeFormatter.ofPattern("HH:mm:ss")));
    }
    
    @Override
    public String toString() {
        return String.format("CodeChange[%s] %s", changeId, getChangeDescription());
    }
}
