package com.example.fartechplugin.core;

/**
 * Represents a code suggestion from AI for Smart Apply feature
 */
public class CodeSuggestion {
    public final String filePath;
    public final String code;
    public final SuggestionType type;
    public final String context;
    
    public CodeSuggestion(String filePath, String code, SuggestionType type, String context) {
        this.filePath = filePath;
        this.code = code;
        this.type = type;
        this.context = context;
    }
    
    @Override
    public String toString() {
        return String.format("CodeSuggestion[%s] %s - %s chars", 
            type.toString().toLowerCase().replace("_", " "),
            filePath != null ? filePath : "new code",
            code.length());
    }
}
