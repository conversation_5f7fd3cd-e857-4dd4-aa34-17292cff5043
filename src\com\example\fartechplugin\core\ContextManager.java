package com.example.fartechplugin.core;

import java.util.*;
import java.time.LocalDateTime;

/**
 * Context Manager for AI Agent
 * FR.A.3: Access and manage context from the IDE
 */
public class ContextManager {
    
    private IDEContext currentContext;
    private final List<ContextSnapshot> contextHistory;
    private final Map<String, Object> globalContext;
    private final List<String> failureHistory;
    
    public ContextManager() {
        this.currentContext = new IDEContext();
        this.contextHistory = new ArrayList<>();
        this.globalContext = new HashMap<>();
        this.failureHistory = new ArrayList<>();
    }
    
    /**
     * Update context with current IDE state
     */
    public void updateContext(IDEContext newContext) {
        // Save current context to history
        if (currentContext != null) {
            contextHistory.add(new ContextSnapshot(currentContext, LocalDateTime.now()));
        }
        
        this.currentContext = newContext;
        
        // Keep only recent history (last 10 snapshots)
        if (contextHistory.size() > 10) {
            contextHistory.remove(0);
        }
    }
    
    /**
     * Get current IDE context
     */
    public IDEContext getCurrentContext() {
        return currentContext;
    }
    
    /**
     * Add failure context for self-correction
     */
    public void addFailureContext(String failureReason) {
        failureHistory.add(LocalDateTime.now() + ": " + failureReason);
        
        // Keep only recent failures
        if (failureHistory.size() > 5) {
            failureHistory.remove(0);
        }
    }
    
    /**
     * Get enriched context for planning
     */
    public EnrichedContext getEnrichedContext() {
        EnrichedContext enriched = new EnrichedContext();
        enriched.setCurrentContext(currentContext);
        enriched.setContextHistory(new ArrayList<>(contextHistory));
        enriched.setGlobalContext(new HashMap<>(globalContext));
        enriched.setFailureHistory(new ArrayList<>(failureHistory));
        enriched.setProjectStructure(analyzeProjectStructure());
        enriched.setRecentFiles(getRecentFiles());
        enriched.setCodePatterns(analyzeCodePatterns());
        
        return enriched;
    }
    
    /**
     * Analyze project structure from current context
     */
    private ProjectStructure analyzeProjectStructure() {
        ProjectStructure structure = new ProjectStructure();
        
        if (currentContext.getProjectRoot() != null) {
            structure.setRootPath(currentContext.getProjectRoot());
            structure.setProjectType(detectProjectType());
            structure.setMainDirectories(getMainDirectories());
            structure.setBuildFiles(getBuildFiles());
        }
        
        return structure;
    }
    
    private String detectProjectType() {
        // Analyze project files to determine type
        List<String> openFiles = currentContext.getOpenFiles();
        
        for (String file : openFiles) {
            if (file.endsWith(".java")) return "Java";
            if (file.endsWith(".js") || file.endsWith(".jsx")) return "JavaScript/React";
            if (file.endsWith(".py")) return "Python";
            if (file.endsWith(".cs")) return "C#";
            if (file.endsWith(".cpp") || file.endsWith(".c")) return "C/C++";
        }
        
        return "Unknown";
    }
    
    private List<String> getMainDirectories() {
        List<String> directories = new ArrayList<>();
        directories.add("src");
        directories.add("test");
        directories.add("resources");
        return directories;
    }
    
    private List<String> getBuildFiles() {
        List<String> buildFiles = new ArrayList<>();
        buildFiles.add("pom.xml");
        buildFiles.add("build.gradle");
        buildFiles.add("package.json");
        return buildFiles;
    }
    
    private List<String> getRecentFiles() {
        List<String> recentFiles = new ArrayList<>();
        
        // Get files from context history
        for (ContextSnapshot snapshot : contextHistory) {
            if (snapshot.getContext().getCurrentFile() != null) {
                String file = snapshot.getContext().getCurrentFile();
                if (!recentFiles.contains(file)) {
                    recentFiles.add(file);
                }
            }
        }
        
        // Add current file
        if (currentContext.getCurrentFile() != null && 
            !recentFiles.contains(currentContext.getCurrentFile())) {
            recentFiles.add(currentContext.getCurrentFile());
        }
        
        return recentFiles;
    }
    
    private List<CodePattern> analyzeCodePatterns() {
        List<CodePattern> patterns = new ArrayList<>();
        
        // Analyze patterns from recent files
        List<String> recentFiles = getRecentFiles();
        for (String file : recentFiles) {
            patterns.addAll(extractPatternsFromFile(file));
        }
        
        return patterns;
    }
    
    private List<CodePattern> extractPatternsFromFile(String filePath) {
        List<CodePattern> patterns = new ArrayList<>();
        
        // Extract common patterns based on file type
        if (filePath.endsWith(".java")) {
            patterns.add(new CodePattern("Java Class", "Standard Java class structure"));
            patterns.add(new CodePattern("Spring Boot", "Spring framework patterns"));
        } else if (filePath.endsWith(".js")) {
            patterns.add(new CodePattern("ES6 Module", "Modern JavaScript module"));
            patterns.add(new CodePattern("React Component", "React component pattern"));
        }
        
        return patterns;
    }
    
    /**
     * Update global context with key-value pairs
     */
    public void setGlobalContextValue(String key, Object value) {
        globalContext.put(key, value);
    }
    
    public Object getGlobalContextValue(String key) {
        return globalContext.get(key);
    }
    
    /**
     * Clear context history
     */
    public void clearHistory() {
        contextHistory.clear();
        failureHistory.clear();
    }
}

/**
 * Snapshot of IDE context at a specific time
 */
class ContextSnapshot {
    private final IDEContext context;
    private final LocalDateTime timestamp;
    
    public ContextSnapshot(IDEContext context, LocalDateTime timestamp) {
        this.context = context;
        this.timestamp = timestamp;
    }
    
    public IDEContext getContext() { return context; }
    public LocalDateTime getTimestamp() { return timestamp; }
}

/**
 * Enriched context with additional analysis
 */
class EnrichedContext {
    private IDEContext currentContext;
    private List<ContextSnapshot> contextHistory;
    private Map<String, Object> globalContext;
    private List<String> failureHistory;
    private ProjectStructure projectStructure;
    private List<String> recentFiles;
    private List<CodePattern> codePatterns;
    
    // Getters and setters
    public IDEContext getCurrentContext() { return currentContext; }
    public void setCurrentContext(IDEContext currentContext) { this.currentContext = currentContext; }
    
    public List<ContextSnapshot> getContextHistory() { return contextHistory; }
    public void setContextHistory(List<ContextSnapshot> contextHistory) { this.contextHistory = contextHistory; }
    
    public Map<String, Object> getGlobalContext() { return globalContext; }
    public void setGlobalContext(Map<String, Object> globalContext) { this.globalContext = globalContext; }
    
    public List<String> getFailureHistory() { return failureHistory; }
    public void setFailureHistory(List<String> failureHistory) { this.failureHistory = failureHistory; }
    
    public ProjectStructure getProjectStructure() { return projectStructure; }
    public void setProjectStructure(ProjectStructure projectStructure) { this.projectStructure = projectStructure; }
    
    public List<String> getRecentFiles() { return recentFiles; }
    public void setRecentFiles(List<String> recentFiles) { this.recentFiles = recentFiles; }
    
    public List<CodePattern> getCodePatterns() { return codePatterns; }
    public void setCodePatterns(List<CodePattern> codePatterns) { this.codePatterns = codePatterns; }
}

/**
 * Project structure information
 */
class ProjectStructure {
    private String rootPath;
    private String projectType;
    private List<String> mainDirectories;
    private List<String> buildFiles;
    
    public ProjectStructure() {
        this.mainDirectories = new ArrayList<>();
        this.buildFiles = new ArrayList<>();
    }
    
    // Getters and setters
    public String getRootPath() { return rootPath; }
    public void setRootPath(String rootPath) { this.rootPath = rootPath; }
    
    public String getProjectType() { return projectType; }
    public void setProjectType(String projectType) { this.projectType = projectType; }
    
    public List<String> getMainDirectories() { return mainDirectories; }
    public void setMainDirectories(List<String> mainDirectories) { this.mainDirectories = mainDirectories; }
    
    public List<String> getBuildFiles() { return buildFiles; }
    public void setBuildFiles(List<String> buildFiles) { this.buildFiles = buildFiles; }
}

/**
 * Code pattern information
 */
class CodePattern {
    private final String name;
    private final String description;
    
    public CodePattern(String name, String description) {
        this.name = name;
        this.description = description;
    }
    
    public String getName() { return name; }
    public String getDescription() { return description; }
}
