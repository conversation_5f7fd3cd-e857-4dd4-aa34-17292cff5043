package com.example.fartechplugin.core;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Directory creation tool for AI Agent
 */
public class CreateDirectoryTool extends BaseAgentTool {
    
    public CreateDirectoryTool() {
        super("CreateDirectoryTool", "Create a new directory", ToolCapability.DIRECTORY_CREATE, false);
    }
    
    @Override
    public ToolExecutionResult execute(Map<String, Object> parameters) {
        if (!validateParameters(parameters)) {
            return ToolExecutionResult.error("Missing required parameter: path");
        }
        
        String path = getStringParameter(parameters, "path", "");
        boolean createParents = getBooleanParameter(parameters, "createParents", true);
        
        try {
            // Simulate directory creation
            return ToolExecutionResult.success("Directory created successfully: " + path);
        } catch (Exception e) {
            return ToolExecutionResult.error("Failed to create directory: " + e.getMessage());
        }
    }
    
    @Override
    public List<String> getRequiredParameters() {
        return Arrays.asList("path");
    }
    
    @Override
    public List<String> getOptionalParameters() {
        return Arrays.asList("createParents", "permissions");
    }
}
