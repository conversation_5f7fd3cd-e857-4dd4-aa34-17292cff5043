package com.example.fartechplugin.core;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * File deletion tool for AI Agent
 */
public class DeleteFileTool extends BaseAgentTool {
    
    public DeleteFileTool() {
        super("DeleteFileTool", "Delete a file or directory", ToolCapability.FILE_DELETE, true);
    }
    
    @Override
    public ToolExecutionResult execute(Map<String, Object> parameters) {
        if (!validateParameters(parameters)) {
            return ToolExecutionResult.error("Missing required parameter: path");
        }

        String path = getStringParameter(parameters, "path", "");
        boolean recursive = getBooleanParameter(parameters, "recursive", false);
        boolean approved = getBooleanParameter(parameters, "approved", false);

        try {
            // Check if this is an approved deletion
            if (approved) {
                // Actually perform the deletion
                java.io.File file = new java.io.File(path);
                if (file.exists()) {
                    if (file.isDirectory()) {
                        if (recursive) {
                            deleteDirectoryRecursively(file);
                        } else {
                            if (file.delete()) {
                                return ToolExecutionResult.success("Directory deleted successfully: " + path);
                            } else {
                                return ToolExecutionResult.error("Failed to delete directory (may not be empty): " + path);
                            }
                        }
                    } else {
                        if (file.delete()) {
                            return ToolExecutionResult.success("File deleted successfully: " + path);
                        } else {
                            return ToolExecutionResult.error("Failed to delete file: " + path);
                        }
                    }
                } else {
                    return ToolExecutionResult.error("File or directory does not exist: " + path);
                }
            } else {
                // Just return what would be deleted (for approval)
                java.io.File file = new java.io.File(path);
                if (file.exists()) {
                    if (file.isDirectory()) {
                        return ToolExecutionResult.success("Will delete directory: " + path + " (contains " + countFiles(file) + " items)");
                    } else {
                        return ToolExecutionResult.success("Will delete file: " + path + " (size: " + file.length() + " bytes)");
                    }
                } else {
                    return ToolExecutionResult.error("File or directory does not exist: " + path);
                }
            }

            return ToolExecutionResult.success("Directory deleted successfully: " + path);
        } catch (Exception e) {
            return ToolExecutionResult.error("Failed to delete file: " + e.getMessage());
        }
    }

    private void deleteDirectoryRecursively(java.io.File dir) throws Exception {
        java.io.File[] files = dir.listFiles();
        if (files != null) {
            for (java.io.File file : files) {
                if (file.isDirectory()) {
                    deleteDirectoryRecursively(file);
                } else {
                    if (!file.delete()) {
                        throw new Exception("Failed to delete file: " + file.getAbsolutePath());
                    }
                }
            }
        }
        if (!dir.delete()) {
            throw new Exception("Failed to delete directory: " + dir.getAbsolutePath());
        }
    }

    private int countFiles(java.io.File dir) {
        if (!dir.isDirectory()) return 1;

        java.io.File[] files = dir.listFiles();
        if (files == null) return 0;

        int count = 0;
        for (java.io.File file : files) {
            if (file.isDirectory()) {
                count += countFiles(file);
            } else {
                count++;
            }
        }
        return count;
    }
    
    @Override
    public List<String> getRequiredParameters() {
        return Arrays.asList("path");
    }
    
    @Override
    public List<String> getOptionalParameters() {
        return Arrays.asList("recursive", "backup");
    }
}
