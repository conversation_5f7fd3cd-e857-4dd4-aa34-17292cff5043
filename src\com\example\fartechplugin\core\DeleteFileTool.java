package com.example.fartechplugin.core;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * File deletion tool for AI Agent
 */
public class DeleteFileTool extends BaseAgentTool {
    
    public DeleteFileTool() {
        super("DeleteFileTool", "Delete a file or directory", ToolCapability.FILE_DELETE, true);
    }
    
    @Override
    public ToolExecutionResult execute(Map<String, Object> parameters) {
        if (!validateParameters(parameters)) {
            return ToolExecutionResult.error("Missing required parameter: path");
        }
        
        String path = getStringParameter(parameters, "path", "");
        boolean recursive = getBooleanParameter(parameters, "recursive", false);
        
        try {
            // Simulate file deletion
            return ToolExecutionResult.success("File deleted successfully: " + path);
        } catch (Exception e) {
            return ToolExecutionResult.error("Failed to delete file: " + e.getMessage());
        }
    }
    
    @Override
    public List<String> getRequiredParameters() {
        return Arrays.asList("path");
    }
    
    @Override
    public List<String> getOptionalParameters() {
        return Arrays.asList("recursive", "backup");
    }
}
