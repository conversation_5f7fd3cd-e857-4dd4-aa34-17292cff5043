package com.example.fartechplugin.core;

/**
 * Diff Viewer
 */
public class DiffViewer {
    
    public String formatDiff(String diff) {
        StringBuilder formatted = new StringBuilder();
        formatted.append("=== PROPOSED CHANGES ===\n");
        formatted.append(diff);
        formatted.append("\n=== END CHANGES ===");
        return formatted.toString();
    }
    
    public String createDiff(String original, String modified, String fileName) {
        StringBuilder diff = new StringBuilder();
        diff.append("--- ").append(fileName).append(" (original)\n");
        diff.append("+++ ").append(fileName).append(" (modified)\n");
        diff.append("@@ -1,").append(countLines(original)).append(" +1,").append(countLines(modified)).append(" @@\n");
        
        // Simple line-by-line diff
        String[] originalLines = original.split("\n");
        String[] modifiedLines = modified.split("\n");
        
        for (int i = 0; i < Math.max(originalLines.length, modifiedLines.length); i++) {
            if (i < originalLines.length && i < modifiedLines.length) {
                if (!originalLines[i].equals(modifiedLines[i])) {
                    diff.append("-").append(originalLines[i]).append("\n");
                    diff.append("+").append(modifiedLines[i]).append("\n");
                } else {
                    diff.append(" ").append(originalLines[i]).append("\n");
                }
            } else if (i < originalLines.length) {
                diff.append("-").append(originalLines[i]).append("\n");
            } else {
                diff.append("+").append(modifiedLines[i]).append("\n");
            }
        }
        
        return diff.toString();
    }
    
    private int countLines(String text) {
        return text.split("\n").length;
    }
}
