package com.example.fartechplugin.core;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Terminal command execution tool for AI Agent
 */
public class ExecuteCommandTool extends BaseAgentTool {
    
    public ExecuteCommandTool() {
        super("ExecuteCommandTool", "Execute terminal commands", ToolCapability.TERMINAL_EXECUTION, false);
    }
    
    @Override
    public ToolExecutionResult execute(Map<String, Object> parameters) {
        if (!validateParameters(parameters)) {
            return ToolExecutionResult.error("Missing required parameter: command");
        }
        
        String command = getStringParameter(parameters, "command", "");
        String workingDir = getStringParameter(parameters, "workingDir", ".");
        
        try {
            // Simulate command execution
            String output = simulateCommandExecution(command);
            return ToolExecutionResult.success(output);
        } catch (Exception e) {
            return ToolExecutionResult.error("Command execution failed: " + e.getMessage());
        }
    }
    
    private String simulateCommandExecution(String command) {
        if (command.startsWith("mvn")) {
            return "[INFO] BUILD SUCCESS\n[INFO] Total time: 2.5s";
        } else if (command.startsWith("npm")) {
            return "npm install completed successfully\nAdded 15 packages";
        } else if (command.startsWith("git")) {
            return "Git command executed successfully";
        } else {
            return "Command executed: " + command + "\nOutput: Success";
        }
    }
    
    @Override
    public List<String> getRequiredParameters() {
        return Arrays.asList("command");
    }
    
    @Override
    public List<String> getOptionalParameters() {
        return Arrays.asList("workingDir", "timeout", "environment");
    }
}
