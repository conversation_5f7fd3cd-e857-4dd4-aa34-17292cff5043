package com.example.fartechplugin.core;

import java.util.*;

/**
 * Execution Engine for AI Agent
 * FR.A.4: Select and invoke appropriate tools to execute planned steps
 * FR.A.5: Interpret tool outputs and errors to inform subsequent steps
 */
public class ExecutionEngine {
    
    private final Map<String, ToolExecutionResult> stepResults;
    private boolean userApprovalRequired;
    private ExecutionStep pendingApprovalStep;
    
    public ExecutionEngine() {
        this.stepResults = new HashMap<>();
        this.userApprovalRequired = false;
    }
    
    /**
     * Execute a complete execution plan
     */
    public AgentResponse executePlan(ExecutionPlan plan, AIAgentCore agentCore) {
        agentCore.logExecution("Starting execution of plan with " + plan.getSteps().size() + " steps");
        
        try {
            for (ExecutionStep step : plan.getSteps()) {
                // Check if task was interrupted
                if (agentCore.getTaskStatus() == TaskStatus.INTERRUPTED) {
                    return AgentResponse.error("Task was interrupted by user");
                }
                
                agentCore.logExecution("Executing step: " + step.getDescription());
                
                // Execute step
                ToolExecutionResult result = executeStep(step, agentCore);
                stepResults.put(step.getStepId(), result);
                
                // Handle step result
                if (!result.isSuccess()) {
                    step.setStatus(StepStatus.FAILED);
                    step.setErrorMessage(result.getErrorMessage());
                    agentCore.logExecution("Step failed: " + result.getErrorMessage());
                    
                    return AgentResponse.error("Execution failed at step: " + step.getDescription() + 
                                             ". Error: " + result.getErrorMessage());
                }
                
                // Check if user approval is required
                if (step.requiresUserApproval()) {
                    step.setStatus(StepStatus.WAITING_FOR_APPROVAL);
                    pendingApprovalStep = step;
                    userApprovalRequired = true;
                    
                    agentCore.logExecution("User approval required for: " + step.getDescription());
                    
                    return AgentResponse.requiresApproval(
                        "User approval required for: " + step.getDescription(),
                        result.getOutput(),
                        createApprovalMetadata(step, result)
                    );
                }
                
                step.setStatus(StepStatus.COMPLETED);
                step.setResult(result.getOutput());
                agentCore.logExecution("Step completed successfully");
            }
            
            // All steps completed
            String finalResult = compileFinalResult(plan);
            agentCore.logExecution("Plan execution completed successfully");
            
            return AgentResponse.success("Task completed successfully", finalResult);
            
        } catch (Exception e) {
            agentCore.logExecution("Plan execution failed with exception: " + e.getMessage());
            return AgentResponse.error("Execution failed: " + e.getMessage());
        }
    }
    
    /**
     * Execute a single step
     */
    private ToolExecutionResult executeStep(ExecutionStep step, AIAgentCore agentCore) {
        try {
            // Get the appropriate tool
            AgentTool tool = getToolForStep(step);
            if (tool == null) {
                return ToolExecutionResult.error("Tool not found: " + step.getToolName());
            }
            
            // Prepare tool parameters
            Map<String, Object> parameters = prepareToolParameters(step);
            
            // Execute tool
            step.setStatus(StepStatus.EXECUTING);
            ToolExecutionResult result = tool.execute(parameters);
            
            // Log tool execution
            agentCore.logExecution("Tool " + step.getToolName() + " executed with result: " + 
                                 (result.isSuccess() ? "SUCCESS" : "FAILED"));
            
            return result;
            
        } catch (Exception e) {
            return ToolExecutionResult.error("Step execution failed: " + e.getMessage());
        }
    }
    
    /**
     * Handle user approval response
     */
    public AgentResponse handleUserApproval(boolean approved, String userFeedback) {
        if (!userApprovalRequired || pendingApprovalStep == null) {
            return AgentResponse.error("No pending approval request");
        }
        
        if (approved) {
            // User approved - continue execution
            pendingApprovalStep.setStatus(StepStatus.COMPLETED);
            userApprovalRequired = false;
            
            // Apply the approved changes
            ToolExecutionResult result = applyApprovedChanges(pendingApprovalStep);
            pendingApprovalStep.setResult(result.getOutput());
            
            pendingApprovalStep = null;
            
            return AgentResponse.success("Changes applied successfully", result.getOutput());
            
        } else {
            // User rejected - mark step as failed
            pendingApprovalStep.setStatus(StepStatus.FAILED);
            pendingApprovalStep.setErrorMessage("User rejected the proposed changes");
            userApprovalRequired = false;
            pendingApprovalStep = null;
            
            return AgentResponse.error("User rejected the proposed changes. " + 
                                     (userFeedback != null ? "Feedback: " + userFeedback : ""));
        }
    }
    
    /**
     * Get tool instance for execution step
     */
    private AgentTool getToolForStep(ExecutionStep step) {
        // This would integrate with the ToolRegistry
        String toolName = step.getToolName();
        
        switch (toolName) {
            case "ReadFileTool": return new ReadFileTool();
            case "WriteFileTool": return new WriteFileTool();
            case "DeleteFileTool": return new DeleteFileTool();
            case "AnalyzeCodeTool": return new AnalyzeCodeTool();
            case "ExecuteCommandTool": return new ExecuteCommandTool();
            case "GitStatusTool": return new GitStatusTool();
            case "GitCommitTool": return new GitCommitTool();
            default: return null;
        }
    }
    
    /**
     * Prepare parameters for tool execution
     */
    private Map<String, Object> prepareToolParameters(ExecutionStep step) {
        Map<String, Object> parameters = new HashMap<>(step.getParameters());
        
        // Add context from previous steps if needed
        for (Map.Entry<String, ToolExecutionResult> entry : stepResults.entrySet()) {
            if (entry.getValue().isSuccess()) {
                parameters.put("previousResult_" + entry.getKey(), entry.getValue().getOutput());
            }
        }
        
        return parameters;
    }
    
    /**
     * Apply changes that were approved by user
     */
    private ToolExecutionResult applyApprovedChanges(ExecutionStep step) {
        try {
            // Get the tool and execute the approved action
            AgentTool tool = getToolForStep(step);
            if (tool == null) {
                return ToolExecutionResult.error("Tool not found for approved action");
            }
            
            Map<String, Object> parameters = step.getParameters();
            parameters.put("approved", true);
            
            return tool.execute(parameters);
            
        } catch (Exception e) {
            return ToolExecutionResult.error("Failed to apply approved changes: " + e.getMessage());
        }
    }
    
    /**
     * Create metadata for approval request
     */
    private Map<String, Object> createApprovalMetadata(ExecutionStep step, ToolExecutionResult result) {
        Map<String, Object> metadata = new HashMap<>();
        metadata.put("stepId", step.getStepId());
        metadata.put("stepDescription", step.getDescription());
        metadata.put("toolName", step.getToolName());
        metadata.put("proposedChanges", result.getOutput());
        metadata.put("parameters", step.getParameters());
        
        // Add diff information if available
        if (result.getMetadata().containsKey("diff")) {
            metadata.put("diff", result.getMetadata().get("diff"));
        }
        
        return metadata;
    }
    
    /**
     * Compile final result from all step results
     */
    private String compileFinalResult(ExecutionPlan plan) {
        StringBuilder result = new StringBuilder();
        result.append("Task completed successfully!\n\n");
        
        result.append("Executed Steps:\n");
        for (ExecutionStep step : plan.getSteps()) {
            result.append("✓ ").append(step.getDescription()).append("\n");
            if (step.getResult() != null && !step.getResult().isEmpty()) {
                result.append("  Result: ").append(step.getResult()).append("\n");
            }
        }
        
        return result.toString();
    }
    
    /**
     * Get execution summary
     */
    public ExecutionSummary getExecutionSummary() {
        ExecutionSummary summary = new ExecutionSummary();
        summary.setTotalSteps(stepResults.size());
        summary.setSuccessfulSteps((int) stepResults.values().stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum());
        summary.setFailedSteps(stepResults.size() - summary.getSuccessfulSteps());
        summary.setUserApprovalRequired(userApprovalRequired);
        summary.setPendingStep(pendingApprovalStep != null ? pendingApprovalStep.getDescription() : null);
        
        return summary;
    }
    
    /**
     * Reset execution state
     */
    public void reset() {
        stepResults.clear();
        userApprovalRequired = false;
        pendingApprovalStep = null;
    }

    /**
     * Get execution engine for external access
     */
    public ExecutionEngine getExecutionEngine() {
        return this;
    }
}

/**
 * Result of tool execution
 */
class ToolExecutionResult {
    private final boolean success;
    private final String output;
    private final String errorMessage;
    private final Map<String, Object> metadata;
    
    private ToolExecutionResult(boolean success, String output, String errorMessage, Map<String, Object> metadata) {
        this.success = success;
        this.output = output;
        this.errorMessage = errorMessage;
        this.metadata = metadata != null ? metadata : new HashMap<>();
    }
    
    public static ToolExecutionResult success(String output) {
        return new ToolExecutionResult(true, output, null, null);
    }
    
    public static ToolExecutionResult success(String output, Map<String, Object> metadata) {
        return new ToolExecutionResult(true, output, null, metadata);
    }
    
    public static ToolExecutionResult error(String errorMessage) {
        return new ToolExecutionResult(false, null, errorMessage, null);
    }
    
    // Getters
    public boolean isSuccess() { return success; }
    public String getOutput() { return output; }
    public String getErrorMessage() { return errorMessage; }
    public Map<String, Object> getMetadata() { return new HashMap<>(metadata); }
}

/**
 * Summary of execution progress
 */
class ExecutionSummary {
    private int totalSteps;
    private int successfulSteps;
    private int failedSteps;
    private boolean userApprovalRequired;
    private String pendingStep;
    
    // Getters and setters
    public int getTotalSteps() { return totalSteps; }
    public void setTotalSteps(int totalSteps) { this.totalSteps = totalSteps; }
    
    public int getSuccessfulSteps() { return successfulSteps; }
    public void setSuccessfulSteps(int successfulSteps) { this.successfulSteps = successfulSteps; }
    
    public int getFailedSteps() { return failedSteps; }
    public void setFailedSteps(int failedSteps) { this.failedSteps = failedSteps; }
    
    public boolean isUserApprovalRequired() { return userApprovalRequired; }
    public void setUserApprovalRequired(boolean userApprovalRequired) { this.userApprovalRequired = userApprovalRequired; }
    
    public String getPendingStep() { return pendingStep; }
    public void setPendingStep(String pendingStep) { this.pendingStep = pendingStep; }
}
