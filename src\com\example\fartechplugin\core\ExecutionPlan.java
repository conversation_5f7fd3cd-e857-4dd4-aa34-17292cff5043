package com.example.fartechplugin.core;

import java.util.*;

/**
 * Represents a plan for executing a task
 */
public class ExecutionPlan {
    private final List<ExecutionStep> steps;
    private final Map<String, Object> metadata;
    private int currentStepIndex;
    
    public ExecutionPlan() {
        this.steps = new ArrayList<>();
        this.metadata = new HashMap<>();
        this.currentStepIndex = 0;
    }
    
    public void addStep(ExecutionStep step) {
        steps.add(step);
    }
    
    public List<ExecutionStep> getSteps() { return new ArrayList<>(steps); }
    public Map<String, Object> getMetadata() { return new HashMap<>(metadata); }
    public int getCurrentStepIndex() { return currentStepIndex; }
    public void setCurrentStepIndex(int index) { this.currentStepIndex = index; }
    
    public ExecutionStep getCurrentStep() {
        if (currentStepIndex < steps.size()) {
            return steps.get(currentStepIndex);
        }
        return null;
    }
    
    public boolean hasNextStep() {
        return currentStepIndex < steps.size() - 1;
    }
    
    public void nextStep() {
        if (hasNextStep()) {
            currentStepIndex++;
        }
    }
}
