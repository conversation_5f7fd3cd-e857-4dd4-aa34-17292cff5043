package com.example.fartechplugin.core;

import java.util.*;

/**
 * Represents a single step in an execution plan
 */
public class ExecutionStep {
    private final String stepId;
    private final String description;
    private final String toolName;
    private final Map<String, Object> parameters;
    private final boolean requiresUserApproval;
    private StepStatus status;
    private String result;
    private String errorMessage;
    
    public ExecutionStep(String description, String toolName, Map<String, Object> parameters, boolean requiresUserApproval) {
        this.stepId = UUID.randomUUID().toString();
        this.description = description;
        this.toolName = toolName;
        this.parameters = parameters != null ? parameters : new HashMap<>();
        this.requiresUserApproval = requiresUserApproval;
        this.status = StepStatus.PENDING;
    }
    
    // Getters and setters
    public String getStepId() { return stepId; }
    public String getDescription() { return description; }
    public String getToolName() { return toolName; }
    public Map<String, Object> getParameters() { return new HashMap<>(parameters); }
    public boolean requiresUserApproval() { return requiresUserApproval; }
    public StepStatus getStatus() { return status; }
    public void setStatus(StepStatus status) { this.status = status; }
    public String getResult() { return result; }
    public void setResult(String result) { this.result = result; }
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
}
