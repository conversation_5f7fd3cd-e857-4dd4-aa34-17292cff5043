package com.example.fartechplugin.core;

import java.util.HashMap;
import java.util.Map;

/**
 * Detects feedback from user interactions
 */
public class FeedbackDetector {
    
    private static final Map<String, Double> POSITIVE_KEYWORDS;
    private static final Map<String, Double> NEGATIVE_KEYWORDS;
    
    static {
        // Initialize positive keywords map (Java 8 compatible)
        POSITIVE_KEYWORDS = new HashMap<>();
        POSITIVE_KEYWORDS.put("perfect", 0.5);
        POSITIVE_KEYWORDS.put("excellent", 0.5);
        POSITIVE_KEYWORDS.put("great", 0.4);
        POSITIVE_KEYWORDS.put("good", 0.3);
        POSITIVE_KEYWORDS.put("exactly", 0.4);
        POSITIVE_KEYWORDS.put("correct", 0.3);
        POSITIVE_KEYWORDS.put("thanks", 0.2);
        POSITIVE_KEYWORDS.put("helpful", 0.3);
        
        // Initialize negative keywords map (Java 8 compatible)
        NEGATIVE_KEYWORDS = new HashMap<>();
        NEGATIVE_KEYWORDS.put("wrong", -0.5);
        NEGATIVE_KEYWORDS.put("incorrect", -0.5);
        NEGATIVE_KEYWORDS.put("bad", -0.4);
        NEGATIVE_KEYWORDS.put("error", -0.3);
        NEGATIVE_KEYWORDS.put("not what", -0.4);
        NEGATIVE_KEYWORDS.put("try again", -0.3);
        NEGATIVE_KEYWORDS.put("fix", -0.2);
        NEGATIVE_KEYWORDS.put("problem", -0.3);
    }
    
    public FeedbackSignal detectFeedback(String userInput, String aiResponse, String followUp) {
        FeedbackSignal signal = new FeedbackSignal();
        
        if (followUp != null) {
            String lower = followUp.toLowerCase();
            
            // Check for explicit positive feedback
            for (Map.Entry<String, Double> entry : POSITIVE_KEYWORDS.entrySet()) {
                if (lower.contains(entry.getKey())) {
                    signal.addSignal("positive_keyword_" + entry.getKey(), entry.getValue());
                }
            }
            
            // Check for explicit negative feedback
            for (Map.Entry<String, Double> entry : NEGATIVE_KEYWORDS.entrySet()) {
                if (lower.contains(entry.getKey())) {
                    signal.addSignal("negative_keyword_" + entry.getKey(), entry.getValue());
                }
            }
            
            // Check for continuation patterns (usually positive)
            if (lower.startsWith("and") || lower.startsWith("also") || lower.startsWith("now")) {
                signal.addSignal("continuation_pattern", 0.2);
            }
            
            // Check for correction patterns (usually negative)
            if (lower.startsWith("but") || lower.startsWith("however") || lower.startsWith("actually")) {
                signal.addSignal("correction_pattern", -0.2);
            }
            
            // Check for question patterns (neutral to slightly negative)
            if (lower.contains("?") && (lower.contains("how") || lower.contains("why") || lower.contains("what"))) {
                signal.addSignal("clarification_needed", -0.1);
            }
        }
        
        return signal;
    }
}
