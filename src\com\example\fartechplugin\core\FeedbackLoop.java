package com.example.fartechplugin.core;

import java.util.*;
import java.time.LocalDateTime;

/**
 * Feedback Loop for AI Agent
 * Processes tool outputs and errors to inform subsequent steps
 */
public class FeedbackLoop {
    
    private final List<FeedbackEntry> feedbackHistory;
    private final Map<String, Double> toolSuccessRates;
    private final Map<String, List<String>> commonErrors;
    
    public FeedbackLoop() {
        this.feedbackHistory = new ArrayList<>();
        this.toolSuccessRates = new HashMap<>();
        this.commonErrors = new HashMap<>();
    }
    
    /**
     * Process feedback from tool execution
     */
    public FeedbackAnalysis processFeedback(String toolName, ToolExecutionResult result, 
                                          Map<String, Object> context) {
        
        FeedbackEntry entry = new FeedbackEntry(toolName, result, context, LocalDateTime.now());
        feedbackHistory.add(entry);
        
        // Update success rates
        updateSuccessRate(toolName, result.isSuccess());
        
        // Track common errors
        if (!result.isSuccess()) {
            trackError(toolName, result.getErrorMessage());
        }
        
        // Generate feedback analysis
        return generateFeedbackAnalysis(entry);
    }
    
    /**
     * Get suggestions for improving execution
     */
    public List<String> getSuggestions(String toolName, String errorMessage) {
        List<String> suggestions = new ArrayList<>();
        
        // Check common errors for this tool
        List<String> errors = commonErrors.get(toolName);
        if (errors != null && errors.contains(errorMessage)) {
            suggestions.add("This is a common error for " + toolName + ". Consider alternative approach.");
        }
        
        // Check success rate
        Double successRate = toolSuccessRates.get(toolName);
        if (successRate != null && successRate < 0.5) {
            suggestions.add("Tool " + toolName + " has low success rate. Consider using alternative tools.");
        }
        
        // Add specific suggestions based on error type
        if (errorMessage.contains("file not found")) {
            suggestions.add("Verify file path exists before attempting operation.");
        } else if (errorMessage.contains("permission denied")) {
            suggestions.add("Check file permissions or run with appropriate privileges.");
        } else if (errorMessage.contains("timeout")) {
            suggestions.add("Consider increasing timeout or breaking operation into smaller steps.");
        }
        
        return suggestions;
    }
    
    /**
     * Get feedback statistics
     */
    public FeedbackStatistics getStatistics() {
        FeedbackStatistics stats = new FeedbackStatistics();
        stats.setTotalFeedbackEntries(feedbackHistory.size());
        stats.setToolSuccessRates(new HashMap<>(toolSuccessRates));
        stats.setCommonErrors(new HashMap<>(commonErrors));
        
        // Calculate overall success rate
        long successCount = feedbackHistory.stream()
            .mapToLong(entry -> entry.getResult().isSuccess() ? 1 : 0)
            .sum();
        
        if (feedbackHistory.size() > 0) {
            stats.setOverallSuccessRate((double) successCount / feedbackHistory.size());
        }
        
        return stats;
    }
    
    private void updateSuccessRate(String toolName, boolean success) {
        // Simple moving average for success rate
        Double currentRate = toolSuccessRates.get(toolName);
        if (currentRate == null) {
            toolSuccessRates.put(toolName, success ? 1.0 : 0.0);
        } else {
            // Weighted average (recent results have more weight)
            double newRate = (currentRate * 0.8) + (success ? 0.2 : 0.0);
            toolSuccessRates.put(toolName, newRate);
        }
    }
    
    private void trackError(String toolName, String errorMessage) {
        List<String> errors = commonErrors.getOrDefault(toolName, new ArrayList<>());
        if (!errors.contains(errorMessage)) {
            errors.add(errorMessage);
            // Keep only most recent 10 errors per tool
            if (errors.size() > 10) {
                errors.remove(0);
            }
            commonErrors.put(toolName, errors);
        }
    }
    
    private FeedbackAnalysis generateFeedbackAnalysis(FeedbackEntry entry) {
        FeedbackAnalysis analysis = new FeedbackAnalysis();
        analysis.setToolName(entry.getToolName());
        analysis.setSuccess(entry.getResult().isSuccess());
        analysis.setTimestamp(entry.getTimestamp());
        
        if (entry.getResult().isSuccess()) {
            analysis.setRecommendation("Tool executed successfully. Continue with next step.");
        } else {
            List<String> suggestions = getSuggestions(entry.getToolName(), entry.getResult().getErrorMessage());
            analysis.setSuggestions(suggestions);
            analysis.setRecommendation("Tool execution failed. Consider: " + String.join(", ", suggestions));
        }
        
        return analysis;
    }
    
    /**
     * Clear old feedback entries
     */
    public void cleanup() {
        // Keep only last 100 entries
        if (feedbackHistory.size() > 100) {
            feedbackHistory.subList(0, feedbackHistory.size() - 100).clear();
        }
    }
}

/**
 * Feedback entry for tracking tool execution results
 */
class FeedbackEntry {
    private final String toolName;
    private final ToolExecutionResult result;
    private final Map<String, Object> context;
    private final LocalDateTime timestamp;
    
    public FeedbackEntry(String toolName, ToolExecutionResult result, 
                        Map<String, Object> context, LocalDateTime timestamp) {
        this.toolName = toolName;
        this.result = result;
        this.context = context;
        this.timestamp = timestamp;
    }
    
    // Getters
    public String getToolName() { return toolName; }
    public ToolExecutionResult getResult() { return result; }
    public Map<String, Object> getContext() { return context; }
    public LocalDateTime getTimestamp() { return timestamp; }
}

/**
 * Analysis of feedback for a tool execution
 */
class FeedbackAnalysis {
    private String toolName;
    private boolean success;
    private LocalDateTime timestamp;
    private String recommendation;
    private List<String> suggestions;
    
    public FeedbackAnalysis() {
        this.suggestions = new ArrayList<>();
    }
    
    // Getters and setters
    public String getToolName() { return toolName; }
    public void setToolName(String toolName) { this.toolName = toolName; }
    
    public boolean isSuccess() { return success; }
    public void setSuccess(boolean success) { this.success = success; }
    
    public LocalDateTime getTimestamp() { return timestamp; }
    public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
    
    public String getRecommendation() { return recommendation; }
    public void setRecommendation(String recommendation) { this.recommendation = recommendation; }
    
    public List<String> getSuggestions() { return suggestions; }
    public void setSuggestions(List<String> suggestions) { this.suggestions = suggestions; }
}

/**
 * Statistics about feedback and tool performance
 */
class FeedbackStatistics {
    private int totalFeedbackEntries;
    private double overallSuccessRate;
    private Map<String, Double> toolSuccessRates;
    private Map<String, List<String>> commonErrors;
    
    // Getters and setters
    public int getTotalFeedbackEntries() { return totalFeedbackEntries; }
    public void setTotalFeedbackEntries(int totalFeedbackEntries) { this.totalFeedbackEntries = totalFeedbackEntries; }
    
    public double getOverallSuccessRate() { return overallSuccessRate; }
    public void setOverallSuccessRate(double overallSuccessRate) { this.overallSuccessRate = overallSuccessRate; }
    
    public Map<String, Double> getToolSuccessRates() { return toolSuccessRates; }
    public void setToolSuccessRates(Map<String, Double> toolSuccessRates) { this.toolSuccessRates = toolSuccessRates; }
    
    public Map<String, List<String>> getCommonErrors() { return commonErrors; }
    public void setCommonErrors(Map<String, List<String>> commonErrors) { this.commonErrors = commonErrors; }
}
