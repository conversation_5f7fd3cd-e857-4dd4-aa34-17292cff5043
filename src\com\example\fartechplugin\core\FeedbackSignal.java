package com.example.fartechplugin.core;

import java.util.HashMap;
import java.util.Map;

/**
 * Represents feedback signals detected from user behavior
 */
public class FeedbackSignal {
    private final Map<String, Double> signals;
    private FeedbackType type;
    private double overallScore;
    
    public FeedbackSignal() {
        this.signals = new HashMap<>();
        this.type = FeedbackType.NEUTRAL;
        this.overallScore = 0.0;
    }
    
    public void addSignal(String signalType, double weight) {
        signals.put(signalType, weight);
        recalculateScore();
    }
    
    private void recalculateScore() {
        overallScore = 0.0;
        for (Double value : signals.values()) {
            overallScore += value;
        }
        
        if (overallScore > 0.3) {
            type = FeedbackType.POSITIVE;
        } else if (overallScore < -0.3) {
            type = FeedbackType.NEGATIVE;
        } else {
            type = FeedbackType.NEUTRAL;
        }
    }
    
    public FeedbackType getType() { return type; }
    public double getOverallScore() { return overallScore; }
    public Map<String, Double> getSignals() { return new HashMap<>(signals); }
}
