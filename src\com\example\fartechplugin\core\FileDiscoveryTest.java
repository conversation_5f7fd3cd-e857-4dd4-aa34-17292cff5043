package com.example.fartechplugin.core;

/**
 * Test class to demonstrate enhanced file discovery capabilities
 */
public class FileDiscoveryTest {
    
    public static void main(String[] args) {
        testFileNameExtraction();
        testFileMatching();
    }
    
    /**
     * Test file name extraction from various user messages
     */
    public static void testFileNameExtraction() {
        System.out.println("=== File Name Extraction Test ===");
        
        String[] testMessages = {
            "can you analyze my code in GenericSubmitTaskLogic.java ?",
            "analyze my code in GenericSubmitTaskLogic.java",
            "read the file GenericSubmitTaskLogic.java",
            "show me the content of UserService.java",
            "examine the config.xml file",
            "check my data.json file",
            "view the script.py file",
            "analyze TaskLogic",
            "read my java file",
            "show me the code"
        };
        
        WorkspaceIndexer mockIndexer = new WorkspaceIndexer();
        FileOperationsAgent mockAgent = new FileOperationsAgent();
        AutoCRUDProcessor processor = new AutoCRUDProcessor(mockIndexer, mockAgent);
        
        for (String message : testMessages) {
            try {
                // Use reflection to test the private method
                java.lang.reflect.Method method = AutoCRUDProcessor.class.getDeclaredMethod("extractFileNameFromMessage", String.class);
                method.setAccessible(true);
                String extracted = (String) method.invoke(processor, message);
                
                System.out.println("Message: \"" + message + "\"");
                System.out.println("Extracted: " + (extracted != null ? "\"" + extracted + "\"" : "null"));
                System.out.println();
                
            } catch (Exception e) {
                System.err.println("Error testing message: " + message + " - " + e.getMessage());
            }
        }
    }
    
    /**
     * Test file matching logic
     */
    public static void testFileMatching() {
        System.out.println("=== File Matching Test ===");
        
        // Mock file list
        String[] mockFiles = {
            "src/main/java/com/example/service/GenericSubmitTaskLogic.java",
            "src/main/java/com/example/service/UserService.java",
            "src/main/java/com/example/controller/TaskController.java",
            "src/main/resources/config.xml",
            "src/main/resources/application.properties",
            "src/test/java/com/example/service/TaskLogicTest.java",
            "README.md",
            "pom.xml"
        };
        
        String[] testTargets = {
            "GenericSubmitTaskLogic.java",
            "GenericSubmitTaskLogic",
            "TaskLogic",
            "UserService",
            "config.xml",
            "TaskController.java",
            "nonexistent.java"
        };
        
        WorkspaceIndexer mockIndexer = new WorkspaceIndexer();
        FileOperationsAgent mockAgent = new FileOperationsAgent();
        AutoCRUDProcessor processor = new AutoCRUDProcessor(mockIndexer, mockAgent);
        
        for (String target : testTargets) {
            try {
                // Use reflection to test the private method
                java.lang.reflect.Method method = AutoCRUDProcessor.class.getDeclaredMethod("findMatchingFiles", String[].class, String.class);
                method.setAccessible(true);
                String[] matches = (String[]) method.invoke(processor, (Object) mockFiles, target);
                
                System.out.println("Target: \"" + target + "\"");
                System.out.println("Matches (" + matches.length + "):");
                for (String match : matches) {
                    System.out.println("  - " + match);
                }
                System.out.println();
                
            } catch (Exception e) {
                System.err.println("Error testing target: " + target + " - " + e.getMessage());
            }
        }
    }
}
