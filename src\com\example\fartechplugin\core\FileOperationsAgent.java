package com.example.fartechplugin.core;

import java.io.*;
import java.nio.file.*;
import java.nio.file.StandardCopyOption;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

import com.example.fartechplugin.FarTechView;
import com.example.fartechplugin.core.WorkspaceIndexer;

/**
 * AI Agent that can perform file operations and command execution
 * Moved to core package for better organization
 */
public class FileOperationsAgent {
    
    private static final Pattern READ_FILE_PATTERN = Pattern.compile("\\[READ_file:([^\\]]+)\\]", Pattern.CASE_INSENSITIVE);
    private static final Pattern WRITE_FILE_PATTERN = Pattern.compile("\\[write_file:([^\\]]+)\\]([\\s\\S]*?)\\[end_file\\]", Pattern.CASE_INSENSITIVE);
    private static final Pattern DELETE_FILE_PATTERN = Pattern.compile("\\[delete_file:([^\\]]+)\\]", Pattern.CASE_INSENSITIVE);
    private static final Pattern LIST_FILES_PATTERN = Pattern.compile("\\[list_files:([^\\]]+)\\]", Pattern.CASE_INSENSITIVE);
    private static final Pattern COPY_FILE_PATTERN = Pattern.compile("\\[copy_file:([^:]+):([^\\]]+)\\]", Pattern.CASE_INSENSITIVE);
    private static final Pattern MOVE_FILE_PATTERN = Pattern.compile("\\[move_file:([^:]+):([^\\]]+)\\]", Pattern.CASE_INSENSITIVE);
    private static final Pattern RENAME_FILE_PATTERN = Pattern.compile("\\[rename_file:([^:]+):([^\\]]+)\\]", Pattern.CASE_INSENSITIVE);
    private static final Pattern CREATE_DIR_PATTERN = Pattern.compile("\\[create_dir:([^\\]]+)\\]", Pattern.CASE_INSENSITIVE);
    private static final Pattern EXEC_POWERSHELL_PATTERN = Pattern.compile("\\[exec_powershell:([^\\]]+)\\]", Pattern.CASE_INSENSITIVE);
    private static final Pattern EXEC_CMD_PATTERN = Pattern.compile("\\[exec_cmd:([^\\]]+)\\]", Pattern.CASE_INSENSITIVE);
    private static final Pattern EXEC_POWERSHELL_DIR_PATTERN = Pattern.compile("\\[exec_powershell_dir:([^:]+):([^\\]]+)\\]", Pattern.CASE_INSENSITIVE);
    private static final Pattern BACKUP_FILE_PATTERN = Pattern.compile("\\[backup_file:([^\\]]+)\\]", Pattern.CASE_INSENSITIVE);

    // Safety settings
    private static final String BACKUP_DIR = ".fartech-backups";
    private static final boolean AUTO_BACKUP_ENABLED = true;
    
    /**
     * Process AI response and execute any agent commands
     */
    public String processAgentCommands(String aiResponse, FarTechView view, WorkspaceIndexer workspaceIndexer) {
        if (aiResponse == null || aiResponse.trim().isEmpty()) {
            return "No response from AI";
        }

        StringBuilder result = new StringBuilder();
        String remainingResponse = aiResponse;

        // Process each type of command (keeping original method signatures)
        remainingResponse = processReadFileCommands(remainingResponse, result, view, workspaceIndexer);
        remainingResponse = processWriteFileCommands(remainingResponse, result, view, workspaceIndexer);
        remainingResponse = processDeleteFileCommands(remainingResponse, result, view, workspaceIndexer);
        remainingResponse = processListFilesCommands(remainingResponse, result, view);
        remainingResponse = processCopyFileCommands(remainingResponse, result, view, workspaceIndexer);
        remainingResponse = processMoveFileCommands(remainingResponse, result, view, workspaceIndexer);
        remainingResponse = processRenameFileCommands(remainingResponse, result, view, workspaceIndexer);
        remainingResponse = processCreateDirCommands(remainingResponse, result, view);
        remainingResponse = processBackupFileCommands(remainingResponse, result, view);
        remainingResponse = processExecCommands(remainingResponse, result, view);

        // Add any remaining response text
        if (!remainingResponse.trim().isEmpty()) {
            result.append(remainingResponse.trim());
        }

        return result.toString();
    }
    
    /**
     * Process READ_FILE commands
     */
    private String processReadFileCommands(String response, StringBuilder result, FarTechView view) {
        return processReadFileCommands(response, result, view, null);
    }

    /**
     * Process READ_FILE commands with workspace indexer
     */
    private String processReadFileCommands(String response, StringBuilder result, FarTechView view, WorkspaceIndexer indexer) {
        Matcher matcher = READ_FILE_PATTERN.matcher(response);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String filePath = matcher.group(1).trim();
            String fileContent = readFileContentWithIndexer(filePath, indexer);

            String replacement = "\n=== FILE CONTENT: " + filePath + " ===\n" + fileContent + "\n=== END FILE ===\n";
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(sb);

        return sb.toString();
    }
    
    /**
     * Process WRITE_FILE commands
     */
    private String processWriteFileCommands(String response, StringBuilder result, FarTechView view, WorkspaceIndexer workspaceIndexer) {
        Matcher matcher = WRITE_FILE_PATTERN.matcher(response);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String filePath = matcher.group(1).trim();
            String content = matcher.group(2).trim();

            // Check if agent mode is enabled for automatic execution
            if (view.isAgentMode()) {
                // Execute automatically in agent mode
                view.addFileOperationProgress("write", filePath);
                view.addSystemMessage("🤖 Agent Mode: Automatically writing file: " + filePath);

                // Create automatic backup if file exists and auto-backup is enabled
                if (AUTO_BACKUP_ENABLED && Files.exists(Paths.get(filePath))) {
                    String backupResult = createBackup(filePath);
                    if (!backupResult.startsWith("Error")) {
                        view.addSystemMessage("💾 Automatic backup created: " + backupResult);
                    }
                }

                String writeResult = writeFileContent(filePath, content);
                boolean success = !writeResult.startsWith("Error");
                view.addFileOperationResult("write", filePath, success, writeResult);

                // Re-index the workspace after file changes
                if (workspaceIndexer != null) {
                    try {
                        workspaceIndexer.indexWorkspace(false); // Silent re-index
                        view.addSystemMessage("🔄 Workspace re-indexed after file modification.");
                    } catch (Exception e) {
                        view.addSystemMessage("⚠️ Warning: Failed to re-index workspace: " + e.getMessage());
                    }
                }

                String replacement = "\n✅ File written successfully: " + filePath + "\n";
                matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
            } else {
                // Ask for confirmation in manual mode
                String confirmMessage = "AI wants to write to file: " + filePath + "\n\nContent preview:\n" +
                                      (content.length() > 200 ? content.substring(0, 200) + "..." : content) +
                                      "\n\nAllow this operation?";

                view.showConfirmationDialog(confirmMessage, () -> {
                    view.addFileOperationProgress("write", filePath);

                    // Create automatic backup if file exists and auto-backup is enabled
                    if (AUTO_BACKUP_ENABLED && Files.exists(Paths.get(filePath))) {
                        String backupResult = createBackup(filePath);
                        if (!backupResult.startsWith("Error")) {
                            view.addSystemMessage("💾 Automatic backup created: " + backupResult);
                        }
                    }

                    String writeResult = writeFileContent(filePath, content);
                    boolean success = !writeResult.startsWith("Error");
                    view.addFileOperationResult("write", filePath, success, writeResult);

                    // Re-index the workspace after file changes
                    if (workspaceIndexer != null) {
                        try {
                            workspaceIndexer.indexWorkspace();
                            view.addSystemMessage("🔄 Workspace re-indexed after file modification.");
                        } catch (Exception e) {
                            view.addSystemMessage("⚠️ Warning: Failed to re-index workspace: " + e.getMessage());
                        }
                    }
                });

                String replacement = "\n[File write operation requested for: " + filePath + " - awaiting user confirmation]\n";
                matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
            }
        }
        matcher.appendTail(sb);

        return sb.toString();
    }
    
    /**
     * Process DELETE_FILE commands
     */
    private String processDeleteFileCommands(String response, StringBuilder result, FarTechView view, WorkspaceIndexer workspaceIndexer) {
        Matcher matcher = DELETE_FILE_PATTERN.matcher(response);
        StringBuffer sb = new StringBuffer();
        
        while (matcher.find()) {
            String filePath = matcher.group(1).trim();
            
            // Ask for confirmation
            String confirmMessage = "AI wants to DELETE file: " + filePath + "\n\nThis action cannot be undone!\n\nAllow this operation?";
            
            view.showConfirmationDialog(confirmMessage, () -> {
                view.addFileOperationProgress("delete", filePath);
                String deleteResult = deleteFile(filePath);
                boolean success = !deleteResult.startsWith("Error") && !deleteResult.contains("not found");
                view.addFileOperationResult("delete", filePath, success, deleteResult);

                // Re-index the workspace after file changes
                if (workspaceIndexer != null) {
                    try {
                        workspaceIndexer.indexWorkspace();
                        view.addSystemMessage("🔄 Workspace re-indexed after file deletion.");
                    } catch (Exception e) {
                        view.addSystemMessage("⚠️ Warning: Failed to re-index workspace: " + e.getMessage());
                    }
                }
            });
            
            String replacement = "\n[File deletion requested for: " + filePath + " - awaiting user confirmation]\n";
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }
    
    /**
     * Process LIST_FILES commands
     */
    private String processListFilesCommands(String response, StringBuilder result, FarTechView view) {
        Matcher matcher = LIST_FILES_PATTERN.matcher(response);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String dirPath = matcher.group(1).trim();
            String fileList = listFiles(dirPath);

            String replacement = "\n=== DIRECTORY LISTING: " + dirPath + " ===\n" + fileList + "\n=== END LISTING ===\n";
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * Process COPY_FILE commands
     */
    private String processCopyFileCommands(String response, StringBuilder result, FarTechView view, WorkspaceIndexer workspaceIndexer) {
        Matcher matcher = COPY_FILE_PATTERN.matcher(response);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String sourcePath = matcher.group(1).trim();
            String destPath = matcher.group(2).trim();

            // Ask for confirmation
            String confirmMessage = "AI wants to COPY file:\n\nFrom: " + sourcePath + "\nTo: " + destPath + "\n\nAllow this operation?";

            view.showConfirmationDialog(confirmMessage, () -> {
                view.addFileOperationProgress("copy", sourcePath + " -> " + destPath);
                String copyResult = copyFile(sourcePath, destPath);
                boolean success = !copyResult.startsWith("Error") && !copyResult.contains("not found");
                view.addFileOperationResult("copy", sourcePath + " -> " + destPath, success, copyResult);

                // Re-index the workspace after file changes
                if (workspaceIndexer != null) {
                    try {
                        workspaceIndexer.indexWorkspace();
                        view.addSystemMessage("🔄 Workspace re-indexed after file copy.");
                    } catch (Exception e) {
                        view.addSystemMessage("⚠️ Warning: Failed to re-index workspace: " + e.getMessage());
                    }
                }
            });

            String replacement = "\n[File copy operation requested: " + sourcePath + " -> " + destPath + " - awaiting user confirmation]\n";
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * Process MOVE_FILE commands
     */
    private String processMoveFileCommands(String response, StringBuilder result, FarTechView view, WorkspaceIndexer workspaceIndexer) {
        Matcher matcher = MOVE_FILE_PATTERN.matcher(response);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String sourcePath = matcher.group(1).trim();
            String destPath = matcher.group(2).trim();

            // Ask for confirmation
            String confirmMessage = "AI wants to MOVE file:\n\nFrom: " + sourcePath + "\nTo: " + destPath + "\n\nThis will remove the original file!\n\nAllow this operation?";

            view.showConfirmationDialog(confirmMessage, () -> {
                String moveResult = moveFile(sourcePath, destPath);
                view.addSystemMessage("File move result: " + moveResult);

                // Re-index the workspace after file changes
                if (workspaceIndexer != null) {
                    try {
                        workspaceIndexer.indexWorkspace();
                        view.addSystemMessage("Workspace re-indexed after file move.");
                    } catch (Exception e) {
                        view.addSystemMessage("Warning: Failed to re-index workspace: " + e.getMessage());
                    }
                }
            });

            String replacement = "\n[File move operation requested: " + sourcePath + " -> " + destPath + " - awaiting user confirmation]\n";
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * Process RENAME_FILE commands
     */
    private String processRenameFileCommands(String response, StringBuilder result, FarTechView view, WorkspaceIndexer workspaceIndexer) {
        Matcher matcher = RENAME_FILE_PATTERN.matcher(response);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String oldPath = matcher.group(1).trim();
            String newPath = matcher.group(2).trim();

            // Ask for confirmation
            String confirmMessage = "AI wants to RENAME file:\n\nFrom: " + oldPath + "\nTo: " + newPath + "\n\nAllow this operation?";

            view.showConfirmationDialog(confirmMessage, () -> {
                String renameResult = renameFile(oldPath, newPath);
                view.addSystemMessage("File rename result: " + renameResult);

                // Re-index the workspace after file changes
                if (workspaceIndexer != null) {
                    try {
                        workspaceIndexer.indexWorkspace();
                        view.addSystemMessage("Workspace re-indexed after file rename.");
                    } catch (Exception e) {
                        view.addSystemMessage("Warning: Failed to re-index workspace: " + e.getMessage());
                    }
                }
            });

            String replacement = "\n[File rename operation requested: " + oldPath + " -> " + newPath + " - awaiting user confirmation]\n";
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * Process CREATE_DIR commands
     */
    private String processCreateDirCommands(String response, StringBuilder result, FarTechView view) {
        Matcher matcher = CREATE_DIR_PATTERN.matcher(response);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String dirPath = matcher.group(1).trim();

            // Ask for confirmation
            String confirmMessage = "AI wants to CREATE directory:\n\nPath: " + dirPath + "\n\nAllow this operation?";

            view.showConfirmationDialog(confirmMessage, () -> {
                String createResult = createDirectory(dirPath);
                view.addSystemMessage("Directory creation result: " + createResult);
            });

            String replacement = "\n[Directory creation requested: " + dirPath + " - awaiting user confirmation]\n";
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * Process BACKUP_FILE commands
     */
    private String processBackupFileCommands(String response, StringBuilder result, FarTechView view) {
        Matcher matcher = BACKUP_FILE_PATTERN.matcher(response);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String filePath = matcher.group(1).trim();

            // Ask for confirmation
            String confirmMessage = "AI wants to CREATE BACKUP of file:\n\nFile: " + filePath + "\n\nAllow this operation?";

            view.showConfirmationDialog(confirmMessage, () -> {
                view.addFileOperationProgress("backup", filePath);
                String backupResult = createBackup(filePath);
                boolean success = !backupResult.startsWith("Error");
                view.addFileOperationResult("backup", filePath, success, backupResult);
            });

            String replacement = "\n[File backup requested: " + filePath + " - awaiting user confirmation]\n";
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * Process EXEC commands
     */
    private String processExecCommands(String response, StringBuilder result, FarTechView view) {
        String processed = response;
        
        // Process PowerShell with directory
        Matcher dirMatcher = EXEC_POWERSHELL_DIR_PATTERN.matcher(processed);
        StringBuffer sb1 = new StringBuffer();
        while (dirMatcher.find()) {
            String workingDir = dirMatcher.group(1).trim();
            String command = dirMatcher.group(2).trim();
            
            String confirmMessage = "AI wants to execute PowerShell command:\n\nCommand: " + command + 
                                  "\nWorking Directory: " + workingDir + "\n\nAllow this operation?";
            
            view.showConfirmationDialog(confirmMessage, () -> {
                String execResult = executeCommand("powershell", command, workingDir);
                view.addSystemMessage("Command execution result:\n" + execResult);
            });
            
            String replacement = "\n[PowerShell command requested: " + command + " (in " + workingDir + ") - awaiting user confirmation]\n";
            dirMatcher.appendReplacement(sb1, Matcher.quoteReplacement(replacement));
        }
        dirMatcher.appendTail(sb1);
        processed = sb1.toString();
        
        // Process regular PowerShell
        Matcher psMatcher = EXEC_POWERSHELL_PATTERN.matcher(processed);
        StringBuffer sb2 = new StringBuffer();
        while (psMatcher.find()) {
            String command = psMatcher.group(1).trim();
            
            String confirmMessage = "AI wants to execute PowerShell command:\n\nCommand: " + command + "\n\nAllow this operation?";
            
            view.showConfirmationDialog(confirmMessage, () -> {
                String execResult = executeCommand("powershell", command, null);
                view.addSystemMessage("Command execution result:\n" + execResult);
            });
            
            String replacement = "\n[PowerShell command requested: " + command + " - awaiting user confirmation]\n";
            psMatcher.appendReplacement(sb2, Matcher.quoteReplacement(replacement));
        }
        psMatcher.appendTail(sb2);
        processed = sb2.toString();
        
        // Process CMD
        Matcher cmdMatcher = EXEC_CMD_PATTERN.matcher(processed);
        StringBuffer sb3 = new StringBuffer();
        while (cmdMatcher.find()) {
            String command = cmdMatcher.group(1).trim();
            
            String confirmMessage = "AI wants to execute CMD command:\n\nCommand: " + command + "\n\nAllow this operation?";
            
            view.showConfirmationDialog(confirmMessage, () -> {
                String execResult = executeCommand("cmd", command, null);
                view.addSystemMessage("Command execution result:\n" + execResult);
            });
            
            String replacement = "\n[CMD command requested: " + command + " - awaiting user confirmation]\n";
            cmdMatcher.appendReplacement(sb3, Matcher.quoteReplacement(replacement));
        }
        cmdMatcher.appendTail(sb3);
        
        return sb3.toString();
    }
    
    /**
     * Read file content with enhanced path resolution
     */
    private String readFileContent(String filePath) {
        return readFileContentWithIndexer(filePath, null);
    }

    /**
     * Read file content with enhanced path resolution and workspace indexer
     */
    private String readFileContentWithIndexer(String filePath, WorkspaceIndexer indexer) {
        try {
            // Try to resolve the file path using enhanced resolution
            String resolvedPath = resolveFilePathWithIndexer(filePath, indexer);
            if (resolvedPath == null) {
                return generateFileNotFoundSuggestions(filePath, indexer);
            }

            Path path = Paths.get(resolvedPath);
            if (!Files.exists(path)) {
                return generateFileNotFoundSuggestions(filePath, indexer);
            }

            if (Files.isDirectory(path)) {
                return "[Error: Path is a directory, not a file: " + resolvedPath + "]";
            }

            // Check file size (limit to 100KB for safety)
            long size = Files.size(path);
            if (size > 100 * 1024) {
                return "[Error: File too large (" + size + " bytes). Maximum size is 100KB]";
            }

            return new String(Files.readAllBytes(path));

        } catch (Exception e) {
            return "[Error reading file: " + e.getMessage() + "]";
        }
    }

    /**
     * Enhanced file path resolution
     * Handles relative paths, absolute paths, and searches workspace index
     */
    private String resolveFilePath(String filePath) {
        return resolveFilePathWithIndexer(filePath, null);
    }

    /**
     * Enhanced file path resolution with workspace indexer
     */
    private String resolveFilePathWithIndexer(String filePath, WorkspaceIndexer indexer) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return null;
        }

        filePath = filePath.trim();

        // Try as absolute path first
        Path absolutePath = Paths.get(filePath);
        if (absolutePath.isAbsolute() && Files.exists(absolutePath)) {
            return filePath;
        }

        // Try as relative path from current working directory
        Path relativePath = Paths.get(System.getProperty("user.dir"), filePath);
        if (Files.exists(relativePath)) {
            return relativePath.toString();
        }

        // Try to find in workspace index if available
        if (indexer != null) {
            String workspaceMatch = findFileInWorkspace(filePath, indexer);
            if (workspaceMatch != null) {
                return workspaceMatch;
            }
        }

        return null;
    }

    /**
     * Find file in workspace using indexer
     */
    private String findFileInWorkspace(String filePath, WorkspaceIndexer indexer) {
        try {
            // Try exact match first
            String fileContext = indexer.getFileContext(filePath);
            if (!fileContext.startsWith("File not found")) {
                // File found in index, get its absolute path
                return Paths.get(indexer.getWorkspaceRoot(), filePath).toString();
            }

            // Try with different path separators
            String normalizedPath = filePath.replace("\\", "/");
            fileContext = indexer.getFileContext(normalizedPath);
            if (!fileContext.startsWith("File not found")) {
                return Paths.get(indexer.getWorkspaceRoot(), normalizedPath).toString();
            }

        } catch (Exception e) {
            // Ignore errors and continue with other resolution methods
        }

        return null;
    }

    /**
     * Generate helpful suggestions when file is not found
     */
    private String generateFileNotFoundSuggestions(String filePath) {
        return generateFileNotFoundSuggestions(filePath, null);
    }

    /**
     * Generate helpful suggestions when file is not found (with workspace indexer)
     */
    private String generateFileNotFoundSuggestions(String filePath, WorkspaceIndexer indexer) {
        StringBuilder suggestions = new StringBuilder();
        suggestions.append("[Error: File not found: ").append(filePath).append("]\n\n");
        suggestions.append("💡 Suggestions:\n");

        // If we have workspace indexer, show workspace context
        if (indexer != null && indexer.getFileCount() > 0) {
            suggestions.append("📁 Workspace files (").append(indexer.getFileCount()).append(" total):\n");
            String workspaceContext = indexer.getWorkspaceContext();
            if (workspaceContext.contains("Recently Modified Files:")) {
                String[] lines = workspaceContext.split("\n");
                boolean inRecentFiles = false;
                int count = 0;
                for (String line : lines) {
                    if (line.contains("Recently Modified Files:")) {
                        inRecentFiles = true;
                        continue;
                    }
                    if (inRecentFiles && line.startsWith("- ") && count < 5) {
                        suggestions.append("  ").append(line).append("\n");
                        count++;
                    }
                }
            }
            suggestions.append("\n");
        }

        // Try to find similar files in current directory
        try {
            String directory = "."; // Current directory

            // If the path contains directory separators, extract the directory
            if (filePath.contains("/") || filePath.contains("\\")) {
                Path pathObj = Paths.get(filePath);
                if (pathObj.getParent() != null) {
                    directory = pathObj.getParent().toString();
                }
            }

            Path searchDir = Paths.get(directory);
            if (Files.exists(searchDir) && Files.isDirectory(searchDir)) {
                suggestions.append("📁 Files in ").append(directory).append(":\n");

                Files.list(searchDir)
                    .filter(Files::isRegularFile)
                    .limit(10)
                    .forEach(file -> {
                        String name = file.getFileName().toString();
                        suggestions.append("  - ").append(name).append("\n");
                    });
            }

        } catch (Exception e) {
            suggestions.append("  - Check the file path and try again\n");
            suggestions.append("  - Use absolute path if the file is outside current directory\n");
            suggestions.append("  - Make sure the file exists and is readable\n");
        }

        return suggestions.toString();
    }
    
    /**
     * Write file content
     */
    private String writeFileContent(String filePath, String content) {
        try {
            Path path = Paths.get(filePath);
            
            // Create parent directories if they don't exist
            Path parent = path.getParent();
            if (parent != null && !Files.exists(parent)) {
                Files.createDirectories(parent);
            }
            
            Files.write(path, content.getBytes());
            return "Successfully wrote " + content.length() + " characters to " + filePath;
            
        } catch (Exception e) {
            return "Error writing file: " + e.getMessage();
        }
    }
    
    /**
     * Delete file
     */
    private String deleteFile(String filePath) {
        try {
            Path path = Paths.get(filePath);
            if (!Files.exists(path)) {
                return "File not found: " + filePath;
            }
            
            Files.delete(path);
            return "Successfully deleted: " + filePath;
            
        } catch (Exception e) {
            return "Error deleting file: " + e.getMessage();
        }
    }
    
    /**
     * List files in directory
     */
    private String listFiles(String dirPath) {
        try {
            Path path = Paths.get(dirPath);
            if (!Files.exists(path)) {
                return "[Error: Directory not found: " + dirPath + "]";
            }
            
            if (!Files.isDirectory(path)) {
                return "[Error: Path is not a directory: " + dirPath + "]";
            }
            
            StringBuilder result = new StringBuilder();
            Files.list(path).sorted().forEach(file -> {
                try {
                    String name = file.getFileName().toString();
                    if (Files.isDirectory(file)) {
                        result.append("[DIR]  ").append(name).append("/\n");
                    } else {
                        long size = Files.size(file);
                        result.append("[FILE] ").append(name).append(" (").append(size).append(" bytes)\n");
                    }
                } catch (Exception e) {
                    result.append("[ERROR] ").append(file.getFileName()).append(" - ").append(e.getMessage()).append("\n");
                }
            });
            
            return result.toString();
            
        } catch (Exception e) {
            return "[Error listing directory: " + e.getMessage() + "]";
        }
    }
    
    /**
     * Execute command
     */
    private String executeCommand(String shell, String command, String workingDir) {
        try {
            ProcessBuilder pb;
            if ("powershell".equals(shell)) {
                pb = new ProcessBuilder("powershell.exe", "-Command", command);
            } else {
                pb = new ProcessBuilder("cmd.exe", "/c", command);
            }
            
            if (workingDir != null && !workingDir.trim().isEmpty()) {
                pb.directory(new File(workingDir));
            }
            
            pb.redirectErrorStream(true);
            Process process = pb.start();
            
            StringBuilder output = new StringBuilder();
            try (BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    output.append(line).append("\n");
                }
            }
            
            int exitCode = process.waitFor();
            
            String result = output.toString();
            if (result.trim().isEmpty()) {
                result = "(No output)";
            }
            
            return "Exit Code: " + exitCode + "\nOutput:\n" + result;
            
        } catch (Exception e) {
            return "Error executing command: " + e.getMessage();
        }
    }

    /**
     * Copy file from source to destination
     */
    private String copyFile(String sourcePath, String destPath) {
        try {
            Path source = Paths.get(sourcePath);
            Path dest = Paths.get(destPath);

            if (!Files.exists(source)) {
                return "Source file not found: " + sourcePath;
            }

            if (Files.isDirectory(source)) {
                return "Cannot copy directory (use copy_dir for directories): " + sourcePath;
            }

            // Create parent directories if they don't exist
            Path parent = dest.getParent();
            if (parent != null && !Files.exists(parent)) {
                Files.createDirectories(parent);
            }

            // Check if destination exists and warn
            if (Files.exists(dest)) {
                Files.copy(source, dest, StandardCopyOption.REPLACE_EXISTING);
                return "Successfully copied " + sourcePath + " to " + destPath + " (overwrote existing file)";
            } else {
                Files.copy(source, dest);
                return "Successfully copied " + sourcePath + " to " + destPath;
            }

        } catch (Exception e) {
            return "Error copying file: " + e.getMessage();
        }
    }

    /**
     * Move file from source to destination
     */
    private String moveFile(String sourcePath, String destPath) {
        try {
            Path source = Paths.get(sourcePath);
            Path dest = Paths.get(destPath);

            if (!Files.exists(source)) {
                return "Source file not found: " + sourcePath;
            }

            if (Files.isDirectory(source)) {
                return "Cannot move directory (use move_dir for directories): " + sourcePath;
            }

            // Create parent directories if they don't exist
            Path parent = dest.getParent();
            if (parent != null && !Files.exists(parent)) {
                Files.createDirectories(parent);
            }

            // Check if destination exists and warn
            if (Files.exists(dest)) {
                Files.move(source, dest, StandardCopyOption.REPLACE_EXISTING);
                return "Successfully moved " + sourcePath + " to " + destPath + " (overwrote existing file)";
            } else {
                Files.move(source, dest);
                return "Successfully moved " + sourcePath + " to " + destPath;
            }

        } catch (Exception e) {
            return "Error moving file: " + e.getMessage();
        }
    }

    /**
     * Rename file
     */
    private String renameFile(String oldPath, String newPath) {
        try {
            Path source = Paths.get(oldPath);
            Path dest = Paths.get(newPath);

            if (!Files.exists(source)) {
                return "File not found: " + oldPath;
            }

            // Create parent directories if they don't exist
            Path parent = dest.getParent();
            if (parent != null && !Files.exists(parent)) {
                Files.createDirectories(parent);
            }

            // Check if destination exists
            if (Files.exists(dest)) {
                return "Cannot rename: destination already exists: " + newPath;
            }

            Files.move(source, dest);
            return "Successfully renamed " + oldPath + " to " + newPath;

        } catch (Exception e) {
            return "Error renaming file: " + e.getMessage();
        }
    }

    /**
     * Create directory
     */
    private String createDirectory(String dirPath) {
        try {
            Path path = Paths.get(dirPath);

            if (Files.exists(path)) {
                if (Files.isDirectory(path)) {
                    return "Directory already exists: " + dirPath;
                } else {
                    return "Cannot create directory: file with same name exists: " + dirPath;
                }
            }

            Files.createDirectories(path);
            return "Successfully created directory: " + dirPath;

        } catch (Exception e) {
            return "Error creating directory: " + e.getMessage();
        }
    }

    /**
     * Create backup of a file
     */
    private String createBackup(String filePath) {
        try {
            Path source = Paths.get(filePath);
            if (!Files.exists(source)) {
                return "Error: File not found: " + filePath;
            }

            if (Files.isDirectory(source)) {
                return "Error: Cannot backup directory: " + filePath;
            }

            // Create backup directory if it doesn't exist
            Path backupDir = Paths.get(BACKUP_DIR);
            if (!Files.exists(backupDir)) {
                Files.createDirectories(backupDir);
            }

            // Generate backup filename with timestamp
            String timestamp = java.time.LocalDateTime.now()
                .format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = source.getFileName().toString();
            String backupFileName = fileName + ".backup_" + timestamp;
            Path backupPath = backupDir.resolve(backupFileName);

            // Copy file to backup location
            Files.copy(source, backupPath);

            return "Backup created: " + backupPath.toString();

        } catch (Exception e) {
            return "Error creating backup: " + e.getMessage();
        }
    }

    /**
     * List available backups for a file
     */
    private String listBackups(String filePath) {
        try {
            Path backupDir = Paths.get(BACKUP_DIR);
            if (!Files.exists(backupDir)) {
                return "No backup directory found";
            }

            String fileName = Paths.get(filePath).getFileName().toString();
            StringBuilder result = new StringBuilder();
            result.append("Available backups for ").append(fileName).append(":\n");

            Files.list(backupDir)
                .filter(path -> path.getFileName().toString().startsWith(fileName + ".backup_"))
                .sorted((a, b) -> b.getFileName().toString().compareTo(a.getFileName().toString())) // Latest first
                .forEach(backup -> {
                    try {
                        long size = Files.size(backup);
                        String lastModified = Files.getLastModifiedTime(backup).toString();
                        result.append("- ").append(backup.getFileName())
                              .append(" (").append(size).append(" bytes, ")
                              .append(lastModified).append(")\n");
                    } catch (Exception e) {
                        result.append("- ").append(backup.getFileName()).append(" (error reading info)\n");
                    }
                });

            return result.toString();

        } catch (Exception e) {
            return "Error listing backups: " + e.getMessage();
        }
    }
}
