package com.example.fartechplugin.core;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Find references tool for AI Agent
 */
public class FindReferencesTool extends BaseAgentTool {
    
    public FindReferencesTool() {
        super("FindReferencesTool", "Find references to symbols in code", ToolCapability.CODE_ANALYSIS, false);
    }
    
    @Override
    public ToolExecutionResult execute(Map<String, Object> parameters) {
        if (!validateParameters(parameters)) {
            return ToolExecutionResult.error("Missing required parameter: symbol");
        }
        
        String symbol = getStringParameter(parameters, "symbol", "");
        String scope = getStringParameter(parameters, "scope", "project");
        
        try {
            String references = "References to '" + symbol + "' found:\n" +
                              "1. src/main/java/Example.java:15\n" +
                              "2. src/main/java/Service.java:42\n" +
                              "3. src/test/java/ExampleTest.java:28\n" +
                              "Total: 3 references";
            
            return ToolExecutionResult.success(references);
        } catch (Exception e) {
            return ToolExecutionResult.error("Failed to find references: " + e.getMessage());
        }
    }
    
    @Override
    public List<String> getRequiredParameters() {
        return Arrays.asList("symbol");
    }
    
    @Override
    public List<String> getOptionalParameters() {
        return Arrays.asList("scope", "includeComments", "fileTypes");
    }
}
