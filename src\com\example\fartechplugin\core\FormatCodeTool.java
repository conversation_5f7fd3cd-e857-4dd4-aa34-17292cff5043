package com.example.fartechplugin.core;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Format code tool for AI Agent
 */
public class FormatCodeTool extends BaseAgentTool {
    
    public FormatCodeTool() {
        super("FormatCodeTool", "Format code according to standards", ToolCapability.CODE_MODIFICATION, false);
    }
    
    @Override
    public ToolExecutionResult execute(Map<String, Object> parameters) {
        if (!validateParameters(parameters)) {
            return ToolExecutionResult.error("Missing required parameter: filePath");
        }
        
        String filePath = getStringParameter(parameters, "filePath", "");
        String style = getStringParameter(parameters, "style", "default");
        
        try {
            String result = "Code formatted successfully in " + filePath + " using " + style + " style";
            return ToolExecutionResult.success(result);
        } catch (Exception e) {
            return ToolExecutionResult.error("Code formatting failed: " + e.getMessage());
        }
    }
    
    @Override
    public List<String> getRequiredParameters() {
        return Arrays.asList("filePath");
    }
    
    @Override
    public List<String> getOptionalParameters() {
        return Arrays.asList("style", "indentSize", "lineLength");
    }
}
