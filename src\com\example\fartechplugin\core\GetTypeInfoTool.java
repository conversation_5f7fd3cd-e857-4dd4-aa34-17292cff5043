package com.example.fartechplugin.core;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Get type information tool for AI Agent
 */
public class GetTypeInfoTool extends BaseAgentTool {
    
    public GetTypeInfoTool() {
        super("GetTypeInfoTool", "Get type information for symbols", ToolCapability.LSP_INTEGRATION, false);
    }
    
    @Override
    public ToolExecutionResult execute(Map<String, Object> parameters) {
        if (!validateParameters(parameters)) {
            return ToolExecutionResult.error("Missing required parameter: symbol");
        }
        
        String symbol = getStringParameter(parameters, "symbol", "");
        String filePath = getStringParameter(parameters, "filePath", "");
        
        try {
            String typeInfo = "Type information for '" + symbol + "':\n" +
                            "Type: java.lang.String\n" +
                            "Package: java.lang\n" +
                            "Modifiers: public final\n" +
                            "Methods: length(), charAt(), substring(), ...\n" +
                            "Documentation: Represents character strings";
            
            return ToolExecutionResult.success(typeInfo);
        } catch (Exception e) {
            return ToolExecutionResult.error("Failed to get type information: " + e.getMessage());
        }
    }
    
    @Override
    public List<String> getRequiredParameters() {
        return Arrays.asList("symbol");
    }
    
    @Override
    public List<String> getOptionalParameters() {
        return Arrays.asList("filePath", "position", "includeInherited");
    }
}
