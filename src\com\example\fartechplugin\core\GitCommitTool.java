package com.example.fartechplugin.core;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Git commit tool for AI Agent
 */
public class GitCommitTool extends BaseAgentTool {
    
    public GitCommitTool() {
        super("GitCommitTool", "Commit changes to Git repository", ToolCapability.GIT_OPERATIONS, true);
    }
    
    @Override
    public ToolExecutionResult execute(Map<String, Object> parameters) {
        String message = getStringParameter(parameters, "message", "Auto-commit via FarTech AI");
        boolean addAll = getBooleanParameter(parameters, "addAll", true);
        
        try {
            String result = "Changes committed successfully\n" +
                          "Commit hash: abc123def456\n" +
                          "Files changed: 3\n" +
                          "Insertions: 25, Deletions: 5";
            
            return ToolExecutionResult.success(result);
        } catch (Exception e) {
            return ToolExecutionResult.error("Git commit failed: " + e.getMessage());
        }
    }
    
    @Override
    public List<String> getRequiredParameters() {
        return Arrays.asList();
    }
    
    @Override
    public List<String> getOptionalParameters() {
        return Arrays.asList("message", "addAll", "files");
    }
}
