package com.example.fartechplugin.core;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Git push tool for AI Agent
 */
public class GitPushTool extends BaseAgentTool {
    
    public GitPushTool() {
        super("GitPushTool", "Push commits to remote repository", ToolCapability.GIT_OPERATIONS, true);
    }
    
    @Override
    public ToolExecutionResult execute(Map<String, Object> parameters) {
        String remote = getStringParameter(parameters, "remote", "origin");
        String branch = getStringParameter(parameters, "branch", "main");
        
        try {
            String result = "Push completed successfully\n" +
                          "Remote: " + remote + "\n" +
                          "Branch: " + branch + "\n" +
                          "Commits pushed: 1";
            
            return ToolExecutionResult.success(result);
        } catch (Exception e) {
            return ToolExecutionResult.error("Git push failed: " + e.getMessage());
        }
    }
    
    @Override
    public List<String> getRequiredParameters() {
        return Arrays.asList();
    }
    
    @Override
    public List<String> getOptionalParameters() {
        return Arrays.asList("remote", "branch", "force");
    }
}
