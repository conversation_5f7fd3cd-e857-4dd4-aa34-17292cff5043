package com.example.fartechplugin.core;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Git status tool for AI Agent
 */
public class GitStatusTool extends BaseAgentTool {
    
    public GitStatusTool() {
        super("GitStatusTool", "Get Git repository status", ToolCapability.GIT_OPERATIONS, false);
    }
    
    @Override
    public ToolExecutionResult execute(Map<String, Object> parameters) {
        try {
            String status = "On branch main\nYour branch is up to date with 'origin/main'.\n\n" +
                          "Changes not staged for commit:\n" +
                          "  modified:   src/main/java/Example.java\n" +
                          "  modified:   README.md\n\n" +
                          "Untracked files:\n" +
                          "  src/test/java/NewTest.java";
            
            return ToolExecutionResult.success(status);
        } catch (Exception e) {
            return ToolExecutionResult.error("Git status failed: " + e.getMessage());
        }
    }
    
    @Override
    public List<String> getRequiredParameters() {
        return Arrays.asList();
    }
    
    @Override
    public List<String> getOptionalParameters() {
        return Arrays.asList("repository");
    }
}
