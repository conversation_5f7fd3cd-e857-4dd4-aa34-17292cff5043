package com.example.fartechplugin.core;

import java.util.*;

/**
 * Represents the current IDE context
 */
public class IDEContext {
    private String currentFile;
    private String selectedText;
    private String projectRoot;
    private List<String> openFiles;
    private String terminalOutput;
    private Map<String, Object> debuggerState;
    private String gitStatus;
    private Map<String, String> environmentVariables;
    
    public IDEContext() {
        this.openFiles = new ArrayList<>();
        this.debuggerState = new HashMap<>();
        this.environmentVariables = new HashMap<>();
    }
    
    // Getters and setters
    public String getCurrentFile() { return currentFile; }
    public void setCurrentFile(String currentFile) { this.currentFile = currentFile; }
    
    public String getSelectedText() { return selectedText; }
    public void setSelectedText(String selectedText) { this.selectedText = selectedText; }
    
    public String getProjectRoot() { return projectRoot; }
    public void setProjectRoot(String projectRoot) { this.projectRoot = projectRoot; }
    
    public List<String> getOpenFiles() { return new ArrayList<>(openFiles); }
    public void setOpenFiles(List<String> openFiles) { this.openFiles = new ArrayList<>(openFiles); }
    
    public String getTerminalOutput() { return terminalOutput; }
    public void setTerminalOutput(String terminalOutput) { this.terminalOutput = terminalOutput; }
    
    public Map<String, Object> getDebuggerState() { return new HashMap<>(debuggerState); }
    public void setDebuggerState(Map<String, Object> debuggerState) { this.debuggerState = new HashMap<>(debuggerState); }
    
    public String getGitStatus() { return gitStatus; }
    public void setGitStatus(String gitStatus) { this.gitStatus = gitStatus; }
    
    public Map<String, String> getEnvironmentVariables() { return new HashMap<>(environmentVariables); }
    public void setEnvironmentVariables(Map<String, String> environmentVariables) { this.environmentVariables = new HashMap<>(environmentVariables); }
}
