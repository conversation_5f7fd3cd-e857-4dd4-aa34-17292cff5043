package com.example.fartechplugin.core;

import java.util.*;

/**
 * Configuration class for workspace indexing preferences
 */
public class IndexingConfiguration {
    
    // Default settings
    public static final long DEFAULT_MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
    public static final int DEFAULT_MAX_DEPTH = 10;
    public static final boolean DEFAULT_AUTO_INDEX = true;
    public static final boolean DEFAULT_INCREMENTAL_INDEX = true;
    
    // File size limits
    private long maxFileSize = DEFAULT_MAX_FILE_SIZE;
    
    // Directory traversal limits
    private int maxDepth = DEFAULT_MAX_DEPTH;
    
    // Auto-indexing settings
    private boolean autoIndexOnStartup = DEFAULT_AUTO_INDEX;
    private boolean incrementalIndexing = DEFAULT_INCREMENTAL_INDEX;
    private boolean backgroundIndexing = true;
    
    // File type preferences
    private Set<String> includedExtensions;
    private Set<String> excludedExtensions;
    private boolean useWhitelist = true; // true = include only specified, false = exclude specified
    
    // Directory exclusions
    private Set<String> excludedDirectories;
    private Set<String> excludedPatterns;
    
    // Performance settings
    private int indexingThreads = 1;
    private long indexingDelay = 2000; // ms to wait before auto-indexing
    
    public IndexingConfiguration() {
        initializeDefaults();
    }
    
    private void initializeDefaults() {
        // Default included file extensions (comprehensive list)
        includedExtensions = new HashSet<>();
        Collections.addAll(includedExtensions,
            // Programming languages
            ".java", ".js", ".ts", ".jsx", ".tsx", ".py", ".cpp", ".c", ".h", ".hpp",
            ".cs", ".php", ".rb", ".go", ".rs", ".kt", ".scala", ".swift", ".dart",
            ".r", ".m", ".mm", ".pl", ".sh", ".bat", ".ps1", ".vb", ".vbs",

            // Web technologies
            ".html", ".htm", ".css", ".scss", ".sass", ".less", ".xml", ".xsl", ".xsd",
            ".json", ".yaml", ".yml", ".toml", ".ini", ".conf", ".config",

            // Documentation and text
            ".md", ".txt", ".rst", ".adoc", ".tex", ".rtf",

            // Configuration files
            ".properties", ".env", ".gitignore", ".gitattributes", ".editorconfig",
            ".eslintrc", ".prettierrc", ".babelrc", ".tsconfig", ".jsconfig",

            // Build and project files
            ".gradle", ".maven", ".pom", ".sbt", ".cmake", ".make", ".dockerfile",
            ".docker-compose", ".vagrantfile", ".ansible", ".terraform",

            // Database
            ".sql", ".ddl", ".dml", ".hql", ".cypher",

            // Markup and templates
            ".jsp", ".jspx", ".ftl", ".vm", ".mustache", ".hbs", ".twig", ".blade",

            // Eclipse and IDE specific files (IMPORTANT FOR YOUR WORKSPACE!)
            ".project", ".classpath", ".factorypath", ".settings",

            // Files without extensions (common in projects)
            "", "LICENSE", "README", "CHANGELOG", "MANIFEST", "Dockerfile", "Makefile"
        );
        
        // Default excluded extensions
        excludedExtensions = new HashSet<>();
        Collections.addAll(excludedExtensions,
            // Binary files
            ".exe", ".dll", ".so", ".dylib", ".bin", ".obj", ".o", ".a", ".lib",
            
            // Archives
            ".zip", ".rar", ".7z", ".tar", ".gz", ".bz2", ".xz",
            
            // Images
            ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".svg", ".ico", ".webp",
            
            // Audio/Video
            ".mp3", ".mp4", ".avi", ".mov", ".wmv", ".flv", ".wav", ".ogg",
            
            // Documents (binary formats)
            ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx"
        );
        
        // Default excluded directories (more permissive for Eclipse projects)
        excludedDirectories = new HashSet<>();
        Collections.addAll(excludedDirectories,
            // Version control
            ".git", ".svn", ".hg", ".bzr",

            // Build outputs (but NOT bin - Eclipse uses bin for important files)
            "target", "build", "dist", "out", "obj",

            // Dependencies
            "node_modules", "vendor", "packages", ".m2", ".gradle",

            // IDE files (but NOT .settings - Eclipse uses this)
            ".idea", ".vscode",

            // Temporary files
            "tmp", "temp", ".tmp", ".cache", "cache",

            // Logs
            "logs", "log"
        );
        
        // Default excluded patterns (regex)
        excludedPatterns = new HashSet<>();
        Collections.addAll(excludedPatterns,
            ".*\\.min\\.(js|css)$", // Minified files
            ".*\\.bundle\\.(js|css)$", // Bundle files
            ".*\\.generated\\.(java|cs|py)$", // Generated files
            ".*\\.backup$", // Backup files
            ".*~$", // Temporary files
            ".*\\.swp$", // Vim swap files
            ".*\\.DS_Store$" // macOS files
        );
    }
    
    // Getters and setters
    public long getMaxFileSize() { return maxFileSize; }
    public void setMaxFileSize(long maxFileSize) { this.maxFileSize = maxFileSize; }
    
    public int getMaxDepth() { return maxDepth; }
    public void setMaxDepth(int maxDepth) { this.maxDepth = maxDepth; }
    
    public boolean isAutoIndexOnStartup() { return autoIndexOnStartup; }
    public void setAutoIndexOnStartup(boolean autoIndexOnStartup) { this.autoIndexOnStartup = autoIndexOnStartup; }
    
    public boolean isIncrementalIndexing() { return incrementalIndexing; }
    public void setIncrementalIndexing(boolean incrementalIndexing) { this.incrementalIndexing = incrementalIndexing; }
    
    public boolean isBackgroundIndexing() { return backgroundIndexing; }
    public void setBackgroundIndexing(boolean backgroundIndexing) { this.backgroundIndexing = backgroundIndexing; }
    
    public Set<String> getIncludedExtensions() { return new HashSet<>(includedExtensions); }
    public void setIncludedExtensions(Set<String> includedExtensions) { this.includedExtensions = new HashSet<>(includedExtensions); }
    
    public Set<String> getExcludedExtensions() { return new HashSet<>(excludedExtensions); }
    public void setExcludedExtensions(Set<String> excludedExtensions) { this.excludedExtensions = new HashSet<>(excludedExtensions); }
    
    public boolean isUseWhitelist() { return useWhitelist; }
    public void setUseWhitelist(boolean useWhitelist) { this.useWhitelist = useWhitelist; }
    
    public Set<String> getExcludedDirectories() { return new HashSet<>(excludedDirectories); }
    public void setExcludedDirectories(Set<String> excludedDirectories) { this.excludedDirectories = new HashSet<>(excludedDirectories); }
    
    public Set<String> getExcludedPatterns() { return new HashSet<>(excludedPatterns); }
    public void setExcludedPatterns(Set<String> excludedPatterns) { this.excludedPatterns = new HashSet<>(excludedPatterns); }
    
    public int getIndexingThreads() { return indexingThreads; }
    public void setIndexingThreads(int indexingThreads) { this.indexingThreads = Math.max(1, indexingThreads); }
    
    public long getIndexingDelay() { return indexingDelay; }
    public void setIndexingDelay(long indexingDelay) { this.indexingDelay = Math.max(0, indexingDelay); }
    
    /**
     * Check if a file extension should be indexed
     */
    public boolean shouldIndexExtension(String extension) {
        if (extension == null || extension.isEmpty()) {
            return false;
        }
        
        String ext = extension.toLowerCase();
        if (useWhitelist) {
            return includedExtensions.contains(ext);
        } else {
            return !excludedExtensions.contains(ext);
        }
    }
    
    /**
     * Check if a directory should be excluded
     */
    public boolean shouldExcludeDirectory(String directoryName) {
        if (directoryName == null || directoryName.isEmpty()) {
            return false;
        }
        
        String dirName = directoryName.toLowerCase();
        return excludedDirectories.contains(dirName);
    }
    
    /**
     * Check if a file path matches any excluded pattern
     */
    public boolean matchesExcludedPattern(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return false;
        }
        
        for (String pattern : excludedPatterns) {
            if (filePath.matches(pattern)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get human-readable file size limit
     */
    public String getMaxFileSizeFormatted() {
        if (maxFileSize < 1024) {
            return maxFileSize + " bytes";
        } else if (maxFileSize < 1024 * 1024) {
            return (maxFileSize / 1024) + " KB";
        } else {
            return (maxFileSize / (1024 * 1024)) + " MB";
        }
    }
}
