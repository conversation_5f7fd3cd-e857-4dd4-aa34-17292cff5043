package com.example.fartechplugin.core;

import java.util.*;

/**
 * Manages user-friendly feedback and suggestions for workspace indexing
 */
public class IndexingFeedbackManager {
    
    private final WorkspaceIndexer indexer;
    private final IndexingConfiguration config;
    
    public IndexingFeedbackManager(WorkspaceIndexer indexer) {
        this.indexer = indexer;
        this.config = indexer.getConfiguration();
    }
    
    /**
     * Generate comprehensive indexing report with suggestions
     */
    public String generateIndexingReport() {
        IndexingStatistics stats = indexer.getStatistics();
        StringBuilder report = new StringBuilder();
        
        report.append("🔍 FARTECH AI WORKSPACE INDEXING REPORT\n");
        report.append("=====================================\n\n");
        
        // Basic statistics
        report.append("📊 INDEXING RESULTS:\n");
        report.append(String.format("✅ Files successfully indexed: %d\n", stats.getTotalFiles()));
        report.append(String.format("📁 Directories scanned: %d\n", stats.getTotalDirectories()));
        report.append(String.format("⏭️ Files skipped: %d\n", stats.getSkippedFiles()));
        report.append(String.format("❌ Files with errors: %d\n", stats.getErrorFiles()));
        report.append(String.format("📈 Success rate: %.1f%%\n", stats.getSuccessRate()));
        report.append(String.format("🕒 Last indexed: %s\n\n", stats.getLastIndexTimeFormatted()));
        
        // Configuration summary
        report.append("⚙️ CURRENT CONFIGURATION:\n");
        report.append(String.format("📏 Max file size: %s\n", config.getMaxFileSizeFormatted()));
        report.append(String.format("🔢 Max directory depth: %d\n", config.getMaxDepth()));
        report.append(String.format("🔄 Auto-indexing: %s\n", config.isAutoIndexOnStartup() ? "Enabled" : "Disabled"));
        report.append(String.format("📈 Incremental indexing: %s\n", config.isIncrementalIndexing() ? "Enabled" : "Disabled"));
        report.append(String.format("🎯 File extensions: %d types supported\n\n", config.getIncludedExtensions().size()));
        
        // Skip reasons analysis
        if (stats.getSkippedFiles() > 0) {
            report.append("📋 FILES SKIPPED BY REASON:\n");
            stats.getSkippedReasons().entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .forEach(entry -> report.append(String.format("  • %s: %d files\n", 
                                                             entry.getKey(), entry.getValue())));
            report.append("\n");
        }
        
        // Performance analysis and suggestions
        report.append(generatePerformanceSuggestions(stats));
        
        // Configuration recommendations
        report.append(generateConfigurationSuggestions(stats));
        
        // Troubleshooting section
        if (stats.getTotalFiles() == 0 || stats.getErrorFiles() > 0) {
            report.append(generateTroubleshootingGuide(stats));
        }
        
        return report.toString();
    }
    
    /**
     * Generate performance suggestions based on statistics
     */
    private String generatePerformanceSuggestions(IndexingStatistics stats) {
        StringBuilder suggestions = new StringBuilder();
        suggestions.append("🚀 PERFORMANCE SUGGESTIONS:\n");
        
        if (stats.getSkippedFiles() > stats.getTotalFiles()) {
            suggestions.append("⚡ Many files are being skipped. Consider:\n");
            suggestions.append("  • Reviewing file type filters\n");
            suggestions.append("  • Increasing max file size limit\n");
            suggestions.append("  • Checking directory exclusions\n\n");
        }
        
        if (stats.getErrorFiles() > 0) {
            suggestions.append("⚠️ Some files had errors during indexing:\n");
            suggestions.append("  • Check file permissions\n");
            suggestions.append("  • Verify file encoding (UTF-8 recommended)\n");
            suggestions.append("  • Look for corrupted or locked files\n\n");
        }
        
        if (stats.getTotalFiles() > 10000) {
            suggestions.append("📈 Large workspace detected:\n");
            suggestions.append("  • Consider enabling incremental indexing\n");
            suggestions.append("  • Add more directory exclusions for build outputs\n");
            suggestions.append("  • Increase file size limits if needed\n\n");
        }
        
        if (stats.getSuccessRate() < 80.0) {
            suggestions.append("🎯 Low success rate detected:\n");
            suggestions.append("  • Review configuration settings\n");
            suggestions.append("  • Check workspace path accessibility\n");
            suggestions.append("  • Consider adjusting file filters\n\n");
        }
        
        return suggestions.toString();
    }
    
    /**
     * Generate configuration suggestions
     */
    private String generateConfigurationSuggestions(IndexingStatistics stats) {
        StringBuilder suggestions = new StringBuilder();
        suggestions.append("⚙️ CONFIGURATION RECOMMENDATIONS:\n");
        
        // File size recommendations
        if (stats.getSkippedReasons().getOrDefault("file too large", 0) > 10) {
            suggestions.append("📏 Consider increasing max file size:\n");
            suggestions.append(String.format("  • Current limit: %s\n", config.getMaxFileSizeFormatted()));
            suggestions.append("  • Suggested: 10MB for large codebases\n");
            suggestions.append("  • Or add specific large file exclusions\n\n");
        }
        
        // Extension recommendations
        if (stats.getSkippedReasons().getOrDefault("unsupported extension", 0) > 5) {
            suggestions.append("📄 Consider adding more file types:\n");
            suggestions.append("  • Review skipped extensions in console logs\n");
            suggestions.append("  • Add project-specific file types\n");
            suggestions.append("  • Consider custom configuration files\n\n");
        }
        
        // Directory recommendations
        if (stats.getTotalDirectories() > 1000) {
            suggestions.append("📁 Large directory structure detected:\n");
            suggestions.append("  • Add more build output exclusions\n");
            suggestions.append("  • Exclude test data directories\n");
            suggestions.append("  • Consider reducing max depth\n\n");
        }
        
        return suggestions.toString();
    }
    
    /**
     * Generate troubleshooting guide for common issues
     */
    private String generateTroubleshootingGuide(IndexingStatistics stats) {
        StringBuilder guide = new StringBuilder();
        guide.append("🔧 TROUBLESHOOTING GUIDE:\n");
        
        if (stats.getTotalFiles() == 0) {
            guide.append("❌ NO FILES INDEXED - Possible causes:\n");
            guide.append("  1. Workspace path issue:\n");
            guide.append(String.format("     • Verify path exists: %s\n", indexer.getWorkspaceRoot()));
            guide.append("     • Check Eclipse workspace location\n");
            guide.append("     • Ensure proper file permissions\n\n");
            
            guide.append("  2. Configuration too restrictive:\n");
            guide.append(String.format("     • Max file size: %s\n", config.getMaxFileSizeFormatted()));
            guide.append(String.format("     • Max depth: %d levels\n", config.getMaxDepth()));
            guide.append("     • File type filters may be too narrow\n\n");
            
            guide.append("  3. All files filtered out:\n");
            guide.append("     • Check directory exclusions\n");
            guide.append("     • Review file extension whitelist\n");
            guide.append("     • Look for hidden file patterns\n\n");
        }
        
        if (stats.getErrorFiles() > stats.getTotalFiles() * 0.1) {
            guide.append("⚠️ HIGH ERROR RATE - Common solutions:\n");
            guide.append("  • File permission issues:\n");
            guide.append("    - Run Eclipse as administrator (if needed)\n");
            guide.append("    - Check file/directory permissions\n");
            guide.append("    - Verify workspace is not read-only\n\n");
            
            guide.append("  • File encoding problems:\n");
            guide.append("    - Ensure files are UTF-8 encoded\n");
            guide.append("    - Check for binary files with text extensions\n");
            guide.append("    - Look for corrupted files\n\n");
        }
        
        guide.append("🆘 GETTING HELP:\n");
        guide.append("  • Check Eclipse Error Log (Window > Show View > Error Log)\n");
        guide.append("  • Review Eclipse Console output for detailed logs\n");
        guide.append("  • Try manual indexing with 'Index Workspace' button\n");
        guide.append("  • Restart Eclipse if issues persist\n\n");
        
        return guide.toString();
    }
    
    /**
     * Generate quick status message for UI
     */
    public String getQuickStatus() {
        IndexingStatistics stats = indexer.getStatistics();
        
        if (indexer.isIndexing()) {
            return "🔄 Indexing in progress...";
        }
        
        if (stats.getTotalFiles() == 0) {
            return "⚠️ No files indexed - check configuration";
        }
        
        if (stats.getSuccessRate() < 50.0) {
            return String.format("❌ Low success rate: %.1f%% - needs attention", stats.getSuccessRate());
        }
        
        if (stats.getSuccessRate() < 90.0) {
            return String.format("⚠️ Partial success: %d files indexed (%.1f%%)", 
                               stats.getTotalFiles(), stats.getSuccessRate());
        }
        
        return String.format("✅ %d files indexed successfully (%.1f%%)", 
                           stats.getTotalFiles(), stats.getSuccessRate());
    }
    
    /**
     * Get actionable suggestions for improving indexing
     */
    public List<String> getActionableSuggestions() {
        IndexingStatistics stats = indexer.getStatistics();
        List<String> suggestions = new ArrayList<>();
        
        if (stats.getTotalFiles() == 0) {
            suggestions.add("Check workspace path: " + indexer.getWorkspaceRoot());
            suggestions.add("Verify file permissions and accessibility");
            suggestions.add("Review file type and size filters");
        }
        
        if (stats.getSkippedFiles() > stats.getTotalFiles()) {
            suggestions.add("Consider relaxing file size limits");
            suggestions.add("Review and expand supported file extensions");
            suggestions.add("Check directory exclusion rules");
        }
        
        if (stats.getErrorFiles() > 0) {
            suggestions.add("Check file permissions and encoding");
            suggestions.add("Look for corrupted or locked files");
            suggestions.add("Review Eclipse Error Log for details");
        }
        
        if (!config.isIncrementalIndexing() && stats.getTotalFiles() > 1000) {
            suggestions.add("Enable incremental indexing for better performance");
        }
        
        return suggestions;
    }
}
