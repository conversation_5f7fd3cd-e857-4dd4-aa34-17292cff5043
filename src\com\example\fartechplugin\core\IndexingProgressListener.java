package com.example.fartechplugin.core;

/**
 * Interface for listening to workspace indexing progress updates
 */
public interface IndexingProgressListener {
    
    /**
     * Called when indexing starts
     * @param totalEstimatedFiles Estimated number of files to process
     */
    void onIndexingStarted(int totalEstimatedFiles);
    
    /**
     * Called during indexing progress
     * @param currentFile Currently processing file path
     * @param filesProcessed Number of files processed so far
     * @param totalFiles Total number of files found
     * @param percentage Completion percentage (0-100)
     */
    void onIndexingProgress(String currentFile, int filesProcessed, int totalFiles, int percentage);
    
    /**
     * Called when a directory is being scanned
     * @param directoryPath Path of directory being scanned
     * @param depth Current directory depth
     */
    void onDirectoryScanning(String directoryPath, int depth);
    
    /**
     * Called when indexing completes successfully
     * @param totalFiles Total files indexed
     * @param totalDirectories Total directories indexed
     * @param durationMs Time taken in milliseconds
     */
    void onIndexingCompleted(int totalFiles, int totalDirectories, long durationMs);
    
    /**
     * Called when indexing fails
     * @param error Error message
     * @param exception Exception that caused the failure (can be null)
     */
    void onIndexingFailed(String error, Exception exception);
    
    /**
     * Called when a file is skipped during indexing
     * @param filePath Path of skipped file
     * @param reason Reason for skipping (e.g., "unsupported extension", "too large", "filtered")
     */
    void onFileSkipped(String filePath, String reason);
    
    /**
     * Called when indexing is cancelled by user
     */
    void onIndexingCancelled();
}
