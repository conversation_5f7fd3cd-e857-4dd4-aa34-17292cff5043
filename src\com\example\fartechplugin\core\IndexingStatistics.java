package com.example.fartechplugin.core;

import java.util.Date;
import java.util.Map;

/**
 * Statistics about workspace indexing operation
 */
public class IndexingStatistics {
    
    private final int totalFiles;
    private final int totalDirectories;
    private final int skippedFiles;
    private final int errorFiles;
    private final long lastIndexTime;
    private final Map<String, Integer> skippedReasons;
    
    public IndexingStatistics(int totalFiles, int totalDirectories, int skippedFiles, 
                            int errorFiles, long lastIndexTime, Map<String, Integer> skippedReasons) {
        this.totalFiles = totalFiles;
        this.totalDirectories = totalDirectories;
        this.skippedFiles = skippedFiles;
        this.errorFiles = errorFiles;
        this.lastIndexTime = lastIndexTime;
        this.skippedReasons = skippedReasons;
    }
    
    public int getTotalFiles() {
        return totalFiles;
    }
    
    public int getTotalDirectories() {
        return totalDirectories;
    }
    
    public int getSkippedFiles() {
        return skippedFiles;
    }
    
    public int getErrorFiles() {
        return errorFiles;
    }
    
    public long getLastIndexTime() {
        return lastIndexTime;
    }
    
    public Map<String, Integer> getSkippedReasons() {
        return skippedReasons;
    }
    
    public String getLastIndexTimeFormatted() {
        return lastIndexTime > 0 ? new Date(lastIndexTime).toString() : "Never";
    }
    
    public int getProcessedFiles() {
        return totalFiles + skippedFiles + errorFiles;
    }
    
    public double getSuccessRate() {
        int processed = getProcessedFiles();
        return processed > 0 ? (double) totalFiles / processed * 100.0 : 0.0;
    }
    
    /**
     * Get a human-readable summary of indexing statistics
     */
    public String getSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("=== INDEXING STATISTICS ===\n");
        summary.append("✅ Files indexed: ").append(totalFiles).append("\n");
        summary.append("📁 Directories indexed: ").append(totalDirectories).append("\n");
        summary.append("⏭️ Files skipped: ").append(skippedFiles).append("\n");
        summary.append("❌ Files with errors: ").append(errorFiles).append("\n");
        summary.append("📊 Success rate: ").append(String.format("%.1f%%", getSuccessRate())).append("\n");
        summary.append("🕒 Last indexed: ").append(getLastIndexTimeFormatted()).append("\n");
        
        if (!skippedReasons.isEmpty()) {
            summary.append("\n📋 Skip reasons:\n");
            skippedReasons.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .forEach(entry -> summary.append("  • ").append(entry.getKey())
                                        .append(": ").append(entry.getValue()).append(" files\n"));
        }
        
        return summary.toString();
    }
    
    /**
     * Get a brief one-line summary
     */
    public String getBriefSummary() {
        return String.format("Indexed %d files, %d directories (%.1f%% success rate)", 
                           totalFiles, totalDirectories, getSuccessRate());
    }
}
