package com.example.fartechplugin.core;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Insert code tool for AI Agent
 */
public class InsertCodeTool extends BaseAgentTool {
    
    public InsertCodeTool() {
        super("InsertCodeTool", "Insert code into existing files", ToolCapability.CODE_MODIFICATION, true);
    }
    
    @Override
    public ToolExecutionResult execute(Map<String, Object> parameters) {
        if (!validateParameters(parameters)) {
            return ToolExecutionResult.error("Missing required parameters: filePath, code, position");
        }
        
        String filePath = getStringParameter(parameters, "filePath", "");
        String code = getStringParameter(parameters, "code", "");
        String position = getStringParameter(parameters, "position", "end");
        
        try {
            String result = "Code inserted successfully into " + filePath + " at position: " + position;
            return ToolExecutionResult.success(result);
        } catch (Exception e) {
            return ToolExecutionResult.error("Code insertion failed: " + e.getMessage());
        }
    }
    
    @Override
    public List<String> getRequiredParameters() {
        return Arrays.asList("filePath", "code", "position");
    }
    
    @Override
    public List<String> getOptionalParameters() {
        return Arrays.asList("lineNumber", "backup");
    }
}
