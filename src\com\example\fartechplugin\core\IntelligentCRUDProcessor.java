package com.example.fartechplugin.core;

import java.util.*;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

import com.example.fartechplugin.FarTechView;

/**
 * Intelligent CRUD processor that automatically detects when file operations
 * should be performed based on AI response content and conversation context
 */
public class IntelligentCRUDProcessor {
    
    private final FileOperationsAgent fileAgent;
    private final WorkspaceIndexer workspaceIndexer;
    
    // Patterns for detecting code blocks and file references
    private static final Pattern CODE_BLOCK_PATTERN = Pattern.compile("```(\\w+)?\\s*\\n([\\s\\S]*?)\\n```", Pattern.MULTILINE);
    private static final Pattern FILE_PATH_PATTERN = Pattern.compile("(?:file|path|location)\\s*:?\\s*([\\w/\\\\.-]+\\.[\\w]+)", Pattern.CASE_INSENSITIVE);
    private static final Pattern JAVA_CLASS_PATTERN = Pattern.compile("(?:class|interface|enum)\\s+(\\w+)", Pattern.CASE_INSENSITIVE);
    private static final Pattern CREATE_INTENT_PATTERN = Pattern.compile("(?:create|generate|make|build|write)\\s+(?:a\\s+)?(?:new\\s+)?(?:file|class|interface|component|service|controller|model|test)", Pattern.CASE_INSENSITIVE);
    private static final Pattern MODIFY_INTENT_PATTERN = Pattern.compile("(?:modify|update|change|edit|fix|improve|refactor)\\s+(?:the\\s+)?(?:file|class|method|function)", Pattern.CASE_INSENSITIVE);
    private static final Pattern READ_INTENT_PATTERN = Pattern.compile("(?:show|display|read|view|check|examine|analyze)\\s+(?:the\\s+)?(?:file|content|code)", Pattern.CASE_INSENSITIVE);
    
    // Context tracking
    private String lastUserMessage = "";
    private String currentProjectContext = "";
    private Map<String, String> conversationContext = new HashMap<>();
    
    public IntelligentCRUDProcessor(FileOperationsAgent fileAgent, WorkspaceIndexer workspaceIndexer) {
        this.fileAgent = fileAgent;
        this.workspaceIndexer = workspaceIndexer;
    }
    
    /**
     * Process AI response and automatically perform CRUD operations based on context
     */
    public String processIntelligentCRUD(String aiResponse, String userMessage, FarTechView view) {
        this.lastUserMessage = userMessage;
        
        // Analyze the response for automatic operations
        List<AutoOperation> operations = analyzeForOperations(aiResponse, userMessage);
        
        if (operations.isEmpty()) {
            return aiResponse; // No operations detected, return original response
        }
        
        // Execute detected operations
        StringBuilder enhancedResponse = new StringBuilder(aiResponse);
        enhancedResponse.append("\n\n🤖 **FarTech AI Auto-Operations Detected:**\n");
        
        for (AutoOperation operation : operations) {
            String result = executeAutoOperation(operation, view);
            enhancedResponse.append("\n").append(result);
        }
        
        return enhancedResponse.toString();
    }
    
    /**
     * Analyze AI response and user message to detect intended operations
     */
    private List<AutoOperation> analyzeForOperations(String aiResponse, String userMessage) {
        List<AutoOperation> operations = new ArrayList<>();
        
        // 1. Detect code blocks that should be saved as files
        operations.addAll(detectCodeFileOperations(aiResponse, userMessage));
        
        // 2. Detect file reading requests
        operations.addAll(detectReadOperations(aiResponse, userMessage));
        
        // 3. Detect modification requests
        operations.addAll(detectModificationOperations(aiResponse, userMessage));
        
        // 4. Detect directory/project structure operations
        operations.addAll(detectStructureOperations(aiResponse, userMessage));
        
        return operations;
    }
    
    /**
     * Detect when code blocks should be automatically saved as files
     */
    private List<AutoOperation> detectCodeFileOperations(String aiResponse, String userMessage) {
        List<AutoOperation> operations = new ArrayList<>();
        
        // Check if user requested file creation
        boolean createIntent = CREATE_INTENT_PATTERN.matcher(userMessage).find();
        
        if (!createIntent) {
            return operations; // No creation intent detected
        }
        
        // Find code blocks in the response
        Matcher codeBlockMatcher = CODE_BLOCK_PATTERN.matcher(aiResponse);
        
        while (codeBlockMatcher.find()) {
            String language = codeBlockMatcher.group(1);
            String code = codeBlockMatcher.group(2);
            
            if (code.trim().length() < 10) {
                continue; // Skip very short code blocks
            }
            
            // Determine file path based on content and context
            String filePath = determineFilePath(code, language, userMessage);
            
            if (filePath != null) {
                operations.add(new AutoOperation(
                    AutoOperation.Type.CREATE_FILE,
                    filePath,
                    code,
                    "Auto-detected code block for file creation",
                    0.8 // High confidence for explicit creation requests
                ));
            }
        }
        
        return operations;
    }
    
    /**
     * Detect when files should be read based on conversation context
     */
    private List<AutoOperation> detectReadOperations(String aiResponse, String userMessage) {
        List<AutoOperation> operations = new ArrayList<>();

        // Check if user requested to read/view files
        boolean readIntent = READ_INTENT_PATTERN.matcher(userMessage).find();

        if (!readIntent) {
            return operations;
        }

        // Look for file path references in user message
        Matcher filePathMatcher = FILE_PATH_PATTERN.matcher(userMessage);

        while (filePathMatcher.find()) {
            String filePath = filePathMatcher.group(1);
            String resolvedPath = resolveFilePathForReading(filePath);

            if (resolvedPath != null) {
                operations.add(new AutoOperation(
                    AutoOperation.Type.READ_FILE,
                    resolvedPath,
                    null,
                    "Auto-detected file read request",
                    0.7 // Medium-high confidence
                ));
            } else {
                // File not found, but add a suggestion operation
                operations.add(new AutoOperation(
                    AutoOperation.Type.READ_FILE,
                    filePath,
                    null,
                    "File not found - will provide suggestions",
                    0.3 // Low confidence - will show suggestions instead
                ));
            }
        }

        // Also look for common file references in natural language
        operations.addAll(detectNaturalFileReferences(userMessage));

        return operations;
    }

    /**
     * Resolve file path for reading with workspace context
     */
    private String resolveFilePathForReading(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return null;
        }

        filePath = filePath.trim();

        // Try as absolute path first
        Path absolutePath = Paths.get(filePath);
        if (absolutePath.isAbsolute() && Files.exists(absolutePath)) {
            return filePath;
        }

        // Try as relative path from current working directory
        Path relativePath = Paths.get(System.getProperty("user.dir"), filePath);
        if (Files.exists(relativePath)) {
            return relativePath.toString();
        }

        // Try to find in workspace index if available
        if (workspaceIndexer != null && workspaceIndexer.getFileCount() > 0) {
            // Try exact match in workspace
            String fileContext = workspaceIndexer.getFileContext(filePath);
            if (!fileContext.startsWith("File not found")) {
                return Paths.get(workspaceIndexer.getWorkspaceRoot(), filePath).toString();
            }

            // Try with different path separators
            String normalizedPath = filePath.replace("\\", "/");
            fileContext = workspaceIndexer.getFileContext(normalizedPath);
            if (!fileContext.startsWith("File not found")) {
                return Paths.get(workspaceIndexer.getWorkspaceRoot(), normalizedPath).toString();
            }

            // Try to find by filename only
            String fileName = Paths.get(filePath).getFileName().toString();
            String matchingFile = findFileByName(fileName);
            if (matchingFile != null) {
                return Paths.get(workspaceIndexer.getWorkspaceRoot(), matchingFile).toString();
            }
        }

        return null;
    }

    /**
     * Detect natural language file references
     */
    private List<AutoOperation> detectNaturalFileReferences(String userMessage) {
        List<AutoOperation> operations = new ArrayList<>();

        if (workspaceIndexer == null || workspaceIndexer.getFileCount() == 0) {
            return operations;
        }

        String lowerMessage = userMessage.toLowerCase();

        // Look for common file type references
        if (lowerMessage.contains("main") && lowerMessage.contains("java")) {
            String mainFile = findFileByPattern(".*[Mm]ain\\.java");
            if (mainFile != null) {
                operations.add(new AutoOperation(
                    AutoOperation.Type.READ_FILE,
                    mainFile,
                    null,
                    "Auto-detected main Java file reference",
                    0.6
                ));
            }
        }

        if (lowerMessage.contains("config") || lowerMessage.contains("properties")) {
            String configFile = findFileByPattern(".*\\.properties|.*config.*\\.(xml|yml|yaml)");
            if (configFile != null) {
                operations.add(new AutoOperation(
                    AutoOperation.Type.READ_FILE,
                    configFile,
                    null,
                    "Auto-detected configuration file reference",
                    0.6
                ));
            }
        }

        if (lowerMessage.contains("readme")) {
            String readmeFile = findFileByPattern("[Rr][Ee][Aa][Dd][Mm][Ee]\\.(md|txt)");
            if (readmeFile != null) {
                operations.add(new AutoOperation(
                    AutoOperation.Type.READ_FILE,
                    readmeFile,
                    null,
                    "Auto-detected README file reference",
                    0.6
                ));
            }
        }

        return operations;
    }

    /**
     * Find file by name in workspace
     */
    private String findFileByName(String fileName) {
        if (workspaceIndexer == null) {
            return null;
        }

        // This is a simplified implementation
        // In a real implementation, we'd need access to the file index
        // For now, try common patterns
        String[] commonPaths = {
            fileName,
            "src/main/java/" + fileName,
            "src/main/resources/" + fileName,
            "src/test/java/" + fileName,
            "src/" + fileName
        };

        for (String path : commonPaths) {
            String fileContext = workspaceIndexer.getFileContext(path);
            if (!fileContext.startsWith("File not found")) {
                return path;
            }
        }

        return null;
    }

    /**
     * Find file by pattern in workspace
     */
    private String findFileByPattern(String pattern) {
        if (workspaceIndexer == null) {
            return null;
        }

        // This is a simplified implementation
        // In a real implementation, we'd iterate through the file index
        // and match against the pattern

        return null;
    }
    
    /**
     * Detect when existing files should be modified
     */
    private List<AutoOperation> detectModificationOperations(String aiResponse, String userMessage) {
        List<AutoOperation> operations = new ArrayList<>();
        
        // Check if user requested modifications
        boolean modifyIntent = MODIFY_INTENT_PATTERN.matcher(userMessage).find();
        
        if (!modifyIntent) {
            return operations;
        }
        
        // Look for code blocks that might be modifications
        Matcher codeBlockMatcher = CODE_BLOCK_PATTERN.matcher(aiResponse);
        
        while (codeBlockMatcher.find()) {
            String language = codeBlockMatcher.group(1);
            String code = codeBlockMatcher.group(2);
            
            // Try to find existing file that matches this code
            String existingFile = findMatchingExistingFile(code, language);
            
            if (existingFile != null) {
                operations.add(new AutoOperation(
                    AutoOperation.Type.MODIFY_FILE,
                    existingFile,
                    code,
                    "Auto-detected file modification",
                    0.6 // Medium confidence for modifications
                ));
            }
        }
        
        return operations;
    }
    
    /**
     * Detect directory structure operations
     */
    private List<AutoOperation> detectStructureOperations(String aiResponse, String userMessage) {
        List<AutoOperation> operations = new ArrayList<>();
        
        // Look for mentions of creating directories or project structure
        if (userMessage.toLowerCase().contains("create") && 
            (userMessage.toLowerCase().contains("directory") || 
             userMessage.toLowerCase().contains("folder") ||
             userMessage.toLowerCase().contains("package"))) {
            
            // Extract potential directory paths from the response
            String[] lines = aiResponse.split("\n");
            for (String line : lines) {
                if (line.contains("/") && !line.contains("http")) {
                    String potentialPath = extractDirectoryPath(line);
                    if (potentialPath != null && !Files.exists(Paths.get(potentialPath))) {
                        operations.add(new AutoOperation(
                            AutoOperation.Type.CREATE_DIRECTORY,
                            potentialPath,
                            null,
                            "Auto-detected directory creation",
                            0.5 // Lower confidence for directory operations
                        ));
                    }
                }
            }
        }
        
        return operations;
    }
    
    /**
     * Determine appropriate file path for code content
     */
    private String determineFilePath(String code, String language, String userMessage) {
        // Try to extract class name from Java code
        if ("java".equals(language) || code.contains("class ") || code.contains("interface ")) {
            Matcher classMatcher = JAVA_CLASS_PATTERN.matcher(code);
            if (classMatcher.find()) {
                String className = classMatcher.group(1);
                return "src/main/java/" + className + ".java";
            }
        }
        
        // Try to extract filename from user message
        Matcher filePathMatcher = FILE_PATH_PATTERN.matcher(userMessage);
        if (filePathMatcher.find()) {
            return filePathMatcher.group(1);
        }
        
        // Generate filename based on language and content
        if (language != null) {
            switch (language.toLowerCase()) {
                case "java":
                    return "src/main/java/GeneratedClass.java";
                case "javascript":
                case "js":
                    return "script.js";
                case "python":
                case "py":
                    return "script.py";
                case "html":
                    return "index.html";
                case "css":
                    return "styles.css";
                case "sql":
                    return "query.sql";
                default:
                    return "generated_file." + language;
            }
        }
        
        return null; // Cannot determine appropriate path
    }
    
    /**
     * Find existing file that might match the given code
     */
    private String findMatchingExistingFile(String code, String language) {
        // This is a simplified implementation
        // In a full implementation, this would analyze workspace files
        // and find the best match based on class names, method signatures, etc.
        
        if (workspaceIndexer != null && workspaceIndexer.getFileCount() > 0) {
            // For now, return null - this would need more sophisticated matching
            // based on workspace analysis
        }
        
        return null;
    }
    
    /**
     * Extract directory path from a line of text
     */
    private String extractDirectoryPath(String line) {
        // Simple extraction - look for path-like patterns
        String[] parts = line.split("\\s+");
        for (String part : parts) {
            if (part.contains("/") && !part.startsWith("http") && part.length() > 3) {
                // Clean up the path
                part = part.replaceAll("[^\\w/.-]", "");
                if (part.endsWith("/")) {
                    part = part.substring(0, part.length() - 1);
                }
                return part;
            }
        }
        return null;
    }
    
    /**
     * Execute an auto-detected operation
     */
    private String executeAutoOperation(AutoOperation operation, FarTechView view) {
        String confidence = String.format("%.0f%%", operation.confidence * 100);

        switch (operation.type) {
            case CREATE_FILE:
                return String.format("📝 **Auto-Create File** (%s confidence)\n" +
                    "   Path: %s\n" +
                    "   Reason: %s\n" +
                    "   Action: [write_file:%s]%s[end_file]",
                    confidence, operation.filePath, operation.reason, operation.filePath, operation.content);

            case READ_FILE:
                // Check if this is a low confidence operation (file not found)
                if (operation.confidence < 0.5) {
                    return generateFileNotFoundSuggestions(operation.filePath);
                } else {
                    return String.format("👁️ **Auto-Read File** (%s confidence)\n" +
                        "   Path: %s\n" +
                        "   Reason: %s\n" +
                        "   Action: [READ_FILE:%s]",
                        confidence, operation.filePath, operation.reason, operation.filePath);
                }

            case MODIFY_FILE:
                return String.format("✏️ **Auto-Modify File** (%s confidence)\n" +
                    "   Path: %s\n" +
                    "   Reason: %s\n" +
                    "   Action: [write_file:%s]%s[end_file]",
                    confidence, operation.filePath, operation.reason, operation.filePath, operation.content);

            case CREATE_DIRECTORY:
                return String.format("📁 **Auto-Create Directory** (%s confidence)\n" +
                    "   Path: %s\n" +
                    "   Reason: %s\n" +
                    "   Action: [create_dir:%s]",
                    confidence, operation.filePath, operation.reason, operation.filePath);

            default:
                return "❓ Unknown operation detected";
        }
    }

    /**
     * Generate helpful suggestions when file is not found
     */
    private String generateFileNotFoundSuggestions(String filePath) {
        StringBuilder suggestions = new StringBuilder();
        suggestions.append("❌ **File Not Found**: ").append(filePath).append("\n\n");
        suggestions.append("💡 **Suggestions**:\n");

        if (workspaceIndexer != null && workspaceIndexer.getFileCount() > 0) {
            // Get workspace context for suggestions
            String workspaceContext = workspaceIndexer.getComprehensiveWorkspaceContext();

            // Extract file names from workspace context
            if (workspaceContext.contains("Recently Modified Files:")) {
                suggestions.append("📝 **Recent Files**:\n");
                String[] lines = workspaceContext.split("\n");
                boolean inRecentFiles = false;
                int count = 0;
                for (String line : lines) {
                    if (line.contains("Recently Modified Files:")) {
                        inRecentFiles = true;
                        continue;
                    }
                    if (inRecentFiles && line.startsWith("📝 ") && count < 5) {
                        suggestions.append("   ").append(line).append("\n");
                        count++;
                    }
                    if (inRecentFiles && line.startsWith("===")) {
                        break;
                    }
                }
            }

            // Show important files
            if (workspaceContext.contains("Important Files Detected:")) {
                suggestions.append("\n⭐ **Important Files**:\n");
                String[] lines = workspaceContext.split("\n");
                boolean inImportantFiles = false;
                for (String line : lines) {
                    if (line.contains("Important Files Detected:")) {
                        inImportantFiles = true;
                        continue;
                    }
                    if (inImportantFiles && line.startsWith("⭐ ")) {
                        suggestions.append("   ").append(line).append("\n");
                    }
                    if (inImportantFiles && line.trim().isEmpty()) {
                        break;
                    }
                }
            }
        } else {
            suggestions.append("   - No workspace indexed yet\n");
            suggestions.append("   - Use 'Index Workspace' to see available files\n");
            suggestions.append("   - Check the file path and try again\n");
        }

        suggestions.append("\n🔍 **Try these commands**:\n");
        suggestions.append("   - \"Index workspace\" to scan for files\n");
        suggestions.append("   - \"Show workspace files\" to see all available files\n");
        suggestions.append("   - Use exact file paths from the workspace\n");

        return suggestions.toString();
    }
    
    /**
     * Inner class to represent an auto-detected operation
     */
    private static class AutoOperation {
        enum Type {
            CREATE_FILE, READ_FILE, MODIFY_FILE, CREATE_DIRECTORY
        }
        
        final Type type;
        final String filePath;
        final String content;
        final String reason;
        final double confidence; // 0.0 to 1.0
        
        AutoOperation(Type type, String filePath, String content, String reason, double confidence) {
            this.type = type;
            this.filePath = filePath;
            this.content = content;
            this.reason = reason;
            this.confidence = confidence;
        }
    }
}
