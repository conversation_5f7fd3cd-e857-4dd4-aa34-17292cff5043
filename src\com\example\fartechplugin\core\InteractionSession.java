package com.example.fartechplugin.core;

import java.time.LocalDateTime;

/**
 * Represents a user interaction session for learning
 */
public class InteractionSession {
    private final String projectName;
    private final String userInput;
    private final String aiResponse;
    private final String followUpInput;
    private final long responseTime;
    private final LocalDateTime timestamp;
    private FeedbackSignal feedbackSignal;
    
    public InteractionSession(String projectName, String userInput, String aiResponse, 
                            String followUpInput, long responseTime) {
        this.projectName = projectName;
        this.userInput = userInput;
        this.aiResponse = aiResponse;
        this.followUpInput = followUpInput;
        this.responseTime = responseTime;
        this.timestamp = LocalDateTime.now();
        this.feedbackSignal = new FeedbackSignal();
    }
    
    // Getters
    public String getProjectName() { return projectName; }
    public String getUserInput() { return userInput; }
    public String getAiResponse() { return aiResponse; }
    public String getFollowUpInput() { return followUpInput; }
    public long getResponseTime() { return responseTime; }
    public LocalDateTime getTimestamp() { return timestamp; }
    public FeedbackSignal getFeedbackSignal() { return feedbackSignal; }
    
    public void setFeedbackSignal(FeedbackSignal feedbackSignal) {
        this.feedbackSignal = feedbackSignal;
    }
}
