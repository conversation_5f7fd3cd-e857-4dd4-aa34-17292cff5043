package com.example.fartechplugin.core;

import java.time.LocalDateTime;

/**
 * Learning statistics for monitoring
 */
public class LearningStatistics {
    public int totalSessions;
    public int behaviorPatterns;
    public String memoryStats;
    public double positiveFeedbackRate;
    public double negativeFeedbackRate;
    public LocalDateTime lastUpdate;
    
    public LearningStatistics() {
        this.lastUpdate = LocalDateTime.now();
    }
    
    @Override
    public String toString() {
        return String.format(
            "Learning Statistics:\n" +
            "- Total Sessions: %d\n" +
            "- Behavior Patterns: %d\n" +
            "- Positive Feedback Rate: %.2f%%\n" +
            "- Negative Feedback Rate: %.2f%%\n" +
            "- Last Update: %s\n\n%s",
            totalSessions, behaviorPatterns,
            positiveFeedbackRate * 100, negativeFeedbackRate * 100,
            lastUpdate.toString(), memoryStats
        );
    }
}
