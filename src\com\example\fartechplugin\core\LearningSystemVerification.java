package com.example.fartechplugin.core;

/**
 * Quick verification that all learning system components work together
 */
public class LearningSystemVerification {
    
    public static void main(String[] args) {
        System.out.println("=== FarTech AI Learning System Verification ===\n");
        
        try {
            // Test 1: Create all components
            System.out.println("✅ Test 1: Component Creation");
            MemoryManager memoryManager = new MemoryManager();
            AutomaticLearningEngine learningEngine = new AutomaticLearningEngine(memoryManager);
            FeedbackDetector detector = new FeedbackDetector();
            PatternRecognizer recognizer = new PatternRecognizer();
            System.out.println("   All components created successfully\n");
            
            // Test 2: Test feedback detection
            System.out.println("✅ Test 2: Feedback Detection");
            FeedbackSignal signal = detector.detectFeedback(
                "Create a class", 
                "Here's your class", 
                "Perfect! Exactly what I needed."
            );
            System.out.println("   Feedback type: " + signal.getType());
            System.out.println("   Feedback score: " + signal.getOverallScore() + "\n");
            
            // Test 3: Test learning interaction
            System.out.println("✅ Test 3: Learning Interaction");
            learningEngine.learnFromInteraction(
                "TestProject",
                "Create a Java service class",
                "Here's your UserService class...",
                "Great! I love detailed documentation.",
                1500
            );
            System.out.println("   Learning interaction completed\n");
            
            // Test 4: Test statistics
            System.out.println("✅ Test 4: Learning Statistics");
            LearningStatistics stats = learningEngine.getLearningStats();
            System.out.println("   Total sessions: " + stats.totalSessions);
            System.out.println("   Behavior patterns: " + stats.behaviorPatterns);
            System.out.println("   Statistics generated successfully\n");
            
            // Test 5: Test pattern recognition
            System.out.println("✅ Test 5: Pattern Recognition");
            String suggestions = recognizer.getSuggestions("Create a test class", "TestProject");
            System.out.println("   Pattern suggestions: " + (suggestions.isEmpty() ? "None" : "Generated"));
            System.out.println("   Pattern recognition working\n");
            
            // Test 6: Test behavior patterns
            System.out.println("✅ Test 6: Behavior Patterns");
            UserBehaviorPattern pattern = new UserBehaviorPattern("testUser", "creation");
            pattern.addAction("create_class", true);
            pattern.addAction("create_test", true);
            System.out.println("   User behavior pattern created");
            System.out.println("   Success rate: " + String.format("%.1f%%", pattern.getSuccessRate() * 100) + "\n");
            
            // Test 7: Test adaptive prompts
            System.out.println("✅ Test 7: Adaptive Prompts");
            String adaptedPrompt = learningEngine.getAdaptivePrompt(
                "Create a new service", 
                "TestProject", 
                "java"
            );
            System.out.println("   Adaptive prompt generated (length: " + adaptedPrompt.length() + " chars)\n");
            
            System.out.println("🎉 ALL TESTS PASSED! Learning system is fully functional.\n");
            
            // Show final statistics
            System.out.println("📊 Final Learning Statistics:");
            System.out.println(stats.toString());
            
        } catch (Exception e) {
            System.err.println("❌ Verification failed: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
