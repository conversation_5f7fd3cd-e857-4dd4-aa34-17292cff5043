package com.example.fartechplugin.core;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Supporting classes for the Automatic Learning Engine
 */

/**
 * Represents a user interaction session for learning
 */
public class InteractionSession {
    private final String projectName;
    private final String userInput;
    private final String aiResponse;
    private final String followUpInput;
    private final long responseTime;
    private final LocalDateTime timestamp;
    private FeedbackSignal feedbackSignal;
    
    public InteractionSession(String projectName, String userInput, String aiResponse, 
                            String followUpInput, long responseTime) {
        this.projectName = projectName;
        this.userInput = userInput;
        this.aiResponse = aiResponse;
        this.followUpInput = followUpInput;
        this.responseTime = responseTime;
        this.timestamp = LocalDateTime.now();
        this.feedbackSignal = new FeedbackSignal();
    }
    
    // Getters
    public String getProjectName() { return projectName; }
    public String getUserInput() { return userInput; }
    public String getAiResponse() { return aiResponse; }
    public String getFollowUpInput() { return followUpInput; }
    public long getResponseTime() { return responseTime; }
    public LocalDateTime getTimestamp() { return timestamp; }
    public FeedbackSignal getFeedbackSignal() { return feedbackSignal; }
    
    public void setFeedbackSignal(FeedbackSignal feedbackSignal) {
        this.feedbackSignal = feedbackSignal;
    }
}

/**
 * Represents feedback signals detected from user behavior
 */
public class FeedbackSignal {
    private final Map<String, Double> signals;
    private FeedbackType type;
    private double overallScore;
    
    public FeedbackSignal() {
        this.signals = new HashMap<>();
        this.type = FeedbackType.NEUTRAL;
        this.overallScore = 0.0;
    }
    
    public void addSignal(String signalType, double weight) {
        signals.put(signalType, weight);
        recalculateScore();
    }
    
    private void recalculateScore() {
        overallScore = signals.values().stream().mapToDouble(Double::doubleValue).sum();
        
        if (overallScore > 0.3) {
            type = FeedbackType.POSITIVE;
        } else if (overallScore < -0.3) {
            type = FeedbackType.NEGATIVE;
        } else {
            type = FeedbackType.NEUTRAL;
        }
    }
    
    public FeedbackType getType() { return type; }
    public double getOverallScore() { return overallScore; }
    public Map<String, Double> getSignals() { return new HashMap<>(signals); }
}

/**
 * Detects feedback from user interactions
 */
public class FeedbackDetector {
    
    private static final Map<String, Double> POSITIVE_KEYWORDS = Map.of(
        "perfect", 0.5, "excellent", 0.5, "great", 0.4, "good", 0.3,
        "exactly", 0.4, "correct", 0.3, "thanks", 0.2, "helpful", 0.3
    );
    
    private static final Map<String, Double> NEGATIVE_KEYWORDS = Map.of(
        "wrong", -0.5, "incorrect", -0.5, "bad", -0.4, "error", -0.3,
        "not what", -0.4, "try again", -0.3, "fix", -0.2, "problem", -0.3
    );
    
    public FeedbackSignal detectFeedback(String userInput, String aiResponse, String followUp) {
        FeedbackSignal signal = new FeedbackSignal();
        
        if (followUp != null) {
            String lower = followUp.toLowerCase();
            
            // Check for explicit positive feedback
            for (Map.Entry<String, Double> entry : POSITIVE_KEYWORDS.entrySet()) {
                if (lower.contains(entry.getKey())) {
                    signal.addSignal("positive_keyword_" + entry.getKey(), entry.getValue());
                }
            }
            
            // Check for explicit negative feedback
            for (Map.Entry<String, Double> entry : NEGATIVE_KEYWORDS.entrySet()) {
                if (lower.contains(entry.getKey())) {
                    signal.addSignal("negative_keyword_" + entry.getKey(), entry.getValue());
                }
            }
            
            // Check for continuation patterns (usually positive)
            if (lower.startsWith("and") || lower.startsWith("also") || lower.startsWith("now")) {
                signal.addSignal("continuation_pattern", 0.2);
            }
            
            // Check for correction patterns (usually negative)
            if (lower.startsWith("but") || lower.startsWith("however") || lower.startsWith("actually")) {
                signal.addSignal("correction_pattern", -0.2);
            }
            
            // Check for question patterns (neutral to slightly negative)
            if (lower.contains("?") && (lower.contains("how") || lower.contains("why") || lower.contains("what"))) {
                signal.addSignal("clarification_needed", -0.1);
            }
        }
        
        return signal;
    }
}

/**
 * Recognizes patterns in user interactions
 */
public class PatternRecognizer {
    
    public String getSuggestions(String prompt, String projectName) {
        StringBuilder suggestions = new StringBuilder();
        
        String lower = prompt.toLowerCase();
        
        // Code generation patterns
        if (lower.contains("create") && lower.contains("class")) {
            suggestions.append("- Consider using design patterns like Builder or Factory\n");
        }
        
        // Testing patterns
        if (lower.contains("test") || lower.contains("junit")) {
            suggestions.append("- Include edge cases and error scenarios\n");
            suggestions.append("- Consider using mocking for dependencies\n");
        }
        
        // Documentation patterns
        if (lower.contains("document") || lower.contains("javadoc")) {
            suggestions.append("- Include parameter descriptions and return values\n");
            suggestions.append("- Add usage examples\n");
        }
        
        // Refactoring patterns
        if (lower.contains("refactor") || lower.contains("improve")) {
            suggestions.append("- Focus on single responsibility principle\n");
            suggestions.append("- Consider extracting methods for better readability\n");
        }
        
        return suggestions.toString();
    }
}

/**
 * Tracks user behavior patterns
 */
public class UserBehaviorPattern {
    private final String userId;
    private final String actionType;
    private final List<BehaviorAction> actions;
    private double successRate;
    private LocalDateTime lastUpdate;
    
    public UserBehaviorPattern(String userId, String actionType) {
        this.userId = userId;
        this.actionType = actionType;
        this.actions = new ArrayList<>();
        this.successRate = 0.5; // Start neutral
        this.lastUpdate = LocalDateTime.now();
    }
    
    public void addAction(String context, boolean successful) {
        actions.add(new BehaviorAction(context, successful, LocalDateTime.now()));
        updateSuccessRate();
        lastUpdate = LocalDateTime.now();
        
        // Keep only recent actions (last 20)
        if (actions.size() > 20) {
            actions.remove(0);
        }
    }
    
    private void updateSuccessRate() {
        if (actions.isEmpty()) {
            successRate = 0.5;
            return;
        }
        
        long successCount = actions.stream().mapToLong(action -> action.successful ? 1 : 0).sum();
        successRate = (double) successCount / actions.size();
    }
    
    public String getUserId() { return userId; }
    public String getActionType() { return actionType; }
    public int getActionCount() { return actions.size(); }
    public double getSuccessRate() { return successRate; }
    public LocalDateTime getLastUpdate() { return lastUpdate; }
}

/**
 * Represents a single behavior action
 */
public class BehaviorAction {
    public final String context;
    public final boolean successful;
    public final LocalDateTime timestamp;
    
    public BehaviorAction(String context, boolean successful, LocalDateTime timestamp) {
        this.context = context;
        this.successful = successful;
        this.timestamp = timestamp;
    }
}

/**
 * Learning statistics for monitoring
 */
public class LearningStats {
    public int totalSessions;
    public int behaviorPatterns;
    public String memoryStats;
    public double positiveFeedbackRate;
    public double negativeFeedbackRate;
    public LocalDateTime lastUpdate;
    
    public LearningStats() {
        this.lastUpdate = LocalDateTime.now();
    }
    
    @Override
    public String toString() {
        return String.format(
            "Learning Statistics:\n" +
            "- Total Sessions: %d\n" +
            "- Behavior Patterns: %d\n" +
            "- Positive Feedback Rate: %.2f%%\n" +
            "- Negative Feedback Rate: %.2f%%\n" +
            "- Last Update: %s\n\n%s",
            totalSessions, behaviorPatterns,
            positiveFeedbackRate * 100, negativeFeedbackRate * 100,
            lastUpdate.toString(), memoryStats
        );
    }
}
