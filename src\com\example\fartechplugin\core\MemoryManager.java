package com.example.fartechplugin.core;

import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
// Removed Gson dependency - using simple Java serialization instead

/**
 * FarTech AI Memory System - Inspired by Augment's Memory capabilities
 * Learns user coding patterns, preferences, and project context across sessions
 */
public class MemoryManager {
    
    private static final String MEMORY_DIR = System.getProperty("user.home") + "/.fartech-ai/memories";
    private static final String CODING_PATTERNS_FILE = "coding_patterns.json";
    private static final String PROJECT_CONTEXT_FILE = "project_context.json";
    private static final String USER_PREFERENCES_FILE = "user_preferences.json";
    private static final String CONVERSATION_HISTORY_FILE = "conversation_history.json";
    
    // Simplified memory storage without external dependencies
    private final Map<String, CodingPattern> codingPatterns;
    private final Map<String, ProjectContext> projectContexts;
    private final UserPreferences userPreferences;
    private final List<ConversationMemory> conversationHistory;

    public MemoryManager() {
        this.codingPatterns = new ConcurrentHashMap<>();
        this.projectContexts = new ConcurrentHashMap<>();
        this.userPreferences = new UserPreferences();
        this.conversationHistory = new ArrayList<>();

        initializeMemoryDirectory();
        // Load existing memories from disk
        loadAllMemories();
    }
    
    private void initializeMemoryDirectory() {
        try {
            Files.createDirectories(Paths.get(MEMORY_DIR));
        } catch (IOException e) {
            System.err.println("Failed to create memory directory: " + e.getMessage());
        }
    }
    
    /**
     * Learn from user's coding interaction
     */
    public void learnFromInteraction(String projectName, String fileType, String userQuery, 
                                   String aiResponse, String userFeedback) {
        // Learn coding patterns
        learnCodingPattern(fileType, userQuery, aiResponse, userFeedback);
        
        // Update project context
        updateProjectContext(projectName, fileType, userQuery);
        
        // Store conversation memory
        storeConversationMemory(projectName, userQuery, aiResponse, userFeedback);
        
        // Save all memories
        saveAllMemories();
    }
    
    private void learnCodingPattern(String fileType, String userQuery, String aiResponse, String feedback) {
        String patternKey = fileType + "_" + extractPatternType(userQuery);
        CodingPattern pattern = codingPatterns.getOrDefault(patternKey, new CodingPattern(fileType, patternKey));
        
        pattern.addInteraction(userQuery, aiResponse, feedback);
        pattern.updateConfidence(feedback);
        
        codingPatterns.put(patternKey, pattern);
    }
    
    private void updateProjectContext(String projectName, String fileType, String userQuery) {
        ProjectContext context = projectContexts.getOrDefault(projectName, new ProjectContext(projectName));
        
        context.addFileType(fileType);
        context.addCommonQuery(userQuery);
        context.updateLastAccessed();
        
        projectContexts.put(projectName, context);
    }
    
    private void storeConversationMemory(String projectName, String userQuery, String aiResponse, String feedback) {
        ConversationMemory memory = new ConversationMemory(
            projectName, userQuery, aiResponse, feedback, LocalDateTime.now()
        );
        
        conversationHistory.add(memory);
        
        // Keep only last 1000 conversations to manage memory
        if (conversationHistory.size() > 1000) {
            conversationHistory.remove(0);
        }
    }
    
    /**
     * Get relevant memories for current context
     */
    public MemoryContext getRelevantMemories(String projectName, String fileType, String currentQuery) {
        MemoryContext context = new MemoryContext();
        
        // Get relevant coding patterns
        context.relevantPatterns = findRelevantCodingPatterns(fileType, currentQuery);
        
        // Get project-specific context
        context.projectContext = projectContexts.get(projectName);
        
        // Get similar past conversations
        context.similarConversations = findSimilarConversations(currentQuery, 5);
        
        // Get user preferences
        context.userPreferences = this.userPreferences;
        
        return context;
    }
    
    private List<CodingPattern> findRelevantCodingPatterns(String fileType, String query) {
        return codingPatterns.values().stream()
            .filter(pattern -> pattern.fileType.equals(fileType))
            .filter(pattern -> isQuerySimilar(pattern.getCommonQueries(), query))
            .sorted((p1, p2) -> Double.compare(p2.confidence, p1.confidence))
            .limit(3)
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    private List<ConversationMemory> findSimilarConversations(String query, int limit) {
        return conversationHistory.stream()
            .filter(conv -> calculateSimilarity(conv.userQuery, query) > 0.3)
            .sorted((c1, c2) -> Double.compare(
                calculateSimilarity(c2.userQuery, query),
                calculateSimilarity(c1.userQuery, query)
            ))
            .limit(limit)
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    /**
     * Generate memory-enhanced prompt
     */
    public String enhancePromptWithMemories(String originalPrompt, String projectName, String fileType) {
        MemoryContext memories = getRelevantMemories(projectName, fileType, originalPrompt);
        
        StringBuilder enhancedPrompt = new StringBuilder();
        enhancedPrompt.append("=== FARTECH AI MEMORY CONTEXT ===\n\n");
        
        // Add user preferences
        if (memories.userPreferences != null) {
            enhancedPrompt.append("User Preferences:\n");
            enhancedPrompt.append("- Coding Style: ").append(memories.userPreferences.codingStyle).append("\n");
            enhancedPrompt.append("- Preferred Patterns: ").append(String.join(", ", memories.userPreferences.preferredPatterns)).append("\n");
            enhancedPrompt.append("- Comment Style: ").append(memories.userPreferences.commentStyle).append("\n\n");
        }
        
        // Add project context
        if (memories.projectContext != null) {
            enhancedPrompt.append("Project Context (").append(memories.projectContext.projectName).append("):\n");
            enhancedPrompt.append("- File Types: ").append(String.join(", ", memories.projectContext.fileTypes)).append("\n");
            enhancedPrompt.append("- Common Queries: ").append(String.join(", ", memories.projectContext.commonQueries)).append("\n\n");
        }
        
        // Add relevant coding patterns
        if (!memories.relevantPatterns.isEmpty()) {
            enhancedPrompt.append("Relevant Coding Patterns:\n");
            for (CodingPattern pattern : memories.relevantPatterns) {
                enhancedPrompt.append("- ").append(pattern.patternType)
                    .append(" (confidence: ").append(String.format("%.2f", pattern.confidence)).append(")\n");
            }
            enhancedPrompt.append("\n");
        }
        
        // Add similar past conversations
        if (!memories.similarConversations.isEmpty()) {
            enhancedPrompt.append("Similar Past Interactions:\n");
            for (ConversationMemory conv : memories.similarConversations) {
                enhancedPrompt.append("- Q: ").append(conv.userQuery.substring(0, Math.min(100, conv.userQuery.length())))
                    .append("...\n");
            }
            enhancedPrompt.append("\n");
        }
        
        enhancedPrompt.append("=== CURRENT REQUEST ===\n");
        enhancedPrompt.append(originalPrompt);
        
        return enhancedPrompt.toString();
    }
    
    /**
     * Update user preferences based on feedback
     */
    public void updateUserPreferences(String feedback, String codeStyle, List<String> patterns) {
        if (feedback.toLowerCase().contains("good") || feedback.toLowerCase().contains("perfect")) {
            if (codeStyle != null) {
                userPreferences.codingStyle = codeStyle;
            }
            if (patterns != null) {
                userPreferences.preferredPatterns.addAll(patterns);
            }
        }
        saveAllMemories();
    }
    
    // Utility methods
    private String extractPatternType(String query) {
        String lowerQuery = query.toLowerCase();
        if (lowerQuery.contains("class") || lowerQuery.contains("object")) return "class_design";
        if (lowerQuery.contains("method") || lowerQuery.contains("function")) return "method_design";
        if (lowerQuery.contains("test") || lowerQuery.contains("junit")) return "testing";
        if (lowerQuery.contains("exception") || lowerQuery.contains("error")) return "error_handling";
        if (lowerQuery.contains("database") || lowerQuery.contains("sql")) return "database";
        if (lowerQuery.contains("api") || lowerQuery.contains("rest")) return "api_design";
        return "general";
    }
    
    private boolean isQuerySimilar(List<String> queries, String targetQuery) {
        return queries.stream()
            .anyMatch(query -> calculateSimilarity(query, targetQuery) > 0.4);
    }
    
    private double calculateSimilarity(String s1, String s2) {
        if (s1 == null || s2 == null) return 0.0;
        
        String[] words1 = s1.toLowerCase().split("\\s+");
        String[] words2 = s2.toLowerCase().split("\\s+");
        
        Set<String> set1 = new HashSet<>(Arrays.asList(words1));
        Set<String> set2 = new HashSet<>(Arrays.asList(words2));
        
        Set<String> intersection = new HashSet<>(set1);
        intersection.retainAll(set2);
        
        Set<String> union = new HashSet<>(set1);
        union.addAll(set2);
        
        return union.isEmpty() ? 0.0 : (double) intersection.size() / union.size();
    }
    
    // Enhanced save and load methods with proper persistence
    private void saveAllMemories() {
        try {
            // Save coding patterns
            saveCodingPatterns();

            // Save project contexts
            saveProjectContexts();

            // Save user preferences
            saveUserPreferences();

            // Save conversation history (last 500 conversations)
            saveConversationHistory();

            System.out.println("FarTech AI: All memories saved successfully");
        } catch (Exception e) {
            System.err.println("Failed to save memories: " + e.getMessage());
        }
    }

    private void loadAllMemories() {
        try {
            // Load coding patterns
            loadCodingPatterns();

            // Load project contexts
            loadProjectContexts();

            // Load user preferences
            loadUserPreferences();

            // Load conversation history
            loadConversationHistory();

            System.out.println("FarTech AI: All memories loaded successfully");
        } catch (Exception e) {
            System.err.println("Failed to load memories: " + e.getMessage());
        }
    }

    private void saveCodingPatterns() throws IOException {
        File file = new File(MEMORY_DIR, CODING_PATTERNS_FILE);
        try (PrintWriter writer = new PrintWriter(new FileWriter(file))) {
            writer.println("# FarTech AI Coding Patterns");
            writer.println("# Format: fileType|patternType|confidence|usageCount|lastUsed|queries|responses");

            for (CodingPattern pattern : codingPatterns.values()) {
                writer.printf("%s|%s|%.3f|%d|%s|%s|%s%n",
                    pattern.fileType,
                    pattern.patternType,
                    pattern.confidence,
                    pattern.usageCount,
                    pattern.lastUsed.toString(),
                    String.join(";;", pattern.commonQueries),
                    String.join(";;", pattern.successfulResponses)
                );
            }
        }
    }

    private void loadCodingPatterns() throws IOException {
        File file = new File(MEMORY_DIR, CODING_PATTERNS_FILE);
        if (!file.exists()) return;

        try (Scanner scanner = new Scanner(file)) {
            while (scanner.hasNextLine()) {
                String line = scanner.nextLine().trim();
                if (line.startsWith("#") || line.isEmpty()) continue;

                String[] parts = line.split("\\|");
                if (parts.length >= 6) {
                    CodingPattern pattern = new CodingPattern(parts[0], parts[1]);
                    pattern.confidence = Double.parseDouble(parts[2]);
                    pattern.usageCount = Integer.parseInt(parts[3]);
                    pattern.lastUsed = LocalDateTime.parse(parts[4]);

                    if (!parts[5].isEmpty()) {
                        pattern.commonQueries.addAll(Arrays.asList(parts[5].split(";;")));
                    }
                    if (parts.length > 6 && !parts[6].isEmpty()) {
                        pattern.successfulResponses.addAll(Arrays.asList(parts[6].split(";;")));
                    }

                    codingPatterns.put(parts[0] + "_" + parts[1], pattern);
                }
            }
        }
    }

    private void saveProjectContexts() throws IOException {
        File file = new File(MEMORY_DIR, PROJECT_CONTEXT_FILE);
        try (PrintWriter writer = new PrintWriter(new FileWriter(file))) {
            writer.println("# FarTech AI Project Contexts");
            writer.println("# Format: projectName|fileTypes|commonQueries|lastAccessed|created");

            for (ProjectContext context : projectContexts.values()) {
                writer.printf("%s|%s|%s|%s|%s%n",
                    context.projectName,
                    String.join(";;", context.fileTypes),
                    String.join(";;", context.commonQueries),
                    context.lastAccessed.toString(),
                    context.created.toString()
                );
            }
        }
    }

    private void loadProjectContexts() throws IOException {
        File file = new File(MEMORY_DIR, PROJECT_CONTEXT_FILE);
        if (!file.exists()) return;

        try (Scanner scanner = new Scanner(file)) {
            while (scanner.hasNextLine()) {
                String line = scanner.nextLine().trim();
                if (line.startsWith("#") || line.isEmpty()) continue;

                String[] parts = line.split("\\|");
                if (parts.length >= 5) {
                    ProjectContext context = new ProjectContext(parts[0]);

                    if (!parts[1].isEmpty()) {
                        context.fileTypes.addAll(Arrays.asList(parts[1].split(";;")));
                    }
                    if (!parts[2].isEmpty()) {
                        context.commonQueries.addAll(Arrays.asList(parts[2].split(";;")));
                    }
                    context.lastAccessed = LocalDateTime.parse(parts[3]);
                    context.created = LocalDateTime.parse(parts[4]);

                    projectContexts.put(parts[0], context);
                }
            }
        }
    }

    private void saveUserPreferences() throws IOException {
        File file = new File(MEMORY_DIR, USER_PREFERENCES_FILE);
        try (PrintWriter writer = new PrintWriter(new FileWriter(file))) {
            writer.println("# FarTech AI User Preferences");
            writer.printf("codingStyle=%s%n", userPreferences.codingStyle);
            writer.printf("commentStyle=%s%n", userPreferences.commentStyle);
            writer.printf("verboseExplanations=%s%n", userPreferences.verboseExplanations);
            writer.printf("includeComments=%s%n", userPreferences.includeComments);
            writer.printf("preferFunctional=%s%n", userPreferences.preferFunctional);
            writer.printf("preferredPatterns=%s%n", String.join(";;", userPreferences.preferredPatterns));
        }
    }

    private void loadUserPreferences() throws IOException {
        File file = new File(MEMORY_DIR, USER_PREFERENCES_FILE);
        if (!file.exists()) return;

        try (Scanner scanner = new Scanner(file)) {
            while (scanner.hasNextLine()) {
                String line = scanner.nextLine().trim();
                if (line.startsWith("#") || line.isEmpty()) continue;

                String[] parts = line.split("=", 2);
                if (parts.length == 2) {
                    String key = parts[0];
                    String value = parts[1];

                    switch (key) {
                        case "codingStyle":
                            userPreferences.codingStyle = value;
                            break;
                        case "commentStyle":
                            userPreferences.commentStyle = value;
                            break;
                        case "verboseExplanations":
                            userPreferences.verboseExplanations = Boolean.parseBoolean(value);
                            break;
                        case "includeComments":
                            userPreferences.includeComments = Boolean.parseBoolean(value);
                            break;
                        case "preferFunctional":
                            userPreferences.preferFunctional = Boolean.parseBoolean(value);
                            break;
                        case "preferredPatterns":
                            if (!value.isEmpty()) {
                                userPreferences.preferredPatterns.clear();
                                userPreferences.preferredPatterns.addAll(Arrays.asList(value.split(";;")));
                            }
                            break;
                    }
                }
            }
        }
    }

    private void saveConversationHistory() throws IOException {
        File file = new File(MEMORY_DIR, CONVERSATION_HISTORY_FILE);
        try (PrintWriter writer = new PrintWriter(new FileWriter(file))) {
            writer.println("# FarTech AI Conversation History");
            writer.println("# Format: projectName|userQuery|aiResponse|userFeedback|timestamp");

            // Save only last 500 conversations to manage file size
            int startIndex = Math.max(0, conversationHistory.size() - 500);
            for (int i = startIndex; i < conversationHistory.size(); i++) {
                ConversationMemory conv = conversationHistory.get(i);
                writer.printf("%s|%s|%s|%s|%s%n",
                    conv.projectName,
                    conv.userQuery.replace("|", "&#124;").replace("\n", "\\n"),
                    conv.aiResponse.replace("|", "&#124;").replace("\n", "\\n"),
                    conv.userFeedback != null ? conv.userFeedback.replace("|", "&#124;") : "",
                    conv.timestamp.toString()
                );
            }
        }
    }

    private void loadConversationHistory() throws IOException {
        File file = new File(MEMORY_DIR, CONVERSATION_HISTORY_FILE);
        if (!file.exists()) return;

        try (Scanner scanner = new Scanner(file)) {
            while (scanner.hasNextLine()) {
                String line = scanner.nextLine().trim();
                if (line.startsWith("#") || line.isEmpty()) continue;

                String[] parts = line.split("\\|");
                if (parts.length >= 5) {
                    ConversationMemory conv = new ConversationMemory(
                        parts[0],
                        parts[1].replace("&#124;", "|").replace("\\n", "\n"),
                        parts[2].replace("&#124;", "|").replace("\\n", "\n"),
                        parts[3].isEmpty() ? null : parts[3].replace("&#124;", "|"),
                        LocalDateTime.parse(parts[4])
                    );
                    conversationHistory.add(conv);
                }
            }
        }
    }
    
    /**
     * Get memory statistics for debugging
     */
    public String getMemoryStats() {
        return String.format(
            "Memory Stats:\n" +
            "- Coding Patterns: %d\n" +
            "- Project Contexts: %d\n" +
            "- Conversation History: %d\n" +
            "- User Preferences: %s",
            codingPatterns.size(),
            projectContexts.size(),
            conversationHistory.size(),
            userPreferences.codingStyle != null ? "Configured" : "Default"
        );
    }
}
