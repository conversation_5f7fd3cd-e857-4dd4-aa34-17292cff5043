package com.example.fartechplugin.core;

import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
// Removed Gson dependency - using simple Java serialization instead

/**
 * FarTech AI Memory System - Inspired by Augment's Memory capabilities
 * Learns user coding patterns, preferences, and project context across sessions
 */
public class MemoryManager {
    
    private static final String MEMORY_DIR = System.getProperty("user.home") + "/.fartech-ai/memories";
    private static final String CODING_PATTERNS_FILE = "coding_patterns.json";
    private static final String PROJECT_CONTEXT_FILE = "project_context.json";
    private static final String USER_PREFERENCES_FILE = "user_preferences.json";
    private static final String CONVERSATION_HISTORY_FILE = "conversation_history.json";
    
    // Simplified memory storage without external dependencies
    private final Map<String, CodingPattern> codingPatterns;
    private final Map<String, ProjectContext> projectContexts;
    private final UserPreferences userPreferences;
    private final List<ConversationMemory> conversationHistory;

    public MemoryManager() {
        this.codingPatterns = new ConcurrentHashMap<>();
        this.projectContexts = new ConcurrentHashMap<>();
        this.userPreferences = new UserPreferences();
        this.conversationHistory = new ArrayList<>();

        initializeMemoryDirectory();
        // Note: Simplified version - memory persistence disabled for now
        // loadAllMemories();
    }
    
    private void initializeMemoryDirectory() {
        try {
            Files.createDirectories(Paths.get(MEMORY_DIR));
        } catch (IOException e) {
            System.err.println("Failed to create memory directory: " + e.getMessage());
        }
    }
    
    /**
     * Learn from user's coding interaction
     */
    public void learnFromInteraction(String projectName, String fileType, String userQuery, 
                                   String aiResponse, String userFeedback) {
        // Learn coding patterns
        learnCodingPattern(fileType, userQuery, aiResponse, userFeedback);
        
        // Update project context
        updateProjectContext(projectName, fileType, userQuery);
        
        // Store conversation memory
        storeConversationMemory(projectName, userQuery, aiResponse, userFeedback);
        
        // Save all memories
        saveAllMemories();
    }
    
    private void learnCodingPattern(String fileType, String userQuery, String aiResponse, String feedback) {
        String patternKey = fileType + "_" + extractPatternType(userQuery);
        CodingPattern pattern = codingPatterns.getOrDefault(patternKey, new CodingPattern(fileType, patternKey));
        
        pattern.addInteraction(userQuery, aiResponse, feedback);
        pattern.updateConfidence(feedback);
        
        codingPatterns.put(patternKey, pattern);
    }
    
    private void updateProjectContext(String projectName, String fileType, String userQuery) {
        ProjectContext context = projectContexts.getOrDefault(projectName, new ProjectContext(projectName));
        
        context.addFileType(fileType);
        context.addCommonQuery(userQuery);
        context.updateLastAccessed();
        
        projectContexts.put(projectName, context);
    }
    
    private void storeConversationMemory(String projectName, String userQuery, String aiResponse, String feedback) {
        ConversationMemory memory = new ConversationMemory(
            projectName, userQuery, aiResponse, feedback, LocalDateTime.now()
        );
        
        conversationHistory.add(memory);
        
        // Keep only last 1000 conversations to manage memory
        if (conversationHistory.size() > 1000) {
            conversationHistory.remove(0);
        }
    }
    
    /**
     * Get relevant memories for current context
     */
    public MemoryContext getRelevantMemories(String projectName, String fileType, String currentQuery) {
        MemoryContext context = new MemoryContext();
        
        // Get relevant coding patterns
        context.relevantPatterns = findRelevantCodingPatterns(fileType, currentQuery);
        
        // Get project-specific context
        context.projectContext = projectContexts.get(projectName);
        
        // Get similar past conversations
        context.similarConversations = findSimilarConversations(currentQuery, 5);
        
        // Get user preferences
        context.userPreferences = this.userPreferences;
        
        return context;
    }
    
    private List<CodingPattern> findRelevantCodingPatterns(String fileType, String query) {
        return codingPatterns.values().stream()
            .filter(pattern -> pattern.fileType.equals(fileType))
            .filter(pattern -> isQuerySimilar(pattern.getCommonQueries(), query))
            .sorted((p1, p2) -> Double.compare(p2.confidence, p1.confidence))
            .limit(3)
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    private List<ConversationMemory> findSimilarConversations(String query, int limit) {
        return conversationHistory.stream()
            .filter(conv -> calculateSimilarity(conv.userQuery, query) > 0.3)
            .sorted((c1, c2) -> Double.compare(
                calculateSimilarity(c2.userQuery, query),
                calculateSimilarity(c1.userQuery, query)
            ))
            .limit(limit)
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    /**
     * Generate memory-enhanced prompt
     */
    public String enhancePromptWithMemories(String originalPrompt, String projectName, String fileType) {
        MemoryContext memories = getRelevantMemories(projectName, fileType, originalPrompt);
        
        StringBuilder enhancedPrompt = new StringBuilder();
        enhancedPrompt.append("=== FARTECH AI MEMORY CONTEXT ===\n\n");
        
        // Add user preferences
        if (memories.userPreferences != null) {
            enhancedPrompt.append("User Preferences:\n");
            enhancedPrompt.append("- Coding Style: ").append(memories.userPreferences.codingStyle).append("\n");
            enhancedPrompt.append("- Preferred Patterns: ").append(String.join(", ", memories.userPreferences.preferredPatterns)).append("\n");
            enhancedPrompt.append("- Comment Style: ").append(memories.userPreferences.commentStyle).append("\n\n");
        }
        
        // Add project context
        if (memories.projectContext != null) {
            enhancedPrompt.append("Project Context (").append(memories.projectContext.projectName).append("):\n");
            enhancedPrompt.append("- File Types: ").append(String.join(", ", memories.projectContext.fileTypes)).append("\n");
            enhancedPrompt.append("- Common Queries: ").append(String.join(", ", memories.projectContext.commonQueries)).append("\n\n");
        }
        
        // Add relevant coding patterns
        if (!memories.relevantPatterns.isEmpty()) {
            enhancedPrompt.append("Relevant Coding Patterns:\n");
            for (CodingPattern pattern : memories.relevantPatterns) {
                enhancedPrompt.append("- ").append(pattern.patternType)
                    .append(" (confidence: ").append(String.format("%.2f", pattern.confidence)).append(")\n");
            }
            enhancedPrompt.append("\n");
        }
        
        // Add similar past conversations
        if (!memories.similarConversations.isEmpty()) {
            enhancedPrompt.append("Similar Past Interactions:\n");
            for (ConversationMemory conv : memories.similarConversations) {
                enhancedPrompt.append("- Q: ").append(conv.userQuery.substring(0, Math.min(100, conv.userQuery.length())))
                    .append("...\n");
            }
            enhancedPrompt.append("\n");
        }
        
        enhancedPrompt.append("=== CURRENT REQUEST ===\n");
        enhancedPrompt.append(originalPrompt);
        
        return enhancedPrompt.toString();
    }
    
    /**
     * Update user preferences based on feedback
     */
    public void updateUserPreferences(String feedback, String codeStyle, List<String> patterns) {
        if (feedback.toLowerCase().contains("good") || feedback.toLowerCase().contains("perfect")) {
            if (codeStyle != null) {
                userPreferences.codingStyle = codeStyle;
            }
            if (patterns != null) {
                userPreferences.preferredPatterns.addAll(patterns);
            }
        }
        saveAllMemories();
    }
    
    // Utility methods
    private String extractPatternType(String query) {
        String lowerQuery = query.toLowerCase();
        if (lowerQuery.contains("class") || lowerQuery.contains("object")) return "class_design";
        if (lowerQuery.contains("method") || lowerQuery.contains("function")) return "method_design";
        if (lowerQuery.contains("test") || lowerQuery.contains("junit")) return "testing";
        if (lowerQuery.contains("exception") || lowerQuery.contains("error")) return "error_handling";
        if (lowerQuery.contains("database") || lowerQuery.contains("sql")) return "database";
        if (lowerQuery.contains("api") || lowerQuery.contains("rest")) return "api_design";
        return "general";
    }
    
    private boolean isQuerySimilar(List<String> queries, String targetQuery) {
        return queries.stream()
            .anyMatch(query -> calculateSimilarity(query, targetQuery) > 0.4);
    }
    
    private double calculateSimilarity(String s1, String s2) {
        if (s1 == null || s2 == null) return 0.0;
        
        String[] words1 = s1.toLowerCase().split("\\s+");
        String[] words2 = s2.toLowerCase().split("\\s+");
        
        Set<String> set1 = new HashSet<>(Arrays.asList(words1));
        Set<String> set2 = new HashSet<>(Arrays.asList(words2));
        
        Set<String> intersection = new HashSet<>(set1);
        intersection.retainAll(set2);
        
        Set<String> union = new HashSet<>(set1);
        union.addAll(set2);
        
        return union.isEmpty() ? 0.0 : (double) intersection.size() / union.size();
    }
    
    // Simplified save and load methods (memory persistence disabled for now)
    private void saveAllMemories() {
        // Note: Simplified version - persistence disabled to avoid external dependencies
        // In a full implementation, this would use Java serialization or simple text files
        System.out.println("Memory save requested - simplified version active");
    }

    private void loadAllMemories() {
        // Note: Simplified version - persistence disabled to avoid external dependencies
        // In a full implementation, this would load from Java serialization or simple text files
        System.out.println("Memory load requested - simplified version active");
    }
    
    /**
     * Get memory statistics for debugging
     */
    public String getMemoryStats() {
        return String.format(
            "Memory Stats:\n" +
            "- Coding Patterns: %d\n" +
            "- Project Contexts: %d\n" +
            "- Conversation History: %d\n" +
            "- User Preferences: %s",
            codingPatterns.size(),
            projectContexts.size(),
            conversationHistory.size(),
            userPreferences.codingStyle != null ? "Configured" : "Default"
        );
    }
}
