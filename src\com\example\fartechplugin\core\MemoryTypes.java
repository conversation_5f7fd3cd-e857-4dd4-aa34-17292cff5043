package com.example.fartechplugin.core;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Data classes for FarTech AI Memory System
 */

/**
 * Represents a learned coding pattern from user interactions
 */
class CodingPattern {
    public String fileType;
    public String patternType;
    public List<String> commonQueries;
    public List<String> successfulResponses;
    public double confidence;
    public int usageCount;
    public LocalDateTime lastUsed;
    
    public CodingPattern(String fileType, String patternType) {
        this.fileType = fileType;
        this.patternType = patternType;
        this.commonQueries = new ArrayList<>();
        this.successfulResponses = new ArrayList<>();
        this.confidence = 0.5; // Start with neutral confidence
        this.usageCount = 0;
        this.lastUsed = LocalDateTime.now();
    }
    
    public void addInteraction(String query, String response, String feedback) {
        if (!commonQueries.contains(query)) {
            commonQueries.add(query);
        }
        
        if (isPositiveFeedback(feedback)) {
            if (!successfulResponses.contains(response)) {
                successfulResponses.add(response);
            }
        }
        
        usageCount++;
        lastUsed = LocalDateTime.now();
    }
    
    public void updateConfidence(String feedback) {
        if (isPositiveFeedback(feedback)) {
            confidence = Math.min(1.0, confidence + 0.1);
        } else if (isNegativeFeedback(feedback)) {
            confidence = Math.max(0.0, confidence - 0.05);
        }
    }
    
    public List<String> getCommonQueries() {
        return new ArrayList<>(commonQueries);
    }
    
    private boolean isPositiveFeedback(String feedback) {
        if (feedback == null) return false;
        String lower = feedback.toLowerCase();
        return lower.contains("good") || lower.contains("perfect") || 
               lower.contains("excellent") || lower.contains("correct") ||
               lower.contains("thanks") || lower.contains("helpful");
    }
    
    private boolean isNegativeFeedback(String feedback) {
        if (feedback == null) return false;
        String lower = feedback.toLowerCase();
        return lower.contains("wrong") || lower.contains("bad") || 
               lower.contains("incorrect") || lower.contains("error") ||
               lower.contains("fix") || lower.contains("problem");
    }
}

/**
 * Represents project-specific context and patterns
 */
class ProjectContext {
    public String projectName;
    public Set<String> fileTypes;
    public List<String> commonQueries;
    public Map<String, Integer> queryFrequency;
    public LocalDateTime lastAccessed;
    public LocalDateTime created;
    
    public ProjectContext(String projectName) {
        this.projectName = projectName;
        this.fileTypes = new HashSet<>();
        this.commonQueries = new ArrayList<>();
        this.queryFrequency = new HashMap<>();
        this.lastAccessed = LocalDateTime.now();
        this.created = LocalDateTime.now();
    }
    
    public void addFileType(String fileType) {
        fileTypes.add(fileType);
    }
    
    public void addCommonQuery(String query) {
        if (!commonQueries.contains(query)) {
            commonQueries.add(query);
        }
        queryFrequency.put(query, queryFrequency.getOrDefault(query, 0) + 1);
        
        // Keep only top 20 most common queries
        if (commonQueries.size() > 20) {
            commonQueries.sort((q1, q2) -> 
                Integer.compare(queryFrequency.getOrDefault(q2, 0), 
                               queryFrequency.getOrDefault(q1, 0)));
            commonQueries = new ArrayList<>(commonQueries.subList(0, 20));
        }
    }
    
    public void updateLastAccessed() {
        this.lastAccessed = LocalDateTime.now();
    }
}

/**
 * Represents user's coding preferences and style
 */
class UserPreferences {
    public String codingStyle;
    public Set<String> preferredPatterns;
    public String commentStyle;
    public Map<String, String> languagePreferences;
    public boolean verboseExplanations;
    public boolean includeComments;
    public boolean preferFunctional;
    
    public UserPreferences() {
        this.codingStyle = "clean_code"; // Default
        this.preferredPatterns = new HashSet<>();
        this.commentStyle = "javadoc";
        this.languagePreferences = new HashMap<>();
        this.verboseExplanations = true;
        this.includeComments = true;
        this.preferFunctional = false;
        
        // Initialize with common patterns
        preferredPatterns.add("singleton");
        preferredPatterns.add("factory");
        preferredPatterns.add("builder");
    }
    
    public void updateFromFeedback(String feedback, String context) {
        String lower = feedback.toLowerCase();
        
        if (lower.contains("too verbose")) {
            verboseExplanations = false;
        } else if (lower.contains("more detail")) {
            verboseExplanations = true;
        }
        
        if (lower.contains("no comments")) {
            includeComments = false;
        } else if (lower.contains("add comments")) {
            includeComments = true;
        }
        
        if (lower.contains("functional") && lower.contains("prefer")) {
            preferFunctional = true;
        }
    }
}

/**
 * Represents a stored conversation memory
 */
class ConversationMemory {
    public String projectName;
    public String userQuery;
    public String aiResponse;
    public String userFeedback;
    public LocalDateTime timestamp;
    public String fileContext;
    public double relevanceScore;
    
    public ConversationMemory(String projectName, String userQuery, String aiResponse, 
                            String userFeedback, LocalDateTime timestamp) {
        this.projectName = projectName;
        this.userQuery = userQuery;
        this.aiResponse = aiResponse;
        this.userFeedback = userFeedback;
        this.timestamp = timestamp;
        this.relevanceScore = calculateInitialRelevance();
    }
    
    private double calculateInitialRelevance() {
        double score = 0.5; // Base relevance
        
        if (userFeedback != null) {
            String lower = userFeedback.toLowerCase();
            if (lower.contains("good") || lower.contains("perfect")) {
                score += 0.3;
            } else if (lower.contains("wrong") || lower.contains("bad")) {
                score -= 0.2;
            }
        }
        
        // Newer conversations are more relevant
        long daysOld = java.time.Duration.between(timestamp, LocalDateTime.now()).toDays();
        score -= (daysOld * 0.01); // Decay over time
        
        return Math.max(0.0, Math.min(1.0, score));
    }
}

/**
 * Container for relevant memories for current context
 */
class MemoryContext {
    public List<CodingPattern> relevantPatterns;
    public ProjectContext projectContext;
    public List<ConversationMemory> similarConversations;
    public UserPreferences userPreferences;
    
    public MemoryContext() {
        this.relevantPatterns = new ArrayList<>();
        this.similarConversations = new ArrayList<>();
    }
    
    public boolean hasRelevantMemories() {
        return !relevantPatterns.isEmpty() || 
               projectContext != null || 
               !similarConversations.isEmpty();
    }
    
    public String generateContextSummary() {
        StringBuilder summary = new StringBuilder();
        
        if (projectContext != null) {
            summary.append("Project: ").append(projectContext.projectName).append(" ");
        }
        
        if (!relevantPatterns.isEmpty()) {
            summary.append("Patterns: ").append(relevantPatterns.size()).append(" ");
        }
        
        if (!similarConversations.isEmpty()) {
            summary.append("Similar conversations: ").append(similarConversations.size());
        }
        
        return summary.toString().trim();
    }
}

/**
 * Memory learning feedback types
 */
enum FeedbackType {
    POSITIVE("positive"),
    NEGATIVE("negative"),
    NEUTRAL("neutral"),
    CORRECTION("correction");
    
    private final String value;
    
    FeedbackType(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
    
    public static FeedbackType fromString(String feedback) {
        if (feedback == null) return NEUTRAL;
        
        String lower = feedback.toLowerCase();
        if (lower.contains("good") || lower.contains("perfect") || lower.contains("excellent")) {
            return POSITIVE;
        } else if (lower.contains("wrong") || lower.contains("bad") || lower.contains("incorrect")) {
            return NEGATIVE;
        } else if (lower.contains("fix") || lower.contains("change") || lower.contains("instead")) {
            return CORRECTION;
        }
        
        return NEUTRAL;
    }
}

/**
 * Memory statistics for monitoring and debugging
 */
class MemoryStats {
    public int totalPatterns;
    public int totalProjects;
    public int totalConversations;
    public double averageConfidence;
    public LocalDateTime lastUpdate;
    public Map<String, Integer> patternsByType;
    public Map<String, Integer> queriesByProject;
    
    public MemoryStats() {
        this.patternsByType = new HashMap<>();
        this.queriesByProject = new HashMap<>();
        this.lastUpdate = LocalDateTime.now();
    }
    
    public void updateStats(Map<String, CodingPattern> patterns, 
                           Map<String, ProjectContext> projects,
                           List<ConversationMemory> conversations) {
        this.totalPatterns = patterns.size();
        this.totalProjects = projects.size();
        this.totalConversations = conversations.size();
        
        // Calculate average confidence
        this.averageConfidence = patterns.values().stream()
            .mapToDouble(p -> p.confidence)
            .average()
            .orElse(0.0);
        
        // Update pattern type distribution
        patternsByType.clear();
        patterns.values().forEach(pattern -> 
            patternsByType.merge(pattern.patternType, 1, Integer::sum));
        
        // Update queries by project
        queriesByProject.clear();
        projects.values().forEach(project -> 
            queriesByProject.put(project.projectName, project.commonQueries.size()));
        
        this.lastUpdate = LocalDateTime.now();
    }
    
    @Override
    public String toString() {
        return String.format(
            "Memory Statistics:\n" +
            "- Total Patterns: %d\n" +
            "- Total Projects: %d\n" +
            "- Total Conversations: %d\n" +
            "- Average Confidence: %.2f\n" +
            "- Last Update: %s",
            totalPatterns, totalProjects, totalConversations, 
            averageConfidence, lastUpdate.toString()
        );
    }
}
