package com.example.fartechplugin.core;

/**
 * Recognizes patterns in user interactions
 */
public class PatternRecognizer {
    
    public String getSuggestions(String prompt, String projectName) {
        StringBuilder suggestions = new StringBuilder();
        
        String lower = prompt.toLowerCase();
        
        // Code generation patterns
        if (lower.contains("create") && lower.contains("class")) {
            suggestions.append("- Consider using design patterns like Builder or Factory\n");
        }
        
        // Testing patterns
        if (lower.contains("test") || lower.contains("junit")) {
            suggestions.append("- Include edge cases and error scenarios\n");
            suggestions.append("- Consider using mocking for dependencies\n");
        }
        
        // Documentation patterns
        if (lower.contains("document") || lower.contains("javadoc")) {
            suggestions.append("- Include parameter descriptions and return values\n");
            suggestions.append("- Add usage examples\n");
        }
        
        // Refactoring patterns
        if (lower.contains("refactor") || lower.contains("improve")) {
            suggestions.append("- Focus on single responsibility principle\n");
            suggestions.append("- Consider extracting methods for better readability\n");
        }
        
        return suggestions.toString();
    }
}
