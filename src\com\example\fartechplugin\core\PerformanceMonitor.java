package com.example.fartechplugin.core;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Performance Monitor
 */
public class PerformanceMonitor {
    
    private final Map<String, Long> taskStartTimes;
    private final List<PerformanceEntry> performanceHistory;
    private long totalTasks;
    private long totalExecutionTime;
    
    public PerformanceMonitor() {
        this.taskStartTimes = new HashMap<>();
        this.performanceHistory = new ArrayList<>();
        this.totalTasks = 0;
        this.totalExecutionTime = 0;
    }
    
    public void startTask(String taskId) {
        taskStartTimes.put(taskId, System.currentTimeMillis());
    }
    
    public void endTask() {
        // End the most recent task
        if (!taskStartTimes.isEmpty()) {
            String lastTask = taskStartTimes.keySet().iterator().next();
            endTask(lastTask);
        }
    }
    
    public void endTask(String taskId) {
        Long startTime = taskStartTimes.remove(taskId);
        if (startTime != null) {
            long duration = System.currentTimeMillis() - startTime;
            
            PerformanceEntry entry = new PerformanceEntry(taskId, duration, LocalDateTime.now());
            performanceHistory.add(entry);
            
            totalTasks++;
            totalExecutionTime += duration;
        }
    }
    
    public PerformanceMetrics getMetrics() {
        PerformanceMetrics metrics = new PerformanceMetrics();
        metrics.setTotalTasks(totalTasks);
        metrics.setTotalExecutionTime(totalExecutionTime);
        metrics.setAverageExecutionTime(totalTasks > 0 ? totalExecutionTime / totalTasks : 0);
        
        // Calculate recent performance
        if (performanceHistory.size() >= 10) {
            List<PerformanceEntry> recent = performanceHistory.subList(performanceHistory.size() - 10, performanceHistory.size());
            long recentTotal = recent.stream().mapToLong(PerformanceEntry::getDuration).sum();
            metrics.setRecentAverageTime(recentTotal / recent.size());
        }
        
        return metrics;
    }
    
    public void shutdown() {
        taskStartTimes.clear();
    }
}

class PerformanceEntry {
    private final String taskId;
    private final long duration;
    private final LocalDateTime timestamp;
    
    public PerformanceEntry(String taskId, long duration, LocalDateTime timestamp) {
        this.taskId = taskId;
        this.duration = duration;
        this.timestamp = timestamp;
    }
    
    public String getTaskId() { return taskId; }
    public long getDuration() { return duration; }
    public LocalDateTime getTimestamp() { return timestamp; }
}

class PerformanceMetrics {
    private long totalTasks;
    private long totalExecutionTime;
    private long averageExecutionTime;
    private long recentAverageTime;
    
    // Getters and setters
    public long getTotalTasks() { return totalTasks; }
    public void setTotalTasks(long totalTasks) { this.totalTasks = totalTasks; }
    
    public long getTotalExecutionTime() { return totalExecutionTime; }
    public void setTotalExecutionTime(long totalExecutionTime) { this.totalExecutionTime = totalExecutionTime; }
    
    public long getAverageExecutionTime() { return averageExecutionTime; }
    public void setAverageExecutionTime(long averageExecutionTime) { this.averageExecutionTime = averageExecutionTime; }
    
    public long getRecentAverageTime() { return recentAverageTime; }
    public void setRecentAverageTime(long recentAverageTime) { this.recentAverageTime = recentAverageTime; }
}
