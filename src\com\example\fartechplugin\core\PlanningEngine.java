package com.example.fartechplugin.core;

import java.util.*;

/**
 * Planning Engine for AI Agent
 * FR.A.2: Perform task decomposition and planning based on user intent and available tools
 */
public class PlanningEngine {
    
    private final Map<IntentType, PlanningStrategy> strategies;
    
    public PlanningEngine() {
        this.strategies = new HashMap<>();
        initializeStrategies();
    }
    
    /**
     * Create execution plan based on user intent and context
     */
    public ExecutionPlan createPlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry) {
        PlanningStrategy strategy = strategies.get(intent.getType());
        if (strategy == null) {
            strategy = strategies.get(IntentType.GENERAL);
        }
        
        return strategy.createPlan(intent, context, toolRegistry);
    }
    
    /**
     * Create alternative plan when original plan fails
     */
    public ExecutionPlan createAlternativePlan(UserIntent intent, IDEContext context, 
                                             ToolRegistry toolRegistry, String failureReason) {
        PlanningStrategy strategy = strategies.get(intent.getType());
        if (strategy == null) {
            return null;
        }
        
        return strategy.createAlternativePlan(intent, context, toolRegistry, failureReason);
    }
    
    private void initializeStrategies() {
        strategies.put(IntentType.CREATE, new CreatePlanningStrategy());
        strategies.put(IntentType.READ, new ReadPlanningStrategy());
        strategies.put(IntentType.UPDATE, new UpdatePlanningStrategy());
        strategies.put(IntentType.DELETE, new DeletePlanningStrategy());
        strategies.put(IntentType.GIT_OPERATION, new GitPlanningStrategy());
        strategies.put(IntentType.EXECUTE, new ExecutePlanningStrategy());
        strategies.put(IntentType.GENERAL, new GeneralPlanningStrategy());
    }
}

/**
 * Base interface for planning strategies
 */
interface PlanningStrategy {
    ExecutionPlan createPlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry);
    ExecutionPlan createAlternativePlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry, String failureReason);
}

/**
 * Planning strategy for CREATE operations
 */
class CreatePlanningStrategy implements PlanningStrategy {
    
    @Override
    public ExecutionPlan createPlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry) {
        ExecutionPlan plan = new ExecutionPlan();
        String prompt = intent.getOriginalPrompt().toLowerCase();
        
        // Step 1: Analyze what to create
        plan.addStep(new ExecutionStep(
            "Analyze creation request",
            "AnalyzeCodeTool",
            Map.of("prompt", intent.getOriginalPrompt(), "context", context),
            false
        ));
        
        // Step 2: Check if target location exists
        if (prompt.contains("file") || prompt.contains("class") || prompt.contains("component")) {
            plan.addStep(new ExecutionStep(
                "Check target directory",
                "ReadFileTool",
                Map.of("path", extractTargetPath(intent)),
                false
            ));
        }
        
        // Step 3: Generate content
        plan.addStep(new ExecutionStep(
            "Generate new content",
            "GenerateCodeTool",
            Map.of("type", extractFileType(intent), "requirements", intent.getOriginalPrompt()),
            false
        ));
        
        // Step 4: Create file (requires approval)
        plan.addStep(new ExecutionStep(
            "Create new file",
            "WriteFileTool",
            Map.of("path", extractTargetPath(intent), "requiresApproval", true),
            true
        ));
        
        return plan;
    }
    
    @Override
    public ExecutionPlan createAlternativePlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry, String failureReason) {
        ExecutionPlan plan = new ExecutionPlan();
        
        // If file already exists, try different name
        if (failureReason.contains("already exists")) {
            plan.addStep(new ExecutionStep(
                "Generate alternative filename",
                "GenerateAlternativeNameTool",
                Map.of("originalPath", extractTargetPath(intent)),
                false
            ));
        }
        
        return plan;
    }
    
    private String extractTargetPath(UserIntent intent) {
        // Extract file path from intent parameters or prompt
        Map<String, Object> params = intent.getParameters();
        if (params.containsKey("path")) {
            return (String) params.get("path");
        }
        
        // Parse from prompt
        String prompt = intent.getOriginalPrompt();
        if (prompt.contains("/")) {
            // Extract path pattern
            return "src/main/java/"; // Default for now
        }
        
        return "src/";
    }
    
    private String extractFileType(UserIntent intent) {
        Map<String, Object> params = intent.getParameters();
        if (params.containsKey("fileType")) {
            return (String) params.get("fileType");
        }
        return "java"; // Default
    }
}

/**
 * Planning strategy for READ operations
 */
class ReadPlanningStrategy implements PlanningStrategy {
    
    @Override
    public ExecutionPlan createPlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry) {
        ExecutionPlan plan = new ExecutionPlan();
        
        // Step 1: Read target file
        plan.addStep(new ExecutionStep(
            "Read file content",
            "ReadFileTool",
            Map.of("path", extractTargetFile(intent, context)),
            false
        ));
        
        // Step 2: Analyze code structure
        plan.addStep(new ExecutionStep(
            "Analyze code structure",
            "AnalyzeCodeTool",
            Map.of("analysisType", "structure"),
            false
        ));
        
        // Step 3: Generate explanation
        plan.addStep(new ExecutionStep(
            "Generate explanation",
            "ExplainCodeTool",
            Map.of("focus", extractAnalysisFocus(intent)),
            false
        ));
        
        return plan;
    }
    
    @Override
    public ExecutionPlan createAlternativePlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry, String failureReason) {
        ExecutionPlan plan = new ExecutionPlan();
        
        if (failureReason.contains("file not found")) {
            // Try to find similar files
            plan.addStep(new ExecutionStep(
                "Search for similar files",
                "FindFilesTool",
                Map.of("pattern", extractFileName(intent)),
                false
            ));
        }
        
        return plan;
    }
    
    private String extractTargetFile(UserIntent intent, IDEContext context) {
        // Try to extract from current context first
        if (context.getCurrentFile() != null) {
            return context.getCurrentFile();
        }
        
        // Parse from prompt
        String prompt = intent.getOriginalPrompt();
        // Add file extraction logic
        return "src/main/java/Main.java"; // Default
    }
    
    private String extractAnalysisFocus(UserIntent intent) {
        String prompt = intent.getOriginalPrompt().toLowerCase();
        if (prompt.contains("method")) return "methods";
        if (prompt.contains("class")) return "classes";
        if (prompt.contains("variable")) return "variables";
        return "general";
    }
    
    private String extractFileName(UserIntent intent) {
        // Extract filename pattern from prompt
        return "*.java";
    }
}

/**
 * Planning strategy for UPDATE operations
 */
class UpdatePlanningStrategy implements PlanningStrategy {
    
    @Override
    public ExecutionPlan createPlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry) {
        ExecutionPlan plan = new ExecutionPlan();
        
        // Step 1: Read current file
        plan.addStep(new ExecutionStep(
            "Read current file",
            "ReadFileTool",
            Map.of("path", extractTargetFile(intent, context)),
            false
        ));
        
        // Step 2: Analyze required changes
        plan.addStep(new ExecutionStep(
            "Analyze required changes",
            "AnalyzeCodeTool",
            Map.of("changeType", extractChangeType(intent)),
            false
        ));
        
        // Step 3: Generate modified content
        plan.addStep(new ExecutionStep(
            "Generate modified content",
            "ModifyCodeTool",
            Map.of("modifications", intent.getOriginalPrompt()),
            false
        ));
        
        // Step 4: Apply changes (requires approval)
        plan.addStep(new ExecutionStep(
            "Apply changes to file",
            "WriteFileTool",
            Map.of("showDiff", true),
            true
        ));
        
        return plan;
    }
    
    @Override
    public ExecutionPlan createAlternativePlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry, String failureReason) {
        ExecutionPlan plan = new ExecutionPlan();
        
        // Try more conservative approach
        plan.addStep(new ExecutionStep(
            "Create backup before modification",
            "BackupFileTool",
            Map.of("path", extractTargetFile(intent, context)),
            false
        ));
        
        return plan;
    }
    
    private String extractTargetFile(UserIntent intent, IDEContext context) {
        return context.getCurrentFile() != null ? context.getCurrentFile() : "src/main/java/Main.java";
    }
    
    private String extractChangeType(UserIntent intent) {
        String prompt = intent.getOriginalPrompt().toLowerCase();
        if (prompt.contains("refactor")) return "refactor";
        if (prompt.contains("fix")) return "bugfix";
        if (prompt.contains("add")) return "addition";
        return "modification";
    }
}

/**
 * Planning strategies for other intent types
 */
class DeletePlanningStrategy implements PlanningStrategy {
    @Override
    public ExecutionPlan createPlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry) {
        ExecutionPlan plan = new ExecutionPlan();
        
        // Always require approval for deletions
        plan.addStep(new ExecutionStep(
            "Confirm deletion target",
            "ConfirmDeletionTool",
            Map.of("target", extractDeletionTarget(intent)),
            true
        ));
        
        plan.addStep(new ExecutionStep(
            "Delete file/directory",
            "DeleteFileTool",
            Map.of("path", extractDeletionTarget(intent)),
            true
        ));
        
        return plan;
    }
    
    @Override
    public ExecutionPlan createAlternativePlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry, String failureReason) {
        return null; // No alternative for deletion
    }
    
    private String extractDeletionTarget(UserIntent intent) {
        return "temp.txt"; // Extract from prompt
    }
}

class GitPlanningStrategy implements PlanningStrategy {
    @Override
    public ExecutionPlan createPlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry) {
        ExecutionPlan plan = new ExecutionPlan();
        
        plan.addStep(new ExecutionStep(
            "Check git status",
            "GitStatusTool",
            new HashMap<>(),
            false
        ));
        
        plan.addStep(new ExecutionStep(
            "Execute git operation",
            "GitCommitTool",
            Map.of("message", extractCommitMessage(intent)),
            true
        ));
        
        return plan;
    }
    
    @Override
    public ExecutionPlan createAlternativePlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry, String failureReason) {
        return null;
    }
    
    private String extractCommitMessage(UserIntent intent) {
        return "Auto-commit via FarTech AI";
    }
}

class ExecutePlanningStrategy implements PlanningStrategy {
    @Override
    public ExecutionPlan createPlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry) {
        ExecutionPlan plan = new ExecutionPlan();
        
        plan.addStep(new ExecutionStep(
            "Execute command",
            "ExecuteCommandTool",
            Map.of("command", extractCommand(intent)),
            false
        ));
        
        return plan;
    }
    
    @Override
    public ExecutionPlan createAlternativePlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry, String failureReason) {
        return null;
    }
    
    private String extractCommand(UserIntent intent) {
        return "echo 'Hello World'";
    }
}

class GeneralPlanningStrategy implements PlanningStrategy {
    @Override
    public ExecutionPlan createPlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry) {
        ExecutionPlan plan = new ExecutionPlan();
        
        plan.addStep(new ExecutionStep(
            "Provide general assistance",
            "GeneralAssistanceTool",
            Map.of("query", intent.getOriginalPrompt()),
            false
        ));
        
        return plan;
    }
    
    @Override
    public ExecutionPlan createAlternativePlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry, String failureReason) {
        return null;
    }
}
