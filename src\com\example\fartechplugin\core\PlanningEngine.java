package com.example.fartechplugin.core;

import java.util.*;

/**
 * Planning Engine for AI Agent
 * FR.A.2: Perform task decomposition and planning based on user intent and available tools
 */
public class PlanningEngine {
    
    private final Map<IntentType, PlanningStrategy> strategies;
    
    public PlanningEngine() {
        this.strategies = new HashMap<>();
        initializeStrategies();
    }
    
    /**
     * Create execution plan based on user intent and context
     */
    public ExecutionPlan createPlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry) {
        PlanningStrategy strategy = strategies.get(intent.getType());
        if (strategy == null) {
            strategy = strategies.get(IntentType.GENERAL);
        }
        
        return strategy.createPlan(intent, context, toolRegistry);
    }
    
    /**
     * Create alternative plan when original plan fails
     */
    public ExecutionPlan createAlternativePlan(UserIntent intent, IDEContext context, 
                                             ToolRegistry toolRegistry, String failureReason) {
        PlanningStrategy strategy = strategies.get(intent.getType());
        if (strategy == null) {
            return null;
        }
        
        return strategy.createAlternativePlan(intent, context, toolRegistry, failureReason);
    }
    
    private void initializeStrategies() {
        strategies.put(IntentType.CREATE, new CreatePlanningStrategy());
        strategies.put(IntentType.READ, new ReadPlanningStrategy());
        strategies.put(IntentType.UPDATE, new UpdatePlanningStrategy());
        strategies.put(IntentType.DELETE, new DeletePlanningStrategy());
        strategies.put(IntentType.GIT_OPERATION, new GitPlanningStrategy());
        strategies.put(IntentType.EXECUTE, new ExecutePlanningStrategy());
        strategies.put(IntentType.GENERAL, new GeneralPlanningStrategy());
    }
}

/**
 * Base interface for planning strategies
 */
interface PlanningStrategy {
    ExecutionPlan createPlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry);
    ExecutionPlan createAlternativePlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry, String failureReason);
}

/**
 * Planning strategy for CREATE operations
 */
class CreatePlanningStrategy implements PlanningStrategy {
    
    @Override
    public ExecutionPlan createPlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry) {
        ExecutionPlan plan = new ExecutionPlan();
        String prompt = intent.getOriginalPrompt().toLowerCase();
        
        // Step 1: Analyze what to create
        Map<String, Object> analyzeParams = new HashMap<>();
        analyzeParams.put("prompt", intent.getOriginalPrompt());
        analyzeParams.put("context", context);
        plan.addStep(new ExecutionStep(
            "Analyze creation request",
            "AnalyzeCodeTool",
            analyzeParams,
            false
        ));

        // Step 2: Check if target location exists
        if (prompt.contains("file") || prompt.contains("class") || prompt.contains("component")) {
            Map<String, Object> readParams = new HashMap<>();
            readParams.put("path", extractTargetPath(intent));
            plan.addStep(new ExecutionStep(
                "Check target directory",
                "ReadFileTool",
                readParams,
                false
            ));
        }

        // Step 3: Generate content
        Map<String, Object> generateParams = new HashMap<>();
        generateParams.put("type", extractFileType(intent));
        generateParams.put("requirements", intent.getOriginalPrompt());
        plan.addStep(new ExecutionStep(
            "Generate new content",
            "GenerateCodeTool",
            generateParams,
            false
        ));

        // Step 4: Create file (requires approval)
        Map<String, Object> createParams = new HashMap<>();
        createParams.put("path", extractTargetPath(intent));
        createParams.put("requiresApproval", true);
        plan.addStep(new ExecutionStep(
            "Create new file",
            "WriteFileTool",
            createParams,
            true
        ));
        
        return plan;
    }
    
    @Override
    public ExecutionPlan createAlternativePlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry, String failureReason) {
        ExecutionPlan plan = new ExecutionPlan();
        
        // If file already exists, try different name
        if (failureReason.contains("already exists")) {
            Map<String, Object> altParams = new HashMap<>();
            altParams.put("originalPath", extractTargetPath(intent));
            plan.addStep(new ExecutionStep(
                "Generate alternative filename",
                "GenerateAlternativeNameTool",
                altParams,
                false
            ));
        }
        
        return plan;
    }
    
    private String extractTargetPath(UserIntent intent) {
        // Extract file path from intent parameters or prompt
        Map<String, Object> params = intent.getParameters();
        if (params.containsKey("path")) {
            return (String) params.get("path");
        }
        
        // Parse from prompt
        String prompt = intent.getOriginalPrompt();
        if (prompt.contains("/")) {
            // Extract path pattern
            return "src/main/java/"; // Default for now
        }
        
        return "src/";
    }
    
    private String extractFileType(UserIntent intent) {
        Map<String, Object> params = intent.getParameters();
        if (params.containsKey("fileType")) {
            return (String) params.get("fileType");
        }
        return "java"; // Default
    }
}

/**
 * Planning strategy for READ operations
 */
class ReadPlanningStrategy implements PlanningStrategy {
    
    @Override
    public ExecutionPlan createPlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry) {
        ExecutionPlan plan = new ExecutionPlan();
        
        // Step 1: Read target file
        Map<String, Object> readParams = new HashMap<>();
        readParams.put("path", extractTargetFile(intent, context));
        plan.addStep(new ExecutionStep(
            "Read file content",
            "ReadFileTool",
            readParams,
            false
        ));

        // Step 2: Analyze code structure
        Map<String, Object> analyzeParams = new HashMap<>();
        analyzeParams.put("analysisType", "structure");
        plan.addStep(new ExecutionStep(
            "Analyze code structure",
            "AnalyzeCodeTool",
            analyzeParams,
            false
        ));

        // Step 3: Generate explanation
        Map<String, Object> explainParams = new HashMap<>();
        explainParams.put("focus", extractAnalysisFocus(intent));
        plan.addStep(new ExecutionStep(
            "Generate explanation",
            "ExplainCodeTool",
            explainParams,
            false
        ));
        
        return plan;
    }
    
    @Override
    public ExecutionPlan createAlternativePlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry, String failureReason) {
        ExecutionPlan plan = new ExecutionPlan();
        
        if (failureReason.contains("file not found")) {
            // Try to find similar files
            Map<String, Object> findParams = new HashMap<>();
            findParams.put("pattern", extractFileName(intent));
            plan.addStep(new ExecutionStep(
                "Search for similar files",
                "FindFilesTool",
                findParams,
                false
            ));
        }
        
        return plan;
    }
    
    private String extractTargetFile(UserIntent intent, IDEContext context) {
        // Try to extract from current context first
        if (context.getCurrentFile() != null) {
            return context.getCurrentFile();
        }
        
        // Parse from prompt
        String prompt = intent.getOriginalPrompt();
        // Add file extraction logic
        return "src/main/java/Main.java"; // Default
    }
    
    private String extractAnalysisFocus(UserIntent intent) {
        String prompt = intent.getOriginalPrompt().toLowerCase();
        if (prompt.contains("method")) return "methods";
        if (prompt.contains("class")) return "classes";
        if (prompt.contains("variable")) return "variables";
        return "general";
    }
    
    private String extractFileName(UserIntent intent) {
        // Extract filename pattern from prompt
        return "*.java";
    }
}

/**
 * Planning strategy for UPDATE operations
 */
class UpdatePlanningStrategy implements PlanningStrategy {
    
    @Override
    public ExecutionPlan createPlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry) {
        ExecutionPlan plan = new ExecutionPlan();
        
        // Step 1: Read current file
        Map<String, Object> readParams = new HashMap<>();
        readParams.put("path", extractTargetFile(intent, context));
        plan.addStep(new ExecutionStep(
            "Read current file",
            "ReadFileTool",
            readParams,
            false
        ));

        // Step 2: Analyze required changes
        Map<String, Object> analyzeParams = new HashMap<>();
        analyzeParams.put("changeType", extractChangeType(intent));
        plan.addStep(new ExecutionStep(
            "Analyze required changes",
            "AnalyzeCodeTool",
            analyzeParams,
            false
        ));

        // Step 3: Generate modified content
        Map<String, Object> modifyParams = new HashMap<>();
        modifyParams.put("modifications", intent.getOriginalPrompt());
        plan.addStep(new ExecutionStep(
            "Generate modified content",
            "ModifyCodeTool",
            modifyParams,
            false
        ));
        
        // Step 4: Apply changes (requires approval)
        Map<String, Object> writeParams = new HashMap<>();
        writeParams.put("showDiff", true);
        plan.addStep(new ExecutionStep(
            "Apply changes to file",
            "WriteFileTool",
            writeParams,
            true
        ));
        
        return plan;
    }
    
    @Override
    public ExecutionPlan createAlternativePlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry, String failureReason) {
        ExecutionPlan plan = new ExecutionPlan();
        
        // Try more conservative approach
        Map<String, Object> backupParams = new HashMap<>();
        backupParams.put("path", extractTargetFile(intent, context));
        plan.addStep(new ExecutionStep(
            "Create backup before modification",
            "BackupFileTool",
            backupParams,
            false
        ));
        
        return plan;
    }
    
    private String extractTargetFile(UserIntent intent, IDEContext context) {
        return context.getCurrentFile() != null ? context.getCurrentFile() : "src/main/java/Main.java";
    }
    
    private String extractChangeType(UserIntent intent) {
        String prompt = intent.getOriginalPrompt().toLowerCase();
        if (prompt.contains("refactor")) return "refactor";
        if (prompt.contains("fix")) return "bugfix";
        if (prompt.contains("add")) return "addition";
        return "modification";
    }
}

/**
 * Planning strategies for other intent types
 */
class DeletePlanningStrategy implements PlanningStrategy {
    @Override
    public ExecutionPlan createPlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry) {
        ExecutionPlan plan = new ExecutionPlan();
        
        // Always require approval for deletions
        Map<String, Object> confirmParams = new HashMap<>();
        confirmParams.put("target", extractDeletionTarget(intent));
        plan.addStep(new ExecutionStep(
            "Confirm deletion target",
            "ConfirmDeletionTool",
            confirmParams,
            true
        ));

        Map<String, Object> deleteParams = new HashMap<>();
        deleteParams.put("path", extractDeletionTarget(intent));
        plan.addStep(new ExecutionStep(
            "Delete file/directory",
            "DeleteFileTool",
            deleteParams,
            true
        ));
        
        return plan;
    }
    
    @Override
    public ExecutionPlan createAlternativePlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry, String failureReason) {
        return null; // No alternative for deletion
    }
    
    private String extractDeletionTarget(UserIntent intent) {
        return "temp.txt"; // Extract from prompt
    }
}

class GitPlanningStrategy implements PlanningStrategy {
    @Override
    public ExecutionPlan createPlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry) {
        ExecutionPlan plan = new ExecutionPlan();
        
        plan.addStep(new ExecutionStep(
            "Check git status",
            "GitStatusTool",
            new HashMap<>(),
            false
        ));
        
        Map<String, Object> gitParams = new HashMap<>();
        gitParams.put("message", extractCommitMessage(intent));
        plan.addStep(new ExecutionStep(
            "Execute git operation",
            "GitCommitTool",
            gitParams,
            true
        ));
        
        return plan;
    }
    
    @Override
    public ExecutionPlan createAlternativePlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry, String failureReason) {
        return null;
    }
    
    private String extractCommitMessage(UserIntent intent) {
        return "Auto-commit via FarTech AI";
    }
}

class ExecutePlanningStrategy implements PlanningStrategy {
    @Override
    public ExecutionPlan createPlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry) {
        ExecutionPlan plan = new ExecutionPlan();
        
        Map<String, Object> cmdParams = new HashMap<>();
        cmdParams.put("command", extractCommand(intent));
        plan.addStep(new ExecutionStep(
            "Execute command",
            "ExecuteCommandTool",
            cmdParams,
            false
        ));
        
        return plan;
    }
    
    @Override
    public ExecutionPlan createAlternativePlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry, String failureReason) {
        return null;
    }
    
    private String extractCommand(UserIntent intent) {
        return "echo 'Hello World'";
    }
}

class GeneralPlanningStrategy implements PlanningStrategy {
    @Override
    public ExecutionPlan createPlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry) {
        ExecutionPlan plan = new ExecutionPlan();
        
        Map<String, Object> assistParams = new HashMap<>();
        assistParams.put("query", intent.getOriginalPrompt());
        plan.addStep(new ExecutionStep(
            "Provide general assistance",
            "GeneralAssistanceTool",
            assistParams,
            false
        ));
        
        return plan;
    }
    
    @Override
    public ExecutionPlan createAlternativePlan(UserIntent intent, IDEContext context, ToolRegistry toolRegistry, String failureReason) {
        return null;
    }
}
