package com.example.fartechplugin.core;

import java.util.Properties;
import java.io.*;
import java.util.HashMap;
import java.util.Map;

/**
 * Manages plugin preferences and secure storage for API keys
 * Simplified version that works without Eclipse dependencies
 * Moved to core package for better organization
 */
public class PreferencesManager {

    private static final String PLUGIN_ID = "com.example.fartechplugin";
    private static final String PREF_CURRENT_PROVIDER = "currentProvider";
    private static final String PREF_AGENT_MODE = "agentMode";
    private static final String PREF_BASE_URL_PREFIX = "baseUrl.";
    private static final String PREF_MODEL_PREFIX = "model.";

    private Properties preferences;
    private Map<String, String> securePreferences;
    private File preferencesFile;
    private File securePreferencesFile;

    public PreferencesManager() {
        preferences = new Properties();
        securePreferences = new HashMap<>();

        // Initialize file paths
        String userHome = System.getProperty("user.home");
        File configDir = new File(userHome, ".fartech-ai");
        configDir.mkdirs();

        preferencesFile = new File(configDir, "preferences.properties");
        securePreferencesFile = new File(configDir, "secure.properties");

        // Load existing preferences
        loadPreferences();
        loadSecurePreferences();
    }

    /**
     * Load preferences from file
     */
    private void loadPreferences() {
        if (preferencesFile.exists()) {
            try (FileInputStream fis = new FileInputStream(preferencesFile)) {
                preferences.load(fis);
            } catch (IOException e) {
                System.err.println("Failed to load preferences: " + e.getMessage());
            }
        }
    }

    /**
     * Load secure preferences from file
     */
    private void loadSecurePreferences() {
        if (securePreferencesFile.exists()) {
            try (FileInputStream fis = new FileInputStream(securePreferencesFile)) {
                Properties secureProps = new Properties();
                secureProps.load(fis);
                for (String key : secureProps.stringPropertyNames()) {
                    securePreferences.put(key, secureProps.getProperty(key));
                }
            } catch (IOException e) {
                System.err.println("Failed to load secure preferences: " + e.getMessage());
            }
        }
    }

    /**
     * Save preferences to file
     */
    private void savePreferences() {
        try (FileOutputStream fos = new FileOutputStream(preferencesFile)) {
            preferences.store(fos, "FarTech AI Assistant Preferences");
        } catch (IOException e) {
            System.err.println("Failed to save preferences: " + e.getMessage());
        }
    }

    /**
     * Save secure preferences to file
     */
    private void saveSecurePreferences() {
        try (FileOutputStream fos = new FileOutputStream(securePreferencesFile)) {
            Properties secureProps = new Properties();
            for (Map.Entry<String, String> entry : securePreferences.entrySet()) {
                secureProps.setProperty(entry.getKey(), entry.getValue());
            }
            secureProps.store(fos, "FarTech AI Assistant Secure Preferences");
        } catch (IOException e) {
            System.err.println("Failed to save secure preferences: " + e.getMessage());
        }
    }

    /**
     * Initialize default preferences with FarTech AI provider
     */
    public void initializeDefaults() {
        // Set default values if not already set - hardcoded to FarTech AI
        if (!preferences.containsKey("api.base.url")) {
            preferences.setProperty("api.base.url", "https://ai.ad-ins.com/api");
        }
        if (!preferences.containsKey("model.name")) {
            preferences.setProperty("model.name", "Devstral Small");
        }
        if (!preferences.containsKey(PREF_AGENT_MODE)) {
            preferences.setProperty(PREF_AGENT_MODE, "false");
        }
        savePreferences();
    }

    /**
     * Reload preferences from file
     */
    public void reloadPreferences() {
        loadPreferences();
        loadSecurePreferences();
        System.out.println("Preferences reloaded");
    }

    /**
     * Save current provider
     */
    public void setCurrentProvider(String providerName) {
        preferences.setProperty(PREF_CURRENT_PROVIDER, providerName);
        savePreferences();
    }

    /**
     * Get current provider
     */
    public String getCurrentProvider() {
        return preferences.getProperty(PREF_CURRENT_PROVIDER, "OpenAI Compatible");
    }

    /**
     * Save agent mode state
     */
    public void setAgentMode(boolean enabled) {
        preferences.setProperty(PREF_AGENT_MODE, String.valueOf(enabled));
        savePreferences();
    }

    /**
     * Get agent mode state
     */
    public boolean getAgentMode() {
        return Boolean.parseBoolean(preferences.getProperty(PREF_AGENT_MODE, "false"));
    }
    
    /**
     * Save API key securely
     */
    public void setApiKey(String providerName, String apiKey) {
        try {
            if (apiKey == null || apiKey.trim().isEmpty()) {
                // Remove the key if empty
                securePreferences.remove(providerName);
            } else {
                securePreferences.put(providerName, apiKey);
            }
            saveSecurePreferences();
        } catch (Exception e) {
            System.err.println("Failed to store API key for " + providerName + ": " + e.getMessage());
        }
    }

    /**
     * Get API key securely
     */
    public String getApiKey(String providerName) {
        try {
            return securePreferences.get(providerName);
        } catch (Exception e) {
            System.err.println("Failed to retrieve API key for " + providerName + ": " + e.getMessage());
            return null;
        }
    }

    /**
     * Check if API key exists for provider
     */
    public boolean hasApiKey(String providerName) {
        String apiKey = getApiKey(providerName);
        return apiKey != null && !apiKey.trim().isEmpty();
    }

    /**
     * Remove API key for provider
     */
    public void removeApiKey(String providerName) {
        try {
            securePreferences.remove(providerName);
            saveSecurePreferences();
        } catch (Exception e) {
            System.err.println("Failed to remove API key for " + providerName + ": " + e.getMessage());
        }
    }

    /**
     * Clear all API keys (for security)
     */
    public void clearAllApiKeys() {
        try {
            securePreferences.clear();
            saveSecurePreferences();
        } catch (Exception e) {
            System.err.println("Failed to clear API keys: " + e.getMessage());
        }
    }

    /**
     * Get all provider names that have API keys
     */
    public String[] getConfiguredProviders() {
        try {
            return securePreferences.keySet().toArray(new String[0]);
        } catch (Exception e) {
            System.err.println("Failed to get configured providers: " + e.getMessage());
            return new String[0];
        }
    }
    
    /**
     * Save base URL for provider - BULLETPROOF with hardcoded endpoint
     */
    public void setBaseUrl(String providerName, String baseUrl) {
        // BULLETPROOF: Always force FarTech AI endpoint
        String interceptedUrl = "https://ai.ad-ins.com/api";

        preferences.setProperty(PREF_BASE_URL_PREFIX + providerName, interceptedUrl);
        savePreferences();
    }

    /**
     * Get base URL for provider - BULLETPROOF with hardcoded endpoint
     */
    public String getBaseUrl(String providerName) {
        // BULLETPROOF: Always return FarTech AI URL
        return "https://ai.ad-ins.com/api";
    }

    /**
     * Save model for provider
     */
    public void setModel(String providerName, String model) {
        if (model == null || model.trim().isEmpty()) {
            preferences.remove(PREF_MODEL_PREFIX + providerName);
        } else {
            preferences.setProperty(PREF_MODEL_PREFIX + providerName, model);
        }
        savePreferences();
    }

    /**
     * Get model for provider
     */
    public String getModel(String providerName) {
        return preferences.getProperty(PREF_MODEL_PREFIX + providerName);
    }

    /**
     * Load preferences into provider manager
     */
    public void loadIntoProviderManager(AIProviderManager providerManager) {
        // Set current provider
        String currentProvider = getCurrentProvider();
        providerManager.setCurrentProvider(currentProvider);

        // Load all API keys
        String[] configuredProviders = getConfiguredProviders();
        for (String providerName : configuredProviders) {
            String apiKey = getApiKey(providerName);
            if (apiKey != null) {
                providerManager.setApiKey(providerName, apiKey);
            }
        }

        // Load base URLs and models for all providers
        String[] allProviders = providerManager.getProviderNames();
        for (String providerName : allProviders) {
            String baseUrl = getBaseUrl(providerName);
            if (baseUrl != null) {
                providerManager.setBaseUrl(providerName, baseUrl);
            }

            String model = getModel(providerName);
            if (model != null) {
                providerManager.setModel(providerName, model);
            }
        }
    }
    
    /**
     * Save provider manager state to preferences
     */
    public void saveFromProviderManager(AIProviderManager providerManager) {
        // Save current provider
        setCurrentProvider(providerManager.getCurrentProviderName());

        // Save API keys, base URLs, and models for all providers
        String[] allProviders = providerManager.getProviderNames();
        for (String providerName : allProviders) {
            String apiKey = providerManager.getApiKey(providerName);
            if (apiKey != null && !apiKey.trim().isEmpty()) {
                setApiKey(providerName, apiKey);
            }

            String baseUrl = providerManager.getBaseUrl(providerName);
            if (baseUrl != null && !baseUrl.trim().isEmpty()) {
                setBaseUrl(providerName, baseUrl);
            }

            String model = providerManager.getModel(providerName);
            if (model != null && !model.trim().isEmpty()) {
                setModel(providerName, model);
            }
        }
    }
}
