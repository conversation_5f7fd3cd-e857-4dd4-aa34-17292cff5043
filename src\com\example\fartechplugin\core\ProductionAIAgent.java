package com.example.fartechplugin.core;

import java.util.*;

/**
 * Production-Ready AI Agent for FarTech
 * Implements full CRUDF operations with user approval workflows
 * Based on BRD requirements for IDE-integrated software development
 */
public class ProductionAIAgent {
    
    private final AIAgentCore agentCore;
    private final UserApprovalManager approvalManager;
    private final SecurityManager securityManager;
    private final PerformanceMonitor performanceMonitor;
    private final DiffViewer diffViewer;
    
    // Agent state
    private boolean agentModeEnabled;
    private AgentTask currentTask;
    private List<String> executionLog;
    
    public ProductionAIAgent() {
        this.agentCore = new AIAgentCore();
        this.approvalManager = new UserApprovalManager();
        this.securityManager = new SecurityManager();
        this.performanceMonitor = new PerformanceMonitor();
        this.diffViewer = new DiffViewer();
        this.executionLog = new ArrayList<>();
        this.agentModeEnabled = false;
    }
    
    /**
     * Main entry point for processing user requests
     * FR.U.1: Always require explicit user approval for any proposed modifications
     */
    public AgentResponse processRequest(String userPrompt, IDEContext ideContext) {
        try {
            // Start performance monitoring
            performanceMonitor.startTask(userPrompt);
            
            // Security check
            if (!securityManager.validateRequest(userPrompt, ideContext)) {
                return AgentResponse.error("Request blocked by security policy");
            }
            
            // Process with agent core
            AgentResponse response = agentCore.processUserRequest(userPrompt, ideContext);
            
            // Handle user approval if required
            if (response.requiresUserApproval()) {
                return handleApprovalRequired(response);
            }
            
            // Log execution
            logExecution("Request processed successfully: " + userPrompt);
            performanceMonitor.endTask();
            
            return response;
            
        } catch (Exception e) {
            performanceMonitor.endTask();
            logExecution("Request failed: " + e.getMessage());
            return AgentResponse.error("Failed to process request: " + e.getMessage());
        }
    }
    
    /**
     * Handle approval required scenarios
     * FR.U.1: Always require explicit user approval for modifications
     */
    private AgentResponse handleApprovalRequired(AgentResponse response) {
        try {
            // Create approval request
            ApprovalRequest request = new ApprovalRequest(
                response.getMessage(),
                response.getResult(),
                response.getMetadata()
            );

            // Show diff if available
            if (response.getMetadata().containsKey("diff")) {
                String diff = (String) response.getMetadata().get("diff");
                String formattedDiff = diffViewer.formatDiff(diff);
                request.setDiffView(formattedDiff);
            }

            // Check if approval is actually required
            if (!approvalManager.requiresApproval(request)) {
                // Auto-approved in agent mode - execute immediately
                logExecution("Auto-approved in agent mode: " + request.getDescription());
                return agentCore.getExecutionEngine().handleUserApproval(true, "Auto-approved in agent mode");
            }

            // Register approval request
            String approvalId = approvalManager.registerApprovalRequest(request);

            // Check if it was auto-approved during registration
            ApprovalRequest registeredRequest = approvalManager.getApprovalRequest(approvalId);
            if (registeredRequest == null) {
                // Was auto-approved, execute immediately
                logExecution("Auto-approved during registration: " + request.getDescription());
                return agentCore.getExecutionEngine().handleUserApproval(true, "Auto-approved in agent mode");
            }

            // Return response with approval metadata
            Map<String, Object> approvalMetadata = new HashMap<>(response.getMetadata());
            approvalMetadata.put("approvalId", approvalId);
            approvalMetadata.put("approvalRequired", true);

            return AgentResponse.requiresApproval(
                "User approval required: " + response.getMessage(),
                response.getResult(),
                approvalMetadata
            );

        } catch (Exception e) {
            return AgentResponse.error("Failed to handle approval request: " + e.getMessage());
        }
    }
    
    /**
     * Handle user approval response
     * FR.U.2: Allow users to interrupt or cancel an ongoing AI Agent task
     */
    public AgentResponse handleUserApproval(String approvalId, boolean approved, String userFeedback) {
        try {
            ApprovalRequest request = approvalManager.getApprovalRequest(approvalId);
            if (request == null) {
                return AgentResponse.error("Invalid approval ID");
            }
            
            if (approved) {
                // User approved - execute the changes
                logExecution("User approved changes for: " + request.getDescription());
                
                // Apply changes through execution engine
                AgentResponse result = agentCore.getExecutionEngine().handleUserApproval(true, userFeedback);
                
                // Mark approval as completed
                approvalManager.completeApproval(approvalId, true, userFeedback);
                
                return result;
                
            } else {
                // User rejected - cancel the operation
                logExecution("User rejected changes for: " + request.getDescription());
                
                AgentResponse result = agentCore.getExecutionEngine().handleUserApproval(false, userFeedback);
                
                // Mark approval as rejected
                approvalManager.completeApproval(approvalId, false, userFeedback);
                
                return result;
            }
            
        } catch (Exception e) {
            return AgentResponse.error("Failed to handle user approval: " + e.getMessage());
        }
    }
    
    /**
     * Enable/disable agent mode
     * FR.U.4: Allow users to provide additional context or refine instructions mid-task
     */
    public void setAgentMode(boolean enabled) {
        this.agentModeEnabled = enabled;
        logExecution("Agent mode " + (enabled ? "enabled" : "disabled"));

        if (enabled) {
            // In agent mode, reduce approval requirements for safe operations
            approvalManager.setAgentMode(true);
            securityManager.setAgentMode(true);
        } else {
            // In manual mode, require approval for all modifications
            approvalManager.setAgentMode(false);
            securityManager.setAgentMode(false);
        }
    }

    /**
     * Check if agent mode is enabled
     */
    public boolean isAgentModeEnabled() {
        return agentModeEnabled;
    }
    
    /**
     * Interrupt current task
     * FR.U.2: Allow users to interrupt or cancel an ongoing AI Agent task
     */
    public AgentResponse interruptCurrentTask() {
        if (currentTask != null) {
            agentCore.interruptTask();
            logExecution("Task interrupted by user: " + currentTask.getTaskId());
            return AgentResponse.success("Task interrupted successfully", "");
        } else {
            return AgentResponse.error("No active task to interrupt");
        }
    }
    
    /**
     * Get current task status
     */
    public TaskStatus getCurrentTaskStatus() {
        return agentCore.getTaskStatus();
    }
    
    /**
     * Get execution log
     * FR.U.3: Provide clear logging of the AI Agent's thought process
     */
    public List<String> getExecutionLog() {
        List<String> combinedLog = new ArrayList<>(executionLog);
        combinedLog.addAll(agentCore.getExecutionLog());
        return combinedLog;
    }
    
    /**
     * Get performance metrics
     */
    public PerformanceMetrics getPerformanceMetrics() {
        return performanceMonitor.getMetrics();
    }
    
    /**
     * Get security status
     */
    public SecurityStatus getSecurityStatus() {
        return securityManager.getStatus();
    }
    
    /**
     * Get pending approval requests
     */
    public List<ApprovalRequest> getPendingApprovals() {
        return approvalManager.getPendingApprovals();
    }
    
    /**
     * Create CRUDF operation
     * FR.W.1: Create operations with user approval
     */
    public AgentResponse createFile(String path, String content, String fileType) {
        Map<String, Object> context = new HashMap<>();
        context.put("operation", "CREATE");
        context.put("path", path);
        context.put("fileType", fileType);
        
        String prompt = String.format("Create new %s file at %s", fileType, path);
        IDEContext ideContext = new IDEContext();
        ideContext.setProjectRoot(extractProjectRoot(path));
        
        return processRequest(prompt, ideContext);
    }
    
    /**
     * Read CRUDF operation
     * FR.W.2: Read operations (no approval needed)
     */
    public AgentResponse readFile(String path) {
        String prompt = "Read and analyze file: " + path;
        IDEContext ideContext = new IDEContext();
        ideContext.setCurrentFile(path);
        
        return processRequest(prompt, ideContext);
    }
    
    /**
     * Update CRUDF operation
     * FR.W.3: Update operations with user approval
     */
    public AgentResponse updateFile(String path, String modifications) {
        String prompt = "Update file " + path + " with modifications: " + modifications;
        IDEContext ideContext = new IDEContext();
        ideContext.setCurrentFile(path);
        
        return processRequest(prompt, ideContext);
    }
    
    /**
     * Delete CRUDF operation
     * FR.W.4: Delete operations with mandatory user approval
     */
    public AgentResponse deleteFile(String path) {
        String prompt = "Delete file: " + path;
        IDEContext ideContext = new IDEContext();
        ideContext.setCurrentFile(path);
        
        return processRequest(prompt, ideContext);
    }
    
    /**
     * Git operations
     * FR.W.5: File/Git operations with user approval
     */
    public AgentResponse gitOperation(String operation, Map<String, Object> parameters) {
        String prompt = "Perform git operation: " + operation;
        IDEContext ideContext = new IDEContext();
        
        return processRequest(prompt, ideContext);
    }
    
    // Helper methods
    private void logExecution(String message) {
        String logEntry = java.time.LocalDateTime.now() + ": " + message;
        executionLog.add(logEntry);
        System.out.println("FarTech Production AI: " + logEntry);
    }
    
    private String extractProjectRoot(String path) {
        // Extract project root from file path
        if (path.contains("src/")) {
            return path.substring(0, path.indexOf("src/"));
        }
        return "./";
    }
    
    /**
     * Shutdown agent and cleanup resources
     */
    public void shutdown() {
        performanceMonitor.shutdown();
        approvalManager.cleanup();
        logExecution("Production AI Agent shutdown completed");
    }
}
