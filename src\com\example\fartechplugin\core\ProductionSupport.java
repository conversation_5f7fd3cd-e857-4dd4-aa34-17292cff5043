package com.example.fartechplugin.core;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Supporting classes for Production AI Agent
 */

/**
 * User Approval Manager
 * Handles user approval workflows for modifications
 */
public class UserApprovalManager {
    
    private final Map<String, ApprovalRequest> pendingApprovals;
    private final List<ApprovalRequest> approvalHistory;
    private boolean agentMode;
    
    public UserApprovalManager() {
        this.pendingApprovals = new HashMap<>();
        this.approvalHistory = new ArrayList<>();
        this.agentMode = false;
    }
    
    public String registerApprovalRequest(ApprovalRequest request) {
        String approvalId = UUID.randomUUID().toString();
        request.setApprovalId(approvalId);
        request.setTimestamp(LocalDateTime.now());
        pendingApprovals.put(approvalId, request);
        return approvalId;
    }
    
    public ApprovalRequest getApprovalRequest(String approvalId) {
        return pendingApprovals.get(approvalId);
    }
    
    public void completeApproval(String approvalId, boolean approved, String feedback) {
        ApprovalRequest request = pendingApprovals.remove(approvalId);
        if (request != null) {
            request.setApproved(approved);
            request.setUserFeedback(feedback);
            request.setCompletedAt(LocalDateTime.now());
            approvalHistory.add(request);
        }
    }
    
    public List<ApprovalRequest> getPendingApprovals() {
        return new ArrayList<>(pendingApprovals.values());
    }
    
    public void setAgentMode(boolean agentMode) {
        this.agentMode = agentMode;
    }
    
    public boolean isAgentMode() {
        return agentMode;
    }
    
    public void cleanup() {
        // Keep only recent history
        if (approvalHistory.size() > 100) {
            approvalHistory.subList(0, approvalHistory.size() - 100).clear();
        }
    }
}

/**
 * Approval Request
 */
public class ApprovalRequest {
    private String approvalId;
    private String description;
    private String proposedChanges;
    private Map<String, Object> metadata;
    private String diffView;
    private LocalDateTime timestamp;
    private LocalDateTime completedAt;
    private boolean approved;
    private String userFeedback;
    
    public ApprovalRequest(String description, String proposedChanges, Map<String, Object> metadata) {
        this.description = description;
        this.proposedChanges = proposedChanges;
        this.metadata = metadata != null ? metadata : new HashMap<>();
    }
    
    // Getters and setters
    public String getApprovalId() { return approvalId; }
    public void setApprovalId(String approvalId) { this.approvalId = approvalId; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public String getProposedChanges() { return proposedChanges; }
    public void setProposedChanges(String proposedChanges) { this.proposedChanges = proposedChanges; }
    
    public Map<String, Object> getMetadata() { return metadata; }
    public void setMetadata(Map<String, Object> metadata) { this.metadata = metadata; }
    
    public String getDiffView() { return diffView; }
    public void setDiffView(String diffView) { this.diffView = diffView; }
    
    public LocalDateTime getTimestamp() { return timestamp; }
    public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }
    
    public LocalDateTime getCompletedAt() { return completedAt; }
    public void setCompletedAt(LocalDateTime completedAt) { this.completedAt = completedAt; }
    
    public boolean isApproved() { return approved; }
    public void setApproved(boolean approved) { this.approved = approved; }
    
    public String getUserFeedback() { return userFeedback; }
    public void setUserFeedback(String userFeedback) { this.userFeedback = userFeedback; }
}

/**
 * Security Manager for AI Agent
 */
public class SecurityManager {
    
    private final Set<String> blockedOperations;
    private final Set<String> safePaths;
    private final List<SecurityEvent> securityLog;
    
    public SecurityManager() {
        this.blockedOperations = new HashSet<>();
        this.safePaths = new HashSet<>();
        this.securityLog = new ArrayList<>();
        
        initializeSecurityRules();
    }
    
    public boolean validateRequest(String prompt, IDEContext context) {
        // Check for blocked operations
        String lowerPrompt = prompt.toLowerCase();
        for (String blocked : blockedOperations) {
            if (lowerPrompt.contains(blocked)) {
                logSecurityEvent("Blocked operation detected: " + blocked, SecurityLevel.HIGH);
                return false;
            }
        }
        
        // Validate file paths
        if (context.getCurrentFile() != null) {
            if (!isPathSafe(context.getCurrentFile())) {
                logSecurityEvent("Unsafe path access: " + context.getCurrentFile(), SecurityLevel.MEDIUM);
                return false;
            }
        }
        
        return true;
    }
    
    private void initializeSecurityRules() {
        // Block dangerous operations
        blockedOperations.add("rm -rf");
        blockedOperations.add("format");
        blockedOperations.add("delete system");
        blockedOperations.add("shutdown");
        
        // Define safe paths
        safePaths.add("src/");
        safePaths.add("test/");
        safePaths.add("resources/");
    }
    
    private boolean isPathSafe(String path) {
        // Check if path is within safe directories
        for (String safePath : safePaths) {
            if (path.startsWith(safePath)) {
                return true;
            }
        }
        
        // Block system paths
        if (path.startsWith("/system") || path.startsWith("C:\\Windows") || path.startsWith("/etc")) {
            return false;
        }
        
        return true;
    }
    
    private void logSecurityEvent(String message, SecurityLevel level) {
        SecurityEvent event = new SecurityEvent(message, level, LocalDateTime.now());
        securityLog.add(event);
        System.out.println("SECURITY: " + level + " - " + message);
    }
    
    public SecurityStatus getStatus() {
        SecurityStatus status = new SecurityStatus();
        status.setTotalEvents(securityLog.size());
        status.setHighRiskEvents((int) securityLog.stream().mapToLong(e -> e.getLevel() == SecurityLevel.HIGH ? 1 : 0).sum());
        status.setLastEvent(securityLog.isEmpty() ? null : securityLog.get(securityLog.size() - 1));
        return status;
    }
}

/**
 * Performance Monitor
 */
public class PerformanceMonitor {
    
    private final Map<String, Long> taskStartTimes;
    private final List<PerformanceEntry> performanceHistory;
    private long totalTasks;
    private long totalExecutionTime;
    
    public PerformanceMonitor() {
        this.taskStartTimes = new HashMap<>();
        this.performanceHistory = new ArrayList<>();
        this.totalTasks = 0;
        this.totalExecutionTime = 0;
    }
    
    public void startTask(String taskId) {
        taskStartTimes.put(taskId, System.currentTimeMillis());
    }
    
    public void endTask() {
        // End the most recent task
        if (!taskStartTimes.isEmpty()) {
            String lastTask = taskStartTimes.keySet().iterator().next();
            endTask(lastTask);
        }
    }
    
    public void endTask(String taskId) {
        Long startTime = taskStartTimes.remove(taskId);
        if (startTime != null) {
            long duration = System.currentTimeMillis() - startTime;
            
            PerformanceEntry entry = new PerformanceEntry(taskId, duration, LocalDateTime.now());
            performanceHistory.add(entry);
            
            totalTasks++;
            totalExecutionTime += duration;
        }
    }
    
    public PerformanceMetrics getMetrics() {
        PerformanceMetrics metrics = new PerformanceMetrics();
        metrics.setTotalTasks(totalTasks);
        metrics.setTotalExecutionTime(totalExecutionTime);
        metrics.setAverageExecutionTime(totalTasks > 0 ? totalExecutionTime / totalTasks : 0);
        
        // Calculate recent performance
        if (performanceHistory.size() >= 10) {
            List<PerformanceEntry> recent = performanceHistory.subList(performanceHistory.size() - 10, performanceHistory.size());
            long recentTotal = recent.stream().mapToLong(PerformanceEntry::getDuration).sum();
            metrics.setRecentAverageTime(recentTotal / recent.size());
        }
        
        return metrics;
    }
    
    public void shutdown() {
        taskStartTimes.clear();
    }
}

/**
 * Diff Viewer
 */
public class DiffViewer {
    
    public String formatDiff(String diff) {
        StringBuilder formatted = new StringBuilder();
        formatted.append("=== PROPOSED CHANGES ===\n");
        formatted.append(diff);
        formatted.append("\n=== END CHANGES ===");
        return formatted.toString();
    }
    
    public String createDiff(String original, String modified, String fileName) {
        StringBuilder diff = new StringBuilder();
        diff.append("--- ").append(fileName).append(" (original)\n");
        diff.append("+++ ").append(fileName).append(" (modified)\n");
        diff.append("@@ -1,").append(countLines(original)).append(" +1,").append(countLines(modified)).append(" @@\n");
        
        // Simple line-by-line diff
        String[] originalLines = original.split("\n");
        String[] modifiedLines = modified.split("\n");
        
        for (int i = 0; i < Math.max(originalLines.length, modifiedLines.length); i++) {
            if (i < originalLines.length && i < modifiedLines.length) {
                if (!originalLines[i].equals(modifiedLines[i])) {
                    diff.append("-").append(originalLines[i]).append("\n");
                    diff.append("+").append(modifiedLines[i]).append("\n");
                } else {
                    diff.append(" ").append(originalLines[i]).append("\n");
                }
            } else if (i < originalLines.length) {
                diff.append("-").append(originalLines[i]).append("\n");
            } else {
                diff.append("+").append(modifiedLines[i]).append("\n");
            }
        }
        
        return diff.toString();
    }
    
    private int countLines(String text) {
        return text.split("\n").length;
    }
}

// Supporting data classes
enum SecurityLevel { LOW, MEDIUM, HIGH }

class SecurityEvent {
    private final String message;
    private final SecurityLevel level;
    private final LocalDateTime timestamp;
    
    public SecurityEvent(String message, SecurityLevel level, LocalDateTime timestamp) {
        this.message = message;
        this.level = level;
        this.timestamp = timestamp;
    }
    
    public String getMessage() { return message; }
    public SecurityLevel getLevel() { return level; }
    public LocalDateTime getTimestamp() { return timestamp; }
}

class SecurityStatus {
    private int totalEvents;
    private int highRiskEvents;
    private SecurityEvent lastEvent;
    
    // Getters and setters
    public int getTotalEvents() { return totalEvents; }
    public void setTotalEvents(int totalEvents) { this.totalEvents = totalEvents; }
    
    public int getHighRiskEvents() { return highRiskEvents; }
    public void setHighRiskEvents(int highRiskEvents) { this.highRiskEvents = highRiskEvents; }
    
    public SecurityEvent getLastEvent() { return lastEvent; }
    public void setLastEvent(SecurityEvent lastEvent) { this.lastEvent = lastEvent; }
}

class PerformanceEntry {
    private final String taskId;
    private final long duration;
    private final LocalDateTime timestamp;
    
    public PerformanceEntry(String taskId, long duration, LocalDateTime timestamp) {
        this.taskId = taskId;
        this.duration = duration;
        this.timestamp = timestamp;
    }
    
    public String getTaskId() { return taskId; }
    public long getDuration() { return duration; }
    public LocalDateTime getTimestamp() { return timestamp; }
}

class PerformanceMetrics {
    private long totalTasks;
    private long totalExecutionTime;
    private long averageExecutionTime;
    private long recentAverageTime;
    
    // Getters and setters
    public long getTotalTasks() { return totalTasks; }
    public void setTotalTasks(long totalTasks) { this.totalTasks = totalTasks; }
    
    public long getTotalExecutionTime() { return totalExecutionTime; }
    public void setTotalExecutionTime(long totalExecutionTime) { this.totalExecutionTime = totalExecutionTime; }
    
    public long getAverageExecutionTime() { return averageExecutionTime; }
    public void setAverageExecutionTime(long averageExecutionTime) { this.averageExecutionTime = averageExecutionTime; }
    
    public long getRecentAverageTime() { return recentAverageTime; }
    public void setRecentAverageTime(long recentAverageTime) { this.recentAverageTime = recentAverageTime; }
}
