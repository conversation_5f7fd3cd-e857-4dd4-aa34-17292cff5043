package com.example.fartechplugin.core;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * File reading tool for AI Agent
 */
public class ReadFileTool extends BaseAgentTool {
    
    public ReadFileTool() {
        super("ReadFileTool", "Read content from a file", ToolCapability.FILE_READ, false);
    }
    
    @Override
    public ToolExecutionResult execute(Map<String, Object> parameters) {
        if (!validateParameters(parameters)) {
            return ToolExecutionResult.error("Missing required parameter: path");
        }
        
        String path = getStringParameter(parameters, "path", "");
        
        try {
            // Simulate file reading
            String content = "// File content from: " + path + "\npublic class Example {\n    // Implementation\n}";
            return ToolExecutionResult.success(content);
        } catch (Exception e) {
            return ToolExecutionResult.error("Failed to read file: " + e.getMessage());
        }
    }
    
    @Override
    public List<String> getRequiredParameters() {
        return Arrays.asList("path");
    }
    
    @Override
    public List<String> getOptionalParameters() {
        return Arrays.asList("encoding", "lineRange");
    }
}
