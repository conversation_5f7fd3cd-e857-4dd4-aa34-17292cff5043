package com.example.fartechplugin.core;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Read terminal output tool for AI Agent
 */
public class ReadTerminalOutputTool extends BaseAgentTool {
    
    public ReadTerminalOutputTool() {
        super("ReadTerminalOutputTool", "Read output from terminal", ToolCapability.TERMINAL_EXECUTION, false);
    }
    
    @Override
    public ToolExecutionResult execute(Map<String, Object> parameters) {
        String sessionId = getStringParameter(parameters, "sessionId", "default");
        int lines = (int) getParameter(parameters, "lines", 50);
        
        try {
            String output = "Terminal output (last " + lines + " lines):\n" +
                          "$ mvn clean install\n" +
                          "[INFO] BUILD SUCCESS\n" +
                          "[INFO] Total time: 2.5s\n" +
                          "$ git status\n" +
                          "On branch main\n" +
                          "nothing to commit, working tree clean";
            
            return ToolExecutionResult.success(output);
        } catch (Exception e) {
            return ToolExecutionResult.error("Failed to read terminal output: " + e.getMessage());
        }
    }
    
    @Override
    public List<String> getRequiredParameters() {
        return Arrays.asList();
    }
    
    @Override
    public List<String> getOptionalParameters() {
        return Arrays.asList("sessionId", "lines", "filter");
    }
}
