package com.example.fartechplugin.core;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Replace code tool for AI Agent
 */
public class ReplaceCodeTool extends BaseAgentTool {
    
    public ReplaceCodeTool() {
        super("ReplaceCodeTool", "Replace code in existing files", ToolCapability.CODE_MODIFICATION, true);
    }
    
    @Override
    public ToolExecutionResult execute(Map<String, Object> parameters) {
        if (!validateParameters(parameters)) {
            return ToolExecutionResult.error("Missing required parameters: filePath, oldCode, newCode");
        }
        
        String filePath = getStringParameter(parameters, "filePath", "");
        String oldCode = getStringParameter(parameters, "oldCode", "");
        String newCode = getStringParameter(parameters, "newCode", "");
        
        try {
            String result = "Code replaced successfully in " + filePath;
            return ToolExecutionResult.success(result);
        } catch (Exception e) {
            return ToolExecutionResult.error("Code replacement failed: " + e.getMessage());
        }
    }
    
    @Override
    public List<String> getRequiredParameters() {
        return Arrays.asList("filePath", "oldCode", "newCode");
    }
    
    @Override
    public List<String> getOptionalParameters() {
        return Arrays.asList("backup", "regex");
    }
}
