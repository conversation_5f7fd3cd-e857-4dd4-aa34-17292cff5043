package com.example.fartechplugin.core;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Security Manager for AI Agent
 */
public class SecurityManager {
    
    private final Set<String> blockedOperations;
    private final Set<String> safePaths;
    private final List<SecurityEvent> securityLog;
    private boolean agentMode;

    public SecurityManager() {
        this.blockedOperations = new HashSet<>();
        this.safePaths = new HashSet<>();
        this.securityLog = new ArrayList<>();
        this.agentMode = false;

        initializeSecurityRules();
    }
    
    public boolean validateRequest(String prompt, IDEContext context) {
        // Check for blocked operations
        String lowerPrompt = prompt.toLowerCase();
        for (String blocked : blockedOperations) {
            if (lowerPrompt.contains(blocked)) {
                logSecurityEvent("Blocked operation detected: " + blocked, SecurityLevel.HIGH);
                return false;
            }
        }
        
        // Validate file paths
        if (context.getCurrentFile() != null) {
            if (!isPathSafe(context.getCurrentFile())) {
                logSecurityEvent("Unsafe path access: " + context.getCurrentFile(), SecurityLevel.MEDIUM);
                return false;
            }
        }
        
        return true;
    }
    
    private void initializeSecurityRules() {
        // Block dangerous operations
        blockedOperations.add("rm -rf");
        blockedOperations.add("format");
        blockedOperations.add("delete system");
        blockedOperations.add("shutdown");
        
        // Define safe paths
        safePaths.add("src/");
        safePaths.add("test/");
        safePaths.add("resources/");
    }
    
    private boolean isPathSafe(String path) {
        // Check if path is within safe directories
        for (String safePath : safePaths) {
            if (path.startsWith(safePath)) {
                return true;
            }
        }
        
        // Block system paths
        if (path.startsWith("/system") || path.startsWith("C:\\Windows") || path.startsWith("/etc")) {
            return false;
        }
        
        return true;
    }
    
    private void logSecurityEvent(String message, SecurityLevel level) {
        SecurityEvent event = new SecurityEvent(message, level, LocalDateTime.now());
        securityLog.add(event);
        System.out.println("SECURITY: " + level + " - " + message);
    }
    
    public SecurityStatus getStatus() {
        SecurityStatus status = new SecurityStatus();
        status.setTotalEvents(securityLog.size());
        status.setHighRiskEvents((int) securityLog.stream().mapToLong(e -> e.getLevel() == SecurityLevel.HIGH ? 1 : 0).sum());
        status.setLastEvent(securityLog.isEmpty() ? null : securityLog.get(securityLog.size() - 1));
        return status;
    }

    /**
     * Set agent mode for security validation
     */
    public void setAgentMode(boolean agentMode) {
        this.agentMode = agentMode;
    }

    /**
     * Check if agent mode is enabled
     */
    public boolean isAgentMode() {
        return agentMode;
    }
}

// Supporting data classes
enum SecurityLevel { LOW, MEDIUM, HIGH }

class SecurityEvent {
    private final String message;
    private final SecurityLevel level;
    private final LocalDateTime timestamp;
    
    public SecurityEvent(String message, SecurityLevel level, LocalDateTime timestamp) {
        this.message = message;
        this.level = level;
        this.timestamp = timestamp;
    }
    
    public String getMessage() { return message; }
    public SecurityLevel getLevel() { return level; }
    public LocalDateTime getTimestamp() { return timestamp; }
}

class SecurityStatus {
    private int totalEvents;
    private int highRiskEvents;
    private SecurityEvent lastEvent;
    
    // Getters and setters
    public int getTotalEvents() { return totalEvents; }
    public void setTotalEvents(int totalEvents) { this.totalEvents = totalEvents; }
    
    public int getHighRiskEvents() { return highRiskEvents; }
    public void setHighRiskEvents(int highRiskEvents) { this.highRiskEvents = highRiskEvents; }
    
    public SecurityEvent getLastEvent() { return lastEvent; }
    public void setLastEvent(SecurityEvent lastEvent) { this.lastEvent = lastEvent; }
}
