package com.example.fartechplugin.core;

import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.regex.*;
import java.time.LocalDateTime;

/**
 * Smart Apply Manager - Inspired by Augment's Smart Apply feature
 * Intelligently applies AI suggestions to code files with proper formatting
 */
public class SmartApplyManager {
    
    private static final String BACKUP_DIR = System.getProperty("user.home") + "/.fartech-ai/backups";
    private final Map<String, List<CodeChange>> pendingChanges;
    private final Map<String, String> fileBackups;
    
    public SmartApplyManager() {
        this.pendingChanges = new HashMap<>();
        this.fileBackups = new HashMap<>();
        initializeBackupDirectory();
    }
    
    private void initializeBackupDirectory() {
        try {
            Files.createDirectories(Paths.get(BACKUP_DIR));
        } catch (IOException e) {
            System.err.println("Failed to create backup directory: " + e.getMessage());
        }
    }
    
    /**
     * Parse AI response for code suggestions and prepare for application
     */
    public List<CodeSuggestion> parseCodeSuggestions(String aiResponse) {
        List<CodeSuggestion> suggestions = new ArrayList<>();
        
        // Pattern to match code blocks with file paths
        Pattern codeBlockPattern = Pattern.compile(
            "```(?:java|javascript|python|xml|json|sql|html|css|typescript|php)?\n(.*?)\n```", 
            Pattern.DOTALL
        );
        
        // Pattern to match file path indicators
        Pattern filePathPattern = Pattern.compile(
            "(?:File:|Path:|In file:|Update file:|Modify:)\\s*([^\\n]+)"
        );
        
        Matcher codeBlockMatcher = codeBlockPattern.matcher(aiResponse);
        Matcher filePathMatcher = filePathPattern.matcher(aiResponse);
        
        String currentFilePath = null;
        
        while (codeBlockMatcher.find()) {
            String codeContent = codeBlockMatcher.group(1).trim();
            
            // Try to find file path before this code block
            filePathMatcher.region(0, codeBlockMatcher.start());
            while (filePathMatcher.find()) {
                currentFilePath = filePathMatcher.group(1).trim();
            }
            
            if (codeContent.length() > 10) { // Ignore very short snippets
                CodeSuggestion suggestion = new CodeSuggestion(
                    currentFilePath,
                    codeContent,
                    determineSuggestionType(aiResponse, codeBlockMatcher.start()),
                    extractContext(aiResponse, codeBlockMatcher.start(), codeBlockMatcher.end())
                );
                suggestions.add(suggestion);
            }
        }
        
        return suggestions;
    }
    
    /**
     * Apply a code suggestion to a file
     */
    public ApplyResult applyCodeSuggestion(CodeSuggestion suggestion, boolean createBackup) {
        try {
            if (suggestion.filePath == null || suggestion.filePath.isEmpty()) {
                return new ApplyResult(false, "No file path specified for suggestion");
            }
            
            Path filePath = Paths.get(suggestion.filePath);
            if (!Files.exists(filePath)) {
                return new ApplyResult(false, "File does not exist: " + suggestion.filePath);
            }
            
            // Create backup if requested
            if (createBackup) {
                createBackup(filePath);
            }
            
            String originalContent = new String(Files.readAllBytes(filePath));
            String modifiedContent;
            
            switch (suggestion.type) {
                case REPLACE_METHOD:
                    modifiedContent = replaceMethod(originalContent, suggestion.code);
                    break;
                case INSERT_CODE:
                    modifiedContent = insertCode(originalContent, suggestion.code, suggestion.context);
                    break;
                case REPLACE_CLASS:
                    modifiedContent = replaceClass(originalContent, suggestion.code);
                    break;
                case ADD_IMPORT:
                    modifiedContent = addImport(originalContent, suggestion.code);
                    break;
                case FULL_REPLACEMENT:
                    modifiedContent = suggestion.code;
                    break;
                default:
                    modifiedContent = smartMerge(originalContent, suggestion.code);
                    break;
            }
            
            // Write the modified content
            Files.write(filePath, modifiedContent.getBytes());
            
            // Track the change
            trackChange(suggestion.filePath, originalContent, modifiedContent, suggestion);
            
            return new ApplyResult(true, "Successfully applied suggestion to " + suggestion.filePath);
            
        } catch (Exception e) {
            return new ApplyResult(false, "Failed to apply suggestion: " + e.getMessage());
        }
    }
    
    /**
     * Smart merge of original content with AI suggestion
     */
    private String smartMerge(String originalContent, String suggestionCode) {
        // Try to find the best insertion point
        String[] originalLines = originalContent.split("\n");
        String[] suggestionLines = suggestionCode.split("\n");
        
        // Look for similar patterns to determine insertion point
        int bestInsertionPoint = findBestInsertionPoint(originalLines, suggestionLines);
        
        if (bestInsertionPoint >= 0) {
            StringBuilder result = new StringBuilder();
            
            // Add lines before insertion point
            for (int i = 0; i < bestInsertionPoint; i++) {
                result.append(originalLines[i]).append("\n");
            }
            
            // Add suggestion
            result.append(suggestionCode);
            if (!suggestionCode.endsWith("\n")) {
                result.append("\n");
            }
            
            // Add remaining original lines
            for (int i = bestInsertionPoint; i < originalLines.length; i++) {
                result.append(originalLines[i]).append("\n");
            }
            
            return result.toString();
        }
        
        // Fallback: append to end
        return originalContent + "\n\n" + suggestionCode;
    }
    
    /**
     * Replace a method in the original content
     */
    private String replaceMethod(String originalContent, String newMethodCode) {
        // Extract method name from new code
        String methodName = extractMethodName(newMethodCode);
        if (methodName == null) {
            return smartMerge(originalContent, newMethodCode);
        }
        
        // Find and replace the existing method
        Pattern methodPattern = Pattern.compile(
            "(public|private|protected|static|\\s)*\\s+\\w+\\s+" + Pattern.quote(methodName) + "\\s*\\([^)]*\\)\\s*\\{[^}]*\\}",
            Pattern.DOTALL
        );
        
        Matcher matcher = methodPattern.matcher(originalContent);
        if (matcher.find()) {
            return originalContent.substring(0, matcher.start()) + 
                   newMethodCode + 
                   originalContent.substring(matcher.end());
        }
        
        return smartMerge(originalContent, newMethodCode);
    }
    
    /**
     * Add import statement to the file
     */
    private String addImport(String originalContent, String importStatement) {
        String[] lines = originalContent.split("\n");
        StringBuilder result = new StringBuilder();
        
        boolean importAdded = false;
        boolean inImportSection = false;
        
        for (String line : lines) {
            if (line.trim().startsWith("import ")) {
                inImportSection = true;
            } else if (inImportSection && !line.trim().startsWith("import ") && !line.trim().isEmpty()) {
                // End of import section, add our import here
                if (!importAdded) {
                    result.append(importStatement).append("\n");
                    importAdded = true;
                }
                inImportSection = false;
            }
            
            result.append(line).append("\n");
        }
        
        return result.toString();
    }

    /**
     * Insert code at the appropriate location
     */
    private String insertCode(String originalContent, String newCode, String context) {
        String[] originalLines = originalContent.split("\n");
        String[] newCodeLines = newCode.split("\n");

        // Find the best insertion point based on context
        int insertionPoint = findBestInsertionPoint(originalLines, newCodeLines);

        StringBuilder result = new StringBuilder();

        // Add lines before insertion point
        for (int i = 0; i < insertionPoint && i < originalLines.length; i++) {
            result.append(originalLines[i]).append("\n");
        }

        // Add new code
        result.append(newCode);
        if (!newCode.endsWith("\n")) {
            result.append("\n");
        }

        // Add remaining lines
        for (int i = insertionPoint; i < originalLines.length; i++) {
            result.append(originalLines[i]).append("\n");
        }

        return result.toString();
    }

    /**
     * Replace entire class with new implementation
     */
    private String replaceClass(String originalContent, String newClassCode) {
        // Extract class name from new code
        Pattern classPattern = Pattern.compile("class\\s+(\\w+)");
        Matcher matcher = classPattern.matcher(newClassCode);

        if (matcher.find()) {
            String className = matcher.group(1);

            // Find and replace the existing class
            Pattern existingClassPattern = Pattern.compile(
                "class\\s+" + className + "\\s*\\{.*?\\n\\}",
                Pattern.DOTALL
            );
            Matcher existingMatcher = existingClassPattern.matcher(originalContent);

            if (existingMatcher.find()) {
                return originalContent.substring(0, existingMatcher.start()) +
                       newClassCode +
                       originalContent.substring(existingMatcher.end());
            }
        }

        // If class not found, use smart merge
        return smartMerge(originalContent, newClassCode);
    }

    /**
     * Create a backup of the file before modification
     */
    private void createBackup(Path filePath) throws IOException {
        String timestamp = LocalDateTime.now().toString().replaceAll("[:.\\s]", "_");
        String backupFileName = filePath.getFileName().toString() + "_" + timestamp + ".backup";
        Path backupPath = Paths.get(BACKUP_DIR, backupFileName);
        
        Files.copy(filePath, backupPath);
        fileBackups.put(filePath.toString(), backupPath.toString());
    }
    
    /**
     * Track changes for potential rollback
     */
    private void trackChange(String filePath, String originalContent, String modifiedContent, CodeSuggestion suggestion) {
        List<CodeChange> changes = pendingChanges.getOrDefault(filePath, new ArrayList<>());
        changes.add(new CodeChange(originalContent, modifiedContent, suggestion, LocalDateTime.now()));
        pendingChanges.put(filePath, changes);
    }
    
    /**
     * Rollback the last change to a file
     */
    public ApplyResult rollbackLastChange(String filePath) {
        try {
            List<CodeChange> changes = pendingChanges.get(filePath);
            if (changes == null || changes.isEmpty()) {
                return new ApplyResult(false, "No changes to rollback for " + filePath);
            }
            
            CodeChange lastChange = changes.get(changes.size() - 1);
            Files.write(Paths.get(filePath), lastChange.originalContent.getBytes());
            
            changes.remove(changes.size() - 1);
            if (changes.isEmpty()) {
                pendingChanges.remove(filePath);
            }
            
            return new ApplyResult(true, "Successfully rolled back last change to " + filePath);
            
        } catch (Exception e) {
            return new ApplyResult(false, "Failed to rollback: " + e.getMessage());
        }
    }
    
    // Utility methods
    private SuggestionType determineSuggestionType(String aiResponse, int codeBlockStart) {
        String contextBefore = aiResponse.substring(Math.max(0, codeBlockStart - 200), codeBlockStart).toLowerCase();
        
        if (contextBefore.contains("replace") && contextBefore.contains("method")) {
            return SuggestionType.REPLACE_METHOD;
        } else if (contextBefore.contains("add") && contextBefore.contains("import")) {
            return SuggestionType.ADD_IMPORT;
        } else if (contextBefore.contains("replace") && contextBefore.contains("class")) {
            return SuggestionType.REPLACE_CLASS;
        } else if (contextBefore.contains("insert") || contextBefore.contains("add")) {
            return SuggestionType.INSERT_CODE;
        } else if (contextBefore.contains("complete") || contextBefore.contains("entire")) {
            return SuggestionType.FULL_REPLACEMENT;
        }
        
        return SuggestionType.SMART_MERGE;
    }
    
    private String extractContext(String aiResponse, int start, int end) {
        int contextStart = Math.max(0, start - 100);
        int contextEnd = Math.min(aiResponse.length(), end + 100);
        return aiResponse.substring(contextStart, contextEnd);
    }
    
    private int findBestInsertionPoint(String[] originalLines, String[] suggestionLines) {
        // Simple heuristic: look for class declarations, method signatures, etc.
        for (int i = 0; i < originalLines.length; i++) {
            String line = originalLines[i].trim();
            if (line.contains("class ") || line.contains("public ") || line.contains("private ")) {
                return i + 1; // Insert after class/method declaration
            }
        }
        return originalLines.length; // Append to end
    }
    
    private String extractMethodName(String methodCode) {
        Pattern pattern = Pattern.compile("\\w+\\s+(\\w+)\\s*\\(");
        Matcher matcher = pattern.matcher(methodCode);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
    
    /**
     * Get pending changes for a file
     */
    public List<CodeChange> getPendingChanges(String filePath) {
        return pendingChanges.getOrDefault(filePath, new ArrayList<>());
    }
    
    /**
     * Clear all pending changes
     */
    public void clearPendingChanges() {
        pendingChanges.clear();
    }
    
    /**
     * Get backup file path
     */
    public String getBackupPath(String filePath) {
        return fileBackups.get(filePath);
    }
}

// Classes moved to separate files for better organization
