package com.example.fartechplugin.core;

/**
 * Test class to demonstrate Spring Boot project creation capabilities
 * Shows how the AI can now automatically create Spring Boot projects
 */
public class SpringBootCreationTest {
    
    public static void main(String[] args) {
        testSpringBootDetection();
        testExpectedBehavior();
    }
    
    /**
     * Test Spring Boot project creation detection
     */
    public static void testSpringBootDetection() {
        System.out.println("=== Spring Boot Detection Test ===");
        
        String[] testMessages = {
            "This is API Integration test. can you make basic project java with spring framework for me ?",
            "create a spring boot project",
            "make a java project with spring",
            "generate a spring boot application",
            "setup a basic spring framework project",
            "build a spring boot app",
            "create a simple spring project",
            "make a basic java project", // Should create regular Java project
            "just chat with me" // Should not create anything
        };
        
        WorkspaceIndexer mockIndexer = new WorkspaceIndexer();
        FileOperationsAgent mockAgent = new FileOperationsAgent();
        AutoCRUDProcessor processor = new AutoCRUDProcessor(mockIndexer, mockAgent);
        
        for (String message : testMessages) {
            try {
                // Use reflection to test the private method
                java.lang.reflect.Method method = AutoCRUDProcessor.class.getDeclaredMethod("detectsFileCreationRequest", String.class);
                method.setAccessible(true);
                boolean detected = (Boolean) method.invoke(processor, message);
                
                System.out.println("Message: \"" + message + "\"");
                System.out.println("Creation Request Detected: " + (detected ? "YES" : "NO"));
                
                // Check what type of project would be created
                if (detected) {
                    String lowerMessage = message.toLowerCase();
                    if ((lowerMessage.contains("spring") || lowerMessage.contains("spring boot") || lowerMessage.contains("spring framework")) &&
                        (lowerMessage.contains("project") || lowerMessage.contains("app") || lowerMessage.contains("application"))) {
                        System.out.println("Project Type: SPRING BOOT");
                    } else if (lowerMessage.contains("java") && 
                               (lowerMessage.contains("project") || lowerMessage.contains("structure"))) {
                        System.out.println("Project Type: REGULAR JAVA");
                    } else {
                        System.out.println("Project Type: OTHER");
                    }
                }
                System.out.println();
                
            } catch (Exception e) {
                System.err.println("Error testing message: " + message + " - " + e.getMessage());
            }
        }
    }
    
    /**
     * Test expected behavior for Spring Boot project creation
     */
    public static void testExpectedBehavior() {
        System.out.println("=== Expected Spring Boot Project Creation ===");
        
        System.out.println("When user asks: \"This is API Integration test. can you make basic project java with spring framework for me ?\"");
        System.out.println();
        System.out.println("Expected AI Response:");
        System.out.println("[AGENT] **Agent Mode: Automatically creating files**");
        System.out.println();
        System.out.println("[PROJECT] **Creating Spring Boot Project Structure**");
        System.out.println();
        System.out.println("[DIR] Created directory: `my-spring-app`");
        System.out.println("[DIR] Created directory: `my-spring-app/src`");
        System.out.println("[DIR] Created directory: `my-spring-app/src/main`");
        System.out.println("[DIR] Created directory: `my-spring-app/src/main/java`");
        System.out.println("[DIR] Created directory: `my-spring-app/src/main/java/com`");
        System.out.println("[DIR] Created directory: `my-spring-app/src/main/java/com/fartech`");
        System.out.println("[DIR] Created directory: `my-spring-app/src/main/java/com/fartech/myapp`");
        System.out.println("[DIR] Created directory: `my-spring-app/src/main/java/com/fartech/myapp/controller`");
        System.out.println("[DIR] Created directory: `my-spring-app/src/main/resources`");
        System.out.println("[DIR] Created directory: `my-spring-app/src/test`");
        System.out.println("[DIR] Created directory: `my-spring-app/src/test/java`");
        System.out.println();
        System.out.println("[FILE] Created: `my-spring-app/src/main/java/com/fartech/myapp/MySpringAppApplication.java`");
        System.out.println("[FILE] Created: `my-spring-app/src/main/java/com/fartech/myapp/controller/HelloController.java`");
        System.out.println("[FILE] Created: `my-spring-app/pom.xml`");
        System.out.println("[FILE] Created: `my-spring-app/src/main/resources/application.properties`");
        System.out.println("[FILE] Created: `my-spring-app/README.md`");
        System.out.println();
        System.out.println("[SUCCESS] Spring Boot project structure created successfully!");
        System.out.println("[INFO] Project location: `[WORKSPACE]/my-spring-app/`");
        System.out.println("[INFO] Main class: `com.fartech.myapp.MySpringAppApplication`");
        System.out.println("[INFO] API endpoint: `http://localhost:8080/api/hello`");
        System.out.println("[INFO] To run: `cd [WORKSPACE]/my-spring-app && mvn spring-boot:run`");
        System.out.println();
        
        System.out.println("=== Files Created ===");
        System.out.println();
        System.out.println("1. MySpringAppApplication.java:");
        System.out.println("   - Package: com.fartech.myapp");
        System.out.println("   - @SpringBootApplication annotation");
        System.out.println("   - Main method with SpringApplication.run()");
        System.out.println("   - Proper Spring Boot documentation");
        System.out.println();
        System.out.println("2. HelloController.java:");
        System.out.println("   - Package: com.fartech.myapp.controller");
        System.out.println("   - @RestController and @RequestMapping annotations");
        System.out.println("   - GET /api/hello endpoint");
        System.out.println("   - GET /api/status endpoint");
        System.out.println("   - Request parameter support");
        System.out.println();
        System.out.println("3. pom.xml:");
        System.out.println("   - Spring Boot 3.2.5 configuration");
        System.out.println("   - Java 17 target");
        System.out.println("   - Spring Boot Starter Web dependency");
        System.out.println("   - Spring Boot Starter Test dependency");
        System.out.println("   - Spring Boot Maven Plugin");
        System.out.println();
        System.out.println("4. application.properties:");
        System.out.println("   - Server port configuration (8080)");
        System.out.println("   - Application name");
        System.out.println("   - Logging configuration");
        System.out.println();
        System.out.println("5. README.md:");
        System.out.println("   - Complete project documentation");
        System.out.println("   - API endpoint descriptions");
        System.out.println("   - Build and run instructions");
        System.out.println("   - Testing examples with curl commands");
        System.out.println();
        
        System.out.println("=== Key Benefits ===");
        System.out.println();
        System.out.println("✓ Complete Spring Boot project structure");
        System.out.println("✓ Ready-to-run REST API");
        System.out.println("✓ Professional Spring Boot conventions");
        System.out.println("✓ Maven build configuration");
        System.out.println("✓ Sample API endpoints");
        System.out.println("✓ Comprehensive documentation");
        System.out.println("✓ Files created in correct workspace directory");
        System.out.println("✓ No more chat-only responses!");
    }
}
