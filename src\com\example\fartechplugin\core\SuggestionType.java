package com.example.fartechplugin.core;

/**
 * Types of code suggestions for Smart Apply
 */
public enum SuggestionType {
    REPLACE_METHOD("Replace existing method"),
    INSERT_CODE("Insert new code"),
    REPLACE_CLASS("Replace entire class"),
    ADD_IMPORT("Add import statement"),
    FULL_REPLACEMENT("Replace entire file"),
    SMART_MERGE("Smart merge with existing code");
    
    private final String description;
    
    SuggestionType(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
    
    @Override
    public String toString() {
        return description;
    }
}
