package com.example.fartechplugin.core;

import java.util.*;

/**
 * Tool Registry for AI Agent
 * Manages available tools and their capabilities
 */
public class ToolRegistry {
    
    private final Map<String, AgentTool> tools;
    private final Map<String, ToolCapability> capabilities;
    
    public ToolRegistry() {
        this.tools = new HashMap<>();
        this.capabilities = new HashMap<>();
    }
    
    /**
     * Register a new tool
     */
    public void registerTool(AgentTool tool) {
        tools.put(tool.getName(), tool);
        capabilities.put(tool.getName(), tool.getCapability());
    }
    
    /**
     * Get tool by name
     */
    public AgentTool getTool(String name) {
        return tools.get(name);
    }
    
    /**
     * Get all available tools
     */
    public Map<String, AgentTool> getAllTools() {
        return new HashMap<>(tools);
    }
    
    /**
     * Get tools by capability
     */
    public List<AgentTool> getToolsByCapability(ToolCapability capability) {
        List<AgentTool> result = new ArrayList<>();
        for (AgentTool tool : tools.values()) {
            if (tool.getCapability() == capability) {
                result.add(tool);
            }
        }
        return result;
    }
    
    /**
     * Check if tool is available
     */
    public boolean hasToolForCapability(ToolCapability capability) {
        return capabilities.containsValue(capability);
    }
    
    /**
     * Get tool count
     */
    public int getToolCount() {
        return tools.size();
    }
    
    /**
     * Get tool names
     */
    public Set<String> getToolNames() {
        return new HashSet<>(tools.keySet());
    }
}

// Tool interfaces and base classes moved to separate files

// All tool classes moved to separate files for Java compliance
