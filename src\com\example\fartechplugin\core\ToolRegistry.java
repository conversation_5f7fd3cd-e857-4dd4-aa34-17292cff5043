package com.example.fartechplugin.core;

import java.util.*;

/**
 * Tool Registry for AI Agent
 * Manages available tools and their capabilities
 */
public class ToolRegistry {
    
    private final Map<String, AgentTool> tools;
    private final Map<String, ToolCapability> capabilities;
    
    public ToolRegistry() {
        this.tools = new HashMap<>();
        this.capabilities = new HashMap<>();
    }
    
    /**
     * Register a new tool
     */
    public void registerTool(AgentTool tool) {
        tools.put(tool.getName(), tool);
        capabilities.put(tool.getName(), tool.getCapability());
    }
    
    /**
     * Get tool by name
     */
    public AgentTool getTool(String name) {
        return tools.get(name);
    }
    
    /**
     * Get all available tools
     */
    public Map<String, AgentTool> getAllTools() {
        return new HashMap<>(tools);
    }
    
    /**
     * Get tools by capability
     */
    public List<AgentTool> getToolsByCapability(ToolCapability capability) {
        List<AgentTool> result = new ArrayList<>();
        for (AgentTool tool : tools.values()) {
            if (tool.getCapability() == capability) {
                result.add(tool);
            }
        }
        return result;
    }
    
    /**
     * Check if tool is available
     */
    public boolean hasToolForCapability(ToolCapability capability) {
        return capabilities.containsValue(capability);
    }
    
    /**
     * Get tool count
     */
    public int getToolCount() {
        return tools.size();
    }
    
    /**
     * Get tool names
     */
    public Set<String> getToolNames() {
        return new HashSet<>(tools.keySet());
    }
}

/**
 * Tool capabilities enum
 */
public enum ToolCapability {
    FILE_READ,
    FILE_WRITE,
    FILE_DELETE,
    DIRECTORY_CREATE,
    CODE_ANALYSIS,
    CODE_GENERATION,
    CODE_MODIFICATION,
    TERMINAL_EXECUTION,
    GIT_OPERATIONS,
    DEBUGGING,
    LSP_INTEGRATION,
    GENERAL_ASSISTANCE
}

/**
 * Base interface for all agent tools
 */
public interface AgentTool {
    String getName();
    String getDescription();
    ToolCapability getCapability();
    ToolExecutionResult execute(Map<String, Object> parameters);
    boolean requiresUserApproval();
    List<String> getRequiredParameters();
    List<String> getOptionalParameters();
}

/**
 * Abstract base class for agent tools
 */
public abstract class BaseAgentTool implements AgentTool {
    
    protected final String name;
    protected final String description;
    protected final ToolCapability capability;
    protected final boolean requiresApproval;
    
    public BaseAgentTool(String name, String description, ToolCapability capability, boolean requiresApproval) {
        this.name = name;
        this.description = description;
        this.capability = capability;
        this.requiresApproval = requiresApproval;
    }
    
    @Override
    public String getName() {
        return name;
    }
    
    @Override
    public String getDescription() {
        return description;
    }
    
    @Override
    public ToolCapability getCapability() {
        return capability;
    }
    
    @Override
    public boolean requiresUserApproval() {
        return requiresApproval;
    }
    
    /**
     * Validate required parameters
     */
    protected boolean validateParameters(Map<String, Object> parameters) {
        List<String> required = getRequiredParameters();
        for (String param : required) {
            if (!parameters.containsKey(param) || parameters.get(param) == null) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * Get parameter value with default
     */
    protected Object getParameter(Map<String, Object> parameters, String key, Object defaultValue) {
        return parameters.getOrDefault(key, defaultValue);
    }
    
    /**
     * Get string parameter
     */
    protected String getStringParameter(Map<String, Object> parameters, String key, String defaultValue) {
        Object value = parameters.get(key);
        return value != null ? value.toString() : defaultValue;
    }
    
    /**
     * Get boolean parameter
     */
    protected boolean getBooleanParameter(Map<String, Object> parameters, String key, boolean defaultValue) {
        Object value = parameters.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return defaultValue;
    }
}

/**
 * File system tools
 */
public class ReadFileTool extends BaseAgentTool {
    
    public ReadFileTool() {
        super("ReadFileTool", "Read content from a file", ToolCapability.FILE_READ, false);
    }
    
    @Override
    public ToolExecutionResult execute(Map<String, Object> parameters) {
        if (!validateParameters(parameters)) {
            return ToolExecutionResult.error("Missing required parameter: path");
        }
        
        String path = getStringParameter(parameters, "path", "");
        
        try {
            // Simulate file reading
            String content = "// File content from: " + path + "\npublic class Example {\n    // Implementation\n}";
            return ToolExecutionResult.success(content);
        } catch (Exception e) {
            return ToolExecutionResult.error("Failed to read file: " + e.getMessage());
        }
    }
    
    @Override
    public List<String> getRequiredParameters() {
        return Arrays.asList("path");
    }
    
    @Override
    public List<String> getOptionalParameters() {
        return Arrays.asList("encoding", "lineRange");
    }
}

public class WriteFileTool extends BaseAgentTool {
    
    public WriteFileTool() {
        super("WriteFileTool", "Write content to a file", ToolCapability.FILE_WRITE, true);
    }
    
    @Override
    public ToolExecutionResult execute(Map<String, Object> parameters) {
        if (!validateParameters(parameters)) {
            return ToolExecutionResult.error("Missing required parameters: path, content");
        }
        
        String path = getStringParameter(parameters, "path", "");
        String content = getStringParameter(parameters, "content", "");
        boolean showDiff = getBooleanParameter(parameters, "showDiff", false);
        
        try {
            // Simulate file writing
            Map<String, Object> metadata = new HashMap<>();
            if (showDiff) {
                metadata.put("diff", generateDiff(path, content));
            }
            
            return ToolExecutionResult.success("File written successfully: " + path, metadata);
        } catch (Exception e) {
            return ToolExecutionResult.error("Failed to write file: " + e.getMessage());
        }
    }
    
    private String generateDiff(String path, String content) {
        return "--- " + path + " (original)\n+++ " + path + " (modified)\n@@ -1,3 +1,5 @@\n public class Example {\n+    // New content added\n     // Implementation\n }";
    }
    
    @Override
    public List<String> getRequiredParameters() {
        return Arrays.asList("path", "content");
    }
    
    @Override
    public List<String> getOptionalParameters() {
        return Arrays.asList("encoding", "backup", "showDiff");
    }
}

public class DeleteFileTool extends BaseAgentTool {
    
    public DeleteFileTool() {
        super("DeleteFileTool", "Delete a file or directory", ToolCapability.FILE_DELETE, true);
    }
    
    @Override
    public ToolExecutionResult execute(Map<String, Object> parameters) {
        if (!validateParameters(parameters)) {
            return ToolExecutionResult.error("Missing required parameter: path");
        }
        
        String path = getStringParameter(parameters, "path", "");
        boolean recursive = getBooleanParameter(parameters, "recursive", false);
        
        try {
            // Simulate file deletion
            return ToolExecutionResult.success("File deleted successfully: " + path);
        } catch (Exception e) {
            return ToolExecutionResult.error("Failed to delete file: " + e.getMessage());
        }
    }
    
    @Override
    public List<String> getRequiredParameters() {
        return Arrays.asList("path");
    }
    
    @Override
    public List<String> getOptionalParameters() {
        return Arrays.asList("recursive", "backup");
    }
}

public class CreateDirectoryTool extends BaseAgentTool {
    
    public CreateDirectoryTool() {
        super("CreateDirectoryTool", "Create a new directory", ToolCapability.DIRECTORY_CREATE, false);
    }
    
    @Override
    public ToolExecutionResult execute(Map<String, Object> parameters) {
        if (!validateParameters(parameters)) {
            return ToolExecutionResult.error("Missing required parameter: path");
        }
        
        String path = getStringParameter(parameters, "path", "");
        boolean createParents = getBooleanParameter(parameters, "createParents", true);
        
        try {
            // Simulate directory creation
            return ToolExecutionResult.success("Directory created successfully: " + path);
        } catch (Exception e) {
            return ToolExecutionResult.error("Failed to create directory: " + e.getMessage());
        }
    }
    
    @Override
    public List<String> getRequiredParameters() {
        return Arrays.asList("path");
    }
    
    @Override
    public List<String> getOptionalParameters() {
        return Arrays.asList("createParents", "permissions");
    }
}

// Additional tool classes will be in separate files
