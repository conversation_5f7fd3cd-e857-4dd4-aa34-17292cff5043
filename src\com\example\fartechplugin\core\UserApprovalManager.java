package com.example.fartechplugin.core;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Supporting classes for Production AI Agent
 */

/**
 * User Approval Manager
 * Handles user approval workflows for modifications
 */
public class UserApprovalManager {
    
    private final Map<String, ApprovalRequest> pendingApprovals;
    private final List<ApprovalRequest> approvalHistory;
    private boolean agentMode;
    
    public UserApprovalManager() {
        this.pendingApprovals = new HashMap<>();
        this.approvalHistory = new ArrayList<>();
        this.agentMode = false;
    }
    
    public String registerApprovalRequest(ApprovalRequest request) {
        String approvalId = UUID.randomUUID().toString();
        request.setApprovalId(approvalId);
        request.setTimestamp(LocalDateTime.now());

        // In agent mode, auto-approve safe operations
        if (agentMode && isSafeOperation(request)) {
            request.setApproved(true);
            request.setUserFeedback("Auto-approved in agent mode");
            request.setCompletedAt(LocalDateTime.now());
            approvalHistory.add(request);
            return approvalId; // Return immediately without requiring user approval
        }

        pendingApprovals.put(approvalId, request);
        return approvalId;
    }

    /**
     * Check if an operation is safe to auto-approve in agent mode
     */
    private boolean isSafeOperation(ApprovalRequest request) {
        String description = request.getDescription().toLowerCase();

        // Safe operations that can be auto-approved in agent mode
        if (description.contains("delete") || description.contains("remove")) {
            // Only auto-approve deletion of temporary/test files
            String changes = request.getProposedChanges().toLowerCase();
            return changes.contains("temp") || changes.contains("test") ||
                   changes.contains(".tmp") || changes.contains("backup");
        }

        // Auto-approve read operations
        if (description.contains("read") || description.contains("analyze") || description.contains("view")) {
            return true;
        }

        // Auto-approve create operations for common file types
        if (description.contains("create") || description.contains("generate")) {
            return true;
        }

        // Auto-approve update operations for non-critical files
        if (description.contains("update") || description.contains("modify")) {
            String changes = request.getProposedChanges().toLowerCase();
            return !changes.contains("pom.xml") && !changes.contains("build.gradle") &&
                   !changes.contains("package.json") && !changes.contains("main");
        }

        return false; // Default to requiring approval
    }

    /**
     * Check if approval is required for an operation
     */
    public boolean requiresApproval(ApprovalRequest request) {
        if (!agentMode) {
            return true; // Always require approval in manual mode
        }

        return !isSafeOperation(request); // In agent mode, only require approval for unsafe operations
    }
    
    public ApprovalRequest getApprovalRequest(String approvalId) {
        return pendingApprovals.get(approvalId);
    }
    
    public void completeApproval(String approvalId, boolean approved, String feedback) {
        ApprovalRequest request = pendingApprovals.remove(approvalId);
        if (request != null) {
            request.setApproved(approved);
            request.setUserFeedback(feedback);
            request.setCompletedAt(LocalDateTime.now());
            approvalHistory.add(request);
        }
    }
    
    public List<ApprovalRequest> getPendingApprovals() {
        return new ArrayList<>(pendingApprovals.values());
    }
    
    public void setAgentMode(boolean agentMode) {
        this.agentMode = agentMode;
    }
    
    public boolean isAgentMode() {
        return agentMode;
    }
    
    public void cleanup() {
        // Keep only recent history
        if (approvalHistory.size() > 100) {
            approvalHistory.subList(0, approvalHistory.size() - 100).clear();
        }
    }
}

// ApprovalRequest class moved to separate file

// SecurityManager class moved to separate file

// PerformanceMonitor class moved to separate file

// All classes moved to separate files for Java compliance
