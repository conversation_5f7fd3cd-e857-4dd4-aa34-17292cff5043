package com.example.fartechplugin.core;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Supporting classes for Production AI Agent
 */

/**
 * User Approval Manager
 * Handles user approval workflows for modifications
 */
public class UserApprovalManager {
    
    private final Map<String, ApprovalRequest> pendingApprovals;
    private final List<ApprovalRequest> approvalHistory;
    private boolean agentMode;
    
    public UserApprovalManager() {
        this.pendingApprovals = new HashMap<>();
        this.approvalHistory = new ArrayList<>();
        this.agentMode = false;
    }
    
    public String registerApprovalRequest(ApprovalRequest request) {
        String approvalId = UUID.randomUUID().toString();
        request.setApprovalId(approvalId);
        request.setTimestamp(LocalDateTime.now());
        pendingApprovals.put(approvalId, request);
        return approvalId;
    }
    
    public ApprovalRequest getApprovalRequest(String approvalId) {
        return pendingApprovals.get(approvalId);
    }
    
    public void completeApproval(String approvalId, boolean approved, String feedback) {
        ApprovalRequest request = pendingApprovals.remove(approvalId);
        if (request != null) {
            request.setApproved(approved);
            request.setUserFeedback(feedback);
            request.setCompletedAt(LocalDateTime.now());
            approvalHistory.add(request);
        }
    }
    
    public List<ApprovalRequest> getPendingApprovals() {
        return new ArrayList<>(pendingApprovals.values());
    }
    
    public void setAgentMode(boolean agentMode) {
        this.agentMode = agentMode;
    }
    
    public boolean isAgentMode() {
        return agentMode;
    }
    
    public void cleanup() {
        // Keep only recent history
        if (approvalHistory.size() > 100) {
            approvalHistory.subList(0, approvalHistory.size() - 100).clear();
        }
    }
}

// ApprovalRequest class moved to separate file

// SecurityManager class moved to separate file

// PerformanceMonitor class moved to separate file

// All classes moved to separate files for Java compliance
