package com.example.fartechplugin.core;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Tracks user behavior patterns
 */
public class UserBehaviorPattern {
    private final String userId;
    private final String actionType;
    private final List<BehaviorAction> actions;
    private double successRate;
    private LocalDateTime lastUpdate;
    
    public UserBehaviorPattern(String userId, String actionType) {
        this.userId = userId;
        this.actionType = actionType;
        this.actions = new ArrayList<>();
        this.successRate = 0.5; // Start neutral
        this.lastUpdate = LocalDateTime.now();
    }
    
    public void addAction(String context, boolean successful) {
        actions.add(new BehaviorAction(context, successful, LocalDateTime.now()));
        updateSuccessRate();
        lastUpdate = LocalDateTime.now();
        
        // Keep only recent actions (last 20)
        if (actions.size() > 20) {
            actions.remove(0);
        }
    }
    
    private void updateSuccessRate() {
        if (actions.isEmpty()) {
            successRate = 0.5;
            return;
        }
        
        int successCount = 0;
        for (BehaviorAction action : actions) {
            if (action.successful) {
                successCount++;
            }
        }
        successRate = (double) successCount / actions.size();
    }
    
    public String getUserId() { return userId; }
    public String getActionType() { return actionType; }
    public int getActionCount() { return actions.size(); }
    public double getSuccessRate() { return successRate; }
    public LocalDateTime getLastUpdate() { return lastUpdate; }
}
