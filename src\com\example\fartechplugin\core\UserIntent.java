package com.example.fartechplugin.core;

import java.time.LocalDateTime;
import java.util.*;

/**
 * Represents a user's intent parsed from their prompt
 */
public class UserIntent {
    private final IntentType type;
    private final String originalPrompt;
    private final String description;
    private final Map<String, Object> parameters;
    private final LocalDateTime timestamp;
    
    public UserIntent(IntentType type, String originalPrompt, Map<String, Object> parameters) {
        this.type = type;
        this.originalPrompt = originalPrompt;
        this.description = generateDescription();
        this.parameters = parameters != null ? parameters : new HashMap<>();
        this.timestamp = LocalDateTime.now();
    }
    
    private String generateDescription() {
        switch (type) {
            case CREATE: return "Create new content";
            case READ: return "Read and analyze content";
            case UPDATE: return "Update existing content";
            case DELETE: return "Delete content";
            case GIT_OPERATION: return "Perform Git operation";
            case EXECUTE: return "Execute command or test";
            default: return "General assistance";
        }
    }
    
    // Getters
    public IntentType getType() { return type; }
    public String getOriginalPrompt() { return originalPrompt; }
    public String getDescription() { return description; }
    public Map<String, Object> getParameters() { return new HashMap<>(parameters); }
    public LocalDateTime getTimestamp() { return timestamp; }
}
