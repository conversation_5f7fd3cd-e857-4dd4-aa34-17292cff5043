package com.example.fartechplugin.core;

import java.nio.file.Paths;
import com.example.fartechplugin.utils.EclipseCompatibility;

/**
 * Test class to demonstrate workspace directory resolution
 * Shows how files are now created in the correct workspace location
 */
public class WorkspaceDirectoryTest {
    
    public static void main(String[] args) {
        testWorkspaceDirectoryResolution();
        testFileCreationPaths();
    }
    
    /**
     * Test workspace directory resolution logic
     */
    public static void testWorkspaceDirectoryResolution() {
        System.out.println("=== Workspace Directory Resolution Test ===");
        
        // Test EclipseCompatibility method
        String eclipseWorkspace = EclipseCompatibility.getWorkspaceLocation();
        System.out.println("Eclipse Workspace Location: " + eclipseWorkspace);
        
        // Test current directory fallback
        String currentDir = System.getProperty("user.dir");
        System.out.println("Current Directory (fallback): " + currentDir);
        
        // Test WorkspaceIndexer integration
        WorkspaceIndexer indexer = new WorkspaceIndexer();
        String indexerWorkspace = indexer.getWorkspaceRoot();
        System.out.println("WorkspaceIndexer Root: " + indexerWorkspace);
        
        System.out.println();
        System.out.println("=== Directory Comparison ===");
        System.out.println("Eclipse vs Current: " + (eclipseWorkspace.equals(currentDir) ? "SAME" : "DIFFERENT"));
        System.out.println("Eclipse vs Indexer: " + (eclipseWorkspace.equals(indexerWorkspace) ? "SAME" : "DIFFERENT"));
        System.out.println();
    }
    
    /**
     * Test file creation paths
     */
    public static void testFileCreationPaths() {
        System.out.println("=== File Creation Path Test ===");
        
        // Simulate AutoCRUDProcessor workspace directory resolution
        WorkspaceIndexer mockIndexer = new WorkspaceIndexer();
        String workspaceDir = getWorkspaceDirectory(mockIndexer);
        
        System.out.println("Resolved Workspace Directory: " + workspaceDir);
        System.out.println();
        
        // Test project structure paths
        String[] projectFiles = {
            "my-java-project/src/main/java/com/example/myproject/Application.java",
            "my-java-project/pom.xml",
            "my-java-project/README.md"
        };
        
        System.out.println("=== Project Files Will Be Created At ===");
        for (String file : projectFiles) {
            String fullPath = Paths.get(workspaceDir, file).toString();
            System.out.println("File: " + file);
            System.out.println("Full Path: " + fullPath);
            System.out.println();
        }
        
        System.out.println("=== Before vs After Comparison ===");
        System.out.println();
        System.out.println("BEFORE (Your Issue):");
        System.out.println("Files created in: " + System.getProperty("user.dir"));
        System.out.println("Example: " + Paths.get(System.getProperty("user.dir"), "my-java-project/Application.java"));
        System.out.println("Problem: Files created in Eclipse installation directory!");
        System.out.println();
        
        System.out.println("AFTER (Fixed):");
        System.out.println("Files created in: " + workspaceDir);
        System.out.println("Example: " + Paths.get(workspaceDir, "my-java-project/Application.java"));
        System.out.println("Solution: Files created in user's workspace directory!");
        System.out.println();
        
        System.out.println("=== Expected AI Response ===");
        System.out.println();
        System.out.println("[AGENT] **Agent Mode: Automatically creating files**");
        System.out.println();
        System.out.println("[PROJECT] **Creating Java Project Structure**");
        System.out.println();
        System.out.println("[DIR] Created directory: `my-java-project`");
        System.out.println("[DIR] Created directory: `my-java-project/src`");
        System.out.println("[DIR] Created directory: `my-java-project/src/main`");
        System.out.println("[DIR] Created directory: `my-java-project/src/main/java`");
        System.out.println("[DIR] Created directory: `my-java-project/src/main/java/com`");
        System.out.println("[DIR] Created directory: `my-java-project/src/main/java/com/example`");
        System.out.println("[DIR] Created directory: `my-java-project/src/main/java/com/example/myproject`");
        System.out.println();
        System.out.println("[FILE] Created: `my-java-project/src/main/java/com/example/myproject/Application.java`");
        System.out.println("[FILE] Created: `my-java-project/pom.xml`");
        System.out.println("[FILE] Created: `my-java-project/README.md`");
        System.out.println();
        System.out.println("[SUCCESS] Java project structure created successfully!");
        System.out.println("[INFO] Project location: `" + workspaceDir + "/my-java-project/`");
        System.out.println("[INFO] Main class: `com.example.myproject.Application`");
        System.out.println("[INFO] To run: `cd " + workspaceDir + "/my-java-project && mvn compile exec:java`");
        System.out.println();
        
        System.out.println("=== Key Benefits ===");
        System.out.println();
        System.out.println("✓ Files created in correct workspace directory");
        System.out.println("✓ No more files in Eclipse installation directory");
        System.out.println("✓ Proper integration with Eclipse workspace");
        System.out.println("✓ Files visible in Eclipse Project Explorer");
        System.out.println("✓ Automatic workspace indexing after creation");
        System.out.println("✓ Correct paths shown in AI responses");
    }
    
    /**
     * Simulate AutoCRUDProcessor workspace directory resolution
     */
    private static String getWorkspaceDirectory(WorkspaceIndexer workspaceIndexer) {
        // Try to get workspace directory from WorkspaceIndexer first
        if (workspaceIndexer != null) {
            String workspaceRoot = workspaceIndexer.getWorkspaceRoot();
            if (workspaceRoot != null && !workspaceRoot.isEmpty()) {
                return workspaceRoot;
            }
        }
        
        // Fallback to EclipseCompatibility
        String workspaceLocation = EclipseCompatibility.getWorkspaceLocation();
        if (workspaceLocation != null && !workspaceLocation.isEmpty()) {
            return workspaceLocation;
        }
        
        // Final fallback to current directory
        return System.getProperty("user.dir");
    }
}
