package com.example.fartechplugin.core;

import java.nio.file.*;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;
import com.example.fartechplugin.utils.EclipseCompatibility;

/**
 * Enhanced workspace indexer with progress tracking and better UX
 * Provides comprehensive file indexing with user-friendly feedback
 */
public class WorkspaceIndexer {

    // Core indexing data
    private final Map<String, FileInfo> fileIndex;
    private final Map<String, DirectoryInfo> directoryIndex;
    private long lastIndexTime;
    private String workspaceRoot;

    // Configuration and preferences
    private final IndexingConfiguration config;
    private Set<String> indexedExtensions; // Legacy support - will be replaced by config

    // Progress tracking
    private final List<IndexingProgressListener> progressListeners;
    private final AtomicBoolean isIndexing;
    private final AtomicBoolean isCancelled;
    private final AtomicInteger filesProcessed;
    private final AtomicInteger totalFilesFound;
    private long indexingStartTime;

    // Statistics
    private int skippedFiles;
    private int errorFiles;
    private Map<String, Integer> skippedReasons;
    
    public WorkspaceIndexer() {
        this(new IndexingConfiguration());
    }

    public WorkspaceIndexer(IndexingConfiguration config) {
        // Initialize core data structures
        this.fileIndex = new ConcurrentHashMap<>();
        this.directoryIndex = new ConcurrentHashMap<>();
        this.lastIndexTime = 0;

        // Configuration
        this.config = config;

        // Progress tracking
        this.progressListeners = new ArrayList<>();
        this.isIndexing = new AtomicBoolean(false);
        this.isCancelled = new AtomicBoolean(false);
        this.filesProcessed = new AtomicInteger(0);
        this.totalFilesFound = new AtomicInteger(0);

        // Statistics
        this.skippedFiles = 0;
        this.errorFiles = 0;
        this.skippedReasons = new HashMap<>();

        // Use proper Eclipse workspace location
        this.workspaceRoot = EclipseCompatibility.getWorkspaceLocation();
        System.out.println("🏠 WorkspaceIndexer initialized with workspace root: " + this.workspaceRoot);

        // Legacy extension support (will be replaced by config)
        this.indexedExtensions = new HashSet<>(config.getIncludedExtensions());
        System.out.println("📋 Supported file extensions: " + indexedExtensions.size() + " types");
        System.out.println("⚙️ Configuration: Max file size: " + config.getMaxFileSizeFormatted() +
                          ", Max depth: " + config.getMaxDepth() +
                          ", Auto-index: " + config.isAutoIndexOnStartup());
    }
    
    /**
     * Index the workspace with enhanced progress tracking
     */
    public void indexWorkspace() throws Exception {
        indexWorkspace(true);
    }

    /**
     * Index the workspace with optional progress notifications
     */
    public void indexWorkspace(boolean notifyProgress) throws Exception {
        if (isIndexing.get()) {
            throw new Exception("Indexing is already in progress");
        }

        try {
            // Initialize indexing state
            isIndexing.set(true);
            isCancelled.set(false);
            filesProcessed.set(0);
            totalFilesFound.set(0);
            skippedFiles = 0;
            errorFiles = 0;
            skippedReasons.clear();
            indexingStartTime = System.currentTimeMillis();

            // Clear previous index
            fileIndex.clear();
            directoryIndex.clear();

            System.out.println("🔍 Starting enhanced workspace indexing at: " + workspaceRoot);
            System.out.println("📂 Workspace path exists: " + Files.exists(Paths.get(workspaceRoot)));

            Path workspacePath = Paths.get(workspaceRoot);
            if (!Files.exists(workspacePath)) {
                String error = "Workspace path does not exist: " + workspaceRoot;
                if (notifyProgress) {
                    notifyProgressListeners(listener -> listener.onIndexingFailed(error, null));
                }
                throw new Exception(error);
            }

            // Estimate total files for progress tracking
            int estimatedFiles = estimateFileCount(workspacePath);
            if (notifyProgress) {
                notifyProgressListeners(listener -> listener.onIndexingStarted(estimatedFiles));
            }

            // List workspace contents for debugging
            System.out.println("📋 Contents of workspace root:");
            try (DirectoryStream<Path> stream = Files.newDirectoryStream(workspacePath)) {
                for (Path entry : stream) {
                    String name = entry.getFileName().toString();
                    String type = Files.isDirectory(entry) ? "DIR" : "FILE";
                    System.out.println("  " + type + ": " + name);
                }
            } catch (Exception e) {
                System.out.println("  Error listing contents: " + e.getMessage());
            }

            // Perform indexing
            indexDirectory(workspacePath, 0, notifyProgress);

            if (isCancelled.get()) {
                System.out.println("❌ Indexing was cancelled by user");
                return;
            }

            lastIndexTime = System.currentTimeMillis();
            long duration = lastIndexTime - indexingStartTime;

            System.out.println("✅ Enhanced workspace indexing completed!");
            System.out.println("📊 Results: " + fileIndex.size() + " files, " + directoryIndex.size() + " directories");
            System.out.println("⏱️ Duration: " + (duration / 1000.0) + " seconds");
            System.out.println("📈 Statistics: " + skippedFiles + " skipped, " + errorFiles + " errors");

            if (notifyProgress) {
                notifyProgressListeners(listener -> listener.onIndexingCompleted(
                    fileIndex.size(), directoryIndex.size(), duration));
            }

            // Show configuration info
            System.out.println("🔧 Configuration: " + config.getMaxFileSizeFormatted() + " max size, " +
                             config.getMaxDepth() + " max depth");

            // Show sample indexed files
            if (fileIndex.size() > 0) {
                System.out.println("📄 Sample indexed files:");
                fileIndex.entrySet().stream().limit(5).forEach(entry ->
                    System.out.println("  " + entry.getKey() + " (" + entry.getValue().extension + ")"));
            }

        } catch (Exception e) {
            if (notifyProgress) {
                notifyProgressListeners(listener -> listener.onIndexingFailed(e.getMessage(), e));
            }
            throw e;
        } finally {
            isIndexing.set(false);
        }
    }
    
    /**
     * Estimate total file count for progress tracking
     */
    private int estimateFileCount(Path rootPath) {
        try {
            return (int) Files.walk(rootPath)
                .filter(Files::isRegularFile)
                .filter(path -> !shouldSkipPath(path))
                .limit(10000) // Reasonable limit for estimation
                .count();
        } catch (Exception e) {
            System.out.println("⚠️ Could not estimate file count: " + e.getMessage());
            return 100; // Default estimate
        }
    }

    /**
     * Check if a path should be skipped based on configuration
     */
    private boolean shouldSkipPath(Path path) {
        String fileName = path.getFileName().toString();

        // Check directory exclusions
        if (Files.isDirectory(path) && config.shouldExcludeDirectory(fileName)) {
            System.out.println("      ⏭️ Skipping directory: " + fileName + " (excluded by configuration)");
            return true;
        }

        // Check file patterns
        if (config.matchesExcludedPattern(path.toString())) {
            System.out.println("      ⏭️ Skipping file: " + fileName + " (matches excluded pattern)");
            return true;
        }

        // Check hidden files (but allow Eclipse-specific hidden files like .project, .classpath)
        if (fileName.startsWith(".") && !isEclipseFile(fileName)) {
            System.out.println("      ⏭️ Skipping hidden file: " + fileName);
            return true;
        }

        return false;
    }

    /**
     * Check if a file is an important Eclipse project file
     */
    private boolean isEclipseFile(String fileName) {
        return fileName.equals(".project") ||
               fileName.equals(".classpath") ||
               fileName.equals(".factorypath") ||
               fileName.equals(".gitignore") ||
               fileName.equals(".settings");
    }

    /**
     * Index a directory recursively with progress tracking
     */
    private void indexDirectory(Path dirPath, int depth, boolean notifyProgress) throws Exception {
        if (depth > config.getMaxDepth()) {
            return;
        }

        if (isCancelled.get()) {
            return;
        }

        String relativePath = getRelativePath(dirPath);
        DirectoryInfo dirInfo = new DirectoryInfo();
        dirInfo.path = relativePath;
        dirInfo.absolutePath = dirPath.toString();
        dirInfo.fileCount = 0;
        dirInfo.subdirectoryCount = 0;

        System.out.println("📁 Indexing directory: " + relativePath + " (depth: " + depth + ")");

        if (notifyProgress) {
            notifyProgressListeners(listener -> listener.onDirectoryScanning(relativePath, depth));
        }

        try (DirectoryStream<Path> stream = Files.newDirectoryStream(dirPath)) {
            for (Path entry : stream) {
                if (isCancelled.get()) {
                    return;
                }

                String fileName = entry.getFileName().toString();

                // Use enhanced filtering from configuration
                if (shouldSkipPath(entry)) {
                    String reason = "configuration filter";
                    System.out.println("  ⏭️ Skipping: " + fileName + " (" + reason + ")");
                    skippedReasons.merge(reason, 1, Integer::sum);
                    if (notifyProgress) {
                        notifyProgressListeners(listener -> listener.onFileSkipped(entry.toString(), reason));
                    }
                    continue;
                }

                if (Files.isDirectory(entry)) {
                    dirInfo.subdirectoryCount++;
                    System.out.println("  📂 Found subdirectory: " + fileName);
                    indexDirectory(entry, depth + 1, notifyProgress);
                } else if (Files.isRegularFile(entry)) {
                    dirInfo.fileCount++;
                    System.out.println("  📄 Found file: " + fileName);
                    indexFile(entry, notifyProgress);
                }
            }
        } catch (Exception e) {
            System.out.println("  ❌ Error indexing directory " + relativePath + ": " + e.getMessage());
            errorFiles++;
        }

        directoryIndex.put(relativePath, dirInfo);
        System.out.println("  ✅ Directory indexed: " + relativePath + " (" + dirInfo.fileCount + " files, " + dirInfo.subdirectoryCount + " subdirs)");
    }
    
    /**
     * Index a single file with progress tracking
     * Package-private to allow access from WorkspaceWatcher
     */
    void indexFile(Path filePath, boolean notifyProgress) throws Exception {
        String fileName = filePath.getFileName().toString();
        String extension = getFileExtension(fileName);
        String relativePath = getRelativePath(filePath);

        System.out.println("    📄 Indexing file: " + fileName + " (extension: '" + extension + "')");

        // Update progress
        int currentProcessed = filesProcessed.incrementAndGet();
        if (notifyProgress) {
            int total = Math.max(totalFilesFound.get(), currentProcessed);
            int percentage = total > 0 ? (currentProcessed * 100) / total : 0;
            notifyProgressListeners(listener -> listener.onIndexingProgress(
                relativePath, currentProcessed, total, percentage));
        }

        // Check if extension is supported using configuration
        if (!config.shouldIndexExtension(extension)) {
            String reason = "unsupported extension: " + extension;
            System.out.println("    ⏭️ Skipping file: " + fileName + " (" + reason + ")");
            skippedFiles++;
            skippedReasons.merge("unsupported extension", 1, Integer::sum);
            if (notifyProgress) {
                notifyProgressListeners(listener -> listener.onFileSkipped(relativePath, reason));
            }
            return;
        }

        long fileSize = Files.size(filePath);
        if (fileSize > config.getMaxFileSize()) {
            String reason = "file too large: " + fileSize + " bytes (limit: " + config.getMaxFileSizeFormatted() + ")";
            System.out.println("    ⏭️ Skipping file: " + fileName + " (" + reason + ")");
            skippedFiles++;
            skippedReasons.merge("file too large", 1, Integer::sum);
            if (notifyProgress) {
                notifyProgressListeners(listener -> listener.onFileSkipped(relativePath, reason));
            }
            return;
        }

        try {
            FileInfo fileInfo = new FileInfo();
            fileInfo.path = relativePath;
            fileInfo.absolutePath = filePath.toString();
            fileInfo.name = fileName;
            fileInfo.extension = extension;
            fileInfo.size = fileSize;
            fileInfo.lastModified = Files.getLastModifiedTime(filePath).toMillis();

            // Read file content for text files
            if (isTextFile(extension)) {
                try {
                    fileInfo.content = new String(Files.readAllBytes(filePath));
                    fileInfo.lineCount = fileInfo.content.split("\n").length;
                    System.out.println("    ✅ File content read: " + fileName + " (" + fileInfo.lineCount + " lines)");
                } catch (Exception e) {
                    fileInfo.content = "[Error reading file: " + e.getMessage() + "]";
                    fileInfo.lineCount = 0;
                    System.out.println("    ⚠️ Error reading file content: " + fileName + " - " + e.getMessage());
                    errorFiles++;
                }
            } else {
                System.out.println("    📄 File indexed without content: " + fileName + " (binary file)");
            }

            fileIndex.put(relativePath, fileInfo);
            System.out.println("    ✅ File indexed successfully: " + relativePath);

        } catch (Exception e) {
            System.out.println("    ❌ Error indexing file: " + fileName + " - " + e.getMessage());
            errorFiles++;
            throw e;
        }
    }

    /**
     * Legacy method for backward compatibility
     */
    private void indexFile(Path filePath) throws Exception {
        indexFile(filePath, false);
    }
    
    /**
     * Get file context for AI
     */
    public String getFileContext(String filePath) {
        FileInfo fileInfo = fileIndex.get(filePath);
        if (fileInfo == null) {
            return "File not found in index: " + filePath;
        }
        
        StringBuilder context = new StringBuilder();
        context.append("=== FILE CONTEXT ===\n");
        context.append("File: ").append(fileInfo.path).append("\n");
        context.append("Size: ").append(fileInfo.size).append(" bytes\n");
        context.append("Lines: ").append(fileInfo.lineCount).append("\n");
        context.append("Last Modified: ").append(new Date(fileInfo.lastModified)).append("\n");
        
        if (fileInfo.content != null && !fileInfo.content.isEmpty()) {
            context.append("\nContent:\n");
            context.append(fileInfo.content);
        }
        
        return context.toString();
    }
    
    /**
     * Get related files context
     */
    public String getRelatedFilesContext(String filePath) {
        StringBuilder context = new StringBuilder();
        context.append("=== RELATED FILES ===\n");
        
        // Find files in the same directory
        String directory = getDirectoryFromPath(filePath);
        List<FileInfo> relatedFiles = new ArrayList<>();
        
        for (FileInfo file : fileIndex.values()) {
            String fileDir = getDirectoryFromPath(file.path);
            if (fileDir.equals(directory) && !file.path.equals(filePath)) {
                relatedFiles.add(file);
            }
        }
        
        if (relatedFiles.isEmpty()) {
            context.append("No related files found in the same directory.\n");
        } else {
            context.append("Files in the same directory:\n");
            for (FileInfo file : relatedFiles) {
                context.append("- ").append(file.name)
                       .append(" (").append(file.size).append(" bytes)\n");
            }
        }
        
        return context.toString();
    }
    
    /**
     * Get workspace context summary
     */
    public String getWorkspaceContext() {
        StringBuilder context = new StringBuilder();
        context.append("=== WORKSPACE SUMMARY ===\n");
        context.append("Workspace Root: ").append(workspaceRoot).append("\n");
        context.append("Total Files: ").append(fileIndex.size()).append("\n");
        context.append("Total Directories: ").append(directoryIndex.size()).append("\n");

        // File type breakdown
        Map<String, Integer> extensionCounts = new HashMap<>();
        for (FileInfo file : fileIndex.values()) {
            extensionCounts.merge(file.extension, 1, Integer::sum);
        }

        context.append("\nFile Types:\n");
        extensionCounts.entrySet().stream()
            .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
            .limit(10)
            .forEach(entry -> context.append("- ").append(entry.getKey())
                                   .append(": ").append(entry.getValue()).append(" files\n"));

        // Recent files
        List<FileInfo> recentFiles = fileIndex.values().stream()
            .sorted((a, b) -> Long.compare(b.lastModified, a.lastModified))
            .limit(5)
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);

        if (!recentFiles.isEmpty()) {
            context.append("\nRecently Modified Files:\n");
            for (FileInfo file : recentFiles) {
                context.append("- ").append(file.path)
                       .append(" (").append(new Date(file.lastModified)).append(")\n");
            }
        }

        return context.toString();
    }

    /**
     * Get comprehensive workspace context for AI prompts
     */
    public String getComprehensiveWorkspaceContext() {
        StringBuilder context = new StringBuilder();
        context.append("=== COMPREHENSIVE WORKSPACE CONTEXT ===\n");
        context.append("Workspace Root: ").append(workspaceRoot).append("\n");
        context.append("Total Files: ").append(fileIndex.size()).append("\n");
        context.append("Total Directories: ").append(directoryIndex.size()).append("\n");
        context.append("Last Indexed: ").append(lastIndexTime > 0 ? new Date(lastIndexTime) : "Never").append("\n\n");

        // File type breakdown with more details
        Map<String, Integer> extensionCounts = new HashMap<>();
        Map<String, List<String>> extensionFiles = new HashMap<>();

        for (FileInfo file : fileIndex.values()) {
            extensionCounts.merge(file.extension, 1, Integer::sum);
            extensionFiles.computeIfAbsent(file.extension, k -> new ArrayList<>()).add(file.path);
        }

        context.append("=== FILE TYPES AND EXAMPLES ===\n");
        extensionCounts.entrySet().stream()
            .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
            .limit(15)
            .forEach(entry -> {
                String ext = entry.getKey();
                int count = entry.getValue();
                List<String> files = extensionFiles.get(ext);

                context.append("📄 ").append(ext.isEmpty() ? "(no extension)" : ext)
                       .append(": ").append(count).append(" files\n");

                // Show up to 3 example files for each type
                if (files != null && !files.isEmpty()) {
                    files.stream().limit(3).forEach(file ->
                        context.append("   - ").append(file).append("\n"));
                    if (files.size() > 3) {
                        context.append("   ... and ").append(files.size() - 3).append(" more\n");
                    }
                }
                context.append("\n");
            });

        // Directory structure overview
        context.append("=== DIRECTORY STRUCTURE ===\n");
        directoryIndex.entrySet().stream()
            .sorted(Map.Entry.comparingByKey())
            .limit(20)
            .forEach(entry -> {
                DirectoryInfo dir = entry.getValue();
                context.append("📁 ").append(entry.getKey().isEmpty() ? "." : entry.getKey())
                       .append(" (").append(dir.fileCount).append(" files, ")
                       .append(dir.subdirectoryCount).append(" subdirs)\n");
            });

        // Recent files with more context
        List<FileInfo> recentFiles = fileIndex.values().stream()
            .sorted((a, b) -> Long.compare(b.lastModified, a.lastModified))
            .limit(10)
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);

        if (!recentFiles.isEmpty()) {
            context.append("\n=== RECENTLY MODIFIED FILES ===\n");
            for (FileInfo file : recentFiles) {
                context.append("📝 ").append(file.path)
                       .append(" (").append(file.size).append(" bytes, ")
                       .append(file.lineCount).append(" lines, ")
                       .append(new Date(file.lastModified)).append(")\n");
            }
        }

        // Important files detection
        context.append("\n=== IMPORTANT FILES DETECTED ===\n");
        List<String> importantFiles = new ArrayList<>();

        for (FileInfo file : fileIndex.values()) {
            String fileName = file.name.toLowerCase();
            String path = file.path.toLowerCase();

            // Detect common important files
            if (fileName.equals("readme.md") || fileName.equals("readme.txt") ||
                fileName.equals("pom.xml") || fileName.equals("build.gradle") ||
                fileName.equals("package.json") || fileName.equals("requirements.txt") ||
                fileName.equals("dockerfile") || fileName.equals("docker-compose.yml") ||
                fileName.equals("manifest.mf") || fileName.equals("plugin.xml") ||
                path.contains("main.java") || path.contains("application.properties") ||
                path.contains("config") && (fileName.endsWith(".xml") || fileName.endsWith(".properties"))) {
                importantFiles.add(file.path);
            }
        }

        if (!importantFiles.isEmpty()) {
            importantFiles.forEach(file -> context.append("⭐ ").append(file).append("\n"));
        } else {
            context.append("No standard project files detected.\n");
        }

        return context.toString();
    }
    
    /**
     * Get file count
     */
    public int getFileCount() {
        return fileIndex.size();
    }
    
    /**
     * Get directory count
     */
    public int getDirectoryCount() {
        return directoryIndex.size();
    }

    /**
     * Get array of all indexed file paths
     */
    public String[] getIndexedFiles() {
        return fileIndex.keySet().toArray(new String[0]);
    }
    
    /**
     * Get last index time
     */
    public long getLastIndexTime() {
        return lastIndexTime;
    }
    
    /**
     * Set workspace root
     */
    public void setWorkspaceRoot(String workspaceRoot) {
        this.workspaceRoot = workspaceRoot;
        System.out.println("📁 Workspace root updated to: " + workspaceRoot);
    }

    /**
     * Get workspace root
     */
    public String getWorkspaceRoot() {
        return workspaceRoot;
    }

    /**
     * Add progress listener
     */
    public void addProgressListener(IndexingProgressListener listener) {
        if (listener != null && !progressListeners.contains(listener)) {
            progressListeners.add(listener);
        }
    }

    /**
     * Remove progress listener
     */
    public void removeProgressListener(IndexingProgressListener listener) {
        progressListeners.remove(listener);
    }

    /**
     * Check if indexing is currently in progress
     */
    public boolean isIndexing() {
        return isIndexing.get();
    }

    /**
     * Cancel ongoing indexing operation
     */
    public void cancelIndexing() {
        isCancelled.set(true);
        notifyProgressListeners(listener -> listener.onIndexingCancelled());
    }

    /**
     * Get indexing configuration
     */
    public IndexingConfiguration getConfiguration() {
        return config;
    }

    /**
     * Get indexing statistics
     */
    public IndexingStatistics getStatistics() {
        return new IndexingStatistics(
            fileIndex.size(),
            directoryIndex.size(),
            skippedFiles,
            errorFiles,
            lastIndexTime,
            new HashMap<>(skippedReasons)
        );
    }
    
    // Helper methods
    private String getRelativePath(Path path) {
        try {
            Path workspacePath = Paths.get(workspaceRoot);
            return workspacePath.relativize(path).toString().replace("\\", "/");
        } catch (Exception e) {
            return path.toString();
        }
    }

    /**
     * Notify all progress listeners with a specific action
     */
    private void notifyProgressListeners(java.util.function.Consumer<IndexingProgressListener> action) {
        for (IndexingProgressListener listener : progressListeners) {
            try {
                action.accept(listener);
            } catch (Exception e) {
                System.err.println("Error notifying progress listener: " + e.getMessage());
            }
        }
    }
    
    private String getFileExtension(String fileName) {
        int lastDot = fileName.lastIndexOf('.');
        String extension;

        if (lastDot > 0) {
            extension = fileName.substring(lastDot);
        } else {
            // For files without extensions, use the filename itself as the "extension"
            // This allows us to support files like "LICENSE", "README", ".project", etc.
            extension = fileName.startsWith(".") ? fileName : "";
        }

        System.out.println("      🔍 File extension for '" + fileName + "': '" + extension + "'");
        return extension;
    }
    
    private boolean isTextFile(String extension) {
        return indexedExtensions.contains(extension.toLowerCase());
    }
    
    private String getDirectoryFromPath(String filePath) {
        int lastSlash = filePath.lastIndexOf('/');
        return lastSlash > 0 ? filePath.substring(0, lastSlash) : "";
    }
    
    // Data classes
    public static class FileInfo {
        public String path;
        public String absolutePath;
        public String name;
        public String extension;
        public long size;
        public long lastModified;
        public String content;
        public int lineCount;
    }
    
    public static class DirectoryInfo {
        public String path;
        public String absolutePath;
        public int fileCount;
        public int subdirectoryCount;
    }
}
