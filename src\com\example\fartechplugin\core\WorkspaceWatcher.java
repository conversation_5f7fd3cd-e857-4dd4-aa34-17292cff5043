package com.example.fartechplugin.core;

import java.io.IOException;
import java.nio.file.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Watches workspace for file changes and triggers incremental indexing
 */
public class WorkspaceWatcher {
    
    private final WorkspaceIndexer indexer;
    private final IndexingConfiguration config;
    private WatchService watchService;
    private final AtomicBoolean isWatching;
    private ExecutorService watcherThread;
    private Path watchedPath;
    
    public WorkspaceWatcher(WorkspaceIndexer indexer) {
        this.indexer = indexer;
        this.config = indexer.getConfiguration();
        this.isWatching = new AtomicBoolean(false);
    }
    
    /**
     * Start watching the workspace for changes
     */
    public void startWatching() {
        if (isWatching.get()) {
            return;
        }
        
        try {
            watchedPath = Paths.get(indexer.getWorkspaceRoot());
            watchService = FileSystems.getDefault().newWatchService();
            
            // Register the workspace root for watching
            registerDirectoryRecursively(watchedPath);
            
            isWatching.set(true);
            
            // Start the watcher thread
            watcherThread = Executors.newSingleThreadExecutor(r -> {
                Thread t = new Thread(r, "WorkspaceWatcher");
                t.setDaemon(true);
                return t;
            });
            
            watcherThread.submit(this::watchLoop);
            
            System.out.println("📡 Workspace watcher started for: " + watchedPath);
            
        } catch (IOException e) {
            System.err.println("❌ Failed to start workspace watcher: " + e.getMessage());
        }
    }
    
    /**
     * Stop watching the workspace
     */
    public void stopWatching() {
        if (!isWatching.get()) {
            return;
        }
        
        isWatching.set(false);
        
        try {
            if (watchService != null) {
                watchService.close();
            }
        } catch (IOException e) {
            System.err.println("⚠️ Error closing watch service: " + e.getMessage());
        }
        
        if (watcherThread != null) {
            watcherThread.shutdown();
        }
        
        System.out.println("📡 Workspace watcher stopped");
    }
    
    /**
     * Check if watcher is currently active
     */
    public boolean isWatching() {
        return isWatching.get();
    }
    
    /**
     * Register a directory and all its subdirectories for watching
     */
    private void registerDirectoryRecursively(Path dir) throws IOException {
        if (!Files.isDirectory(dir)) {
            return;
        }
        
        // Register this directory
        dir.register(watchService, 
            StandardWatchEventKinds.ENTRY_CREATE,
            StandardWatchEventKinds.ENTRY_DELETE,
            StandardWatchEventKinds.ENTRY_MODIFY);
        
        // Register subdirectories
        try (DirectoryStream<Path> stream = Files.newDirectoryStream(dir)) {
            for (Path entry : stream) {
                if (Files.isDirectory(entry) && !shouldSkipDirectory(entry)) {
                    registerDirectoryRecursively(entry);
                }
            }
        }
    }
    
    /**
     * Check if a directory should be skipped from watching
     */
    private boolean shouldSkipDirectory(Path dir) {
        String dirName = dir.getFileName().toString();
        return config.shouldExcludeDirectory(dirName) || dirName.startsWith(".");
    }
    
    /**
     * Main watch loop
     */
    private void watchLoop() {
        while (isWatching.get()) {
            try {
                WatchKey key = watchService.take();
                
                for (WatchEvent<?> event : key.pollEvents()) {
                    WatchEvent.Kind<?> kind = event.kind();
                    
                    if (kind == StandardWatchEventKinds.OVERFLOW) {
                        System.out.println("⚠️ Watch service overflow - triggering full re-index");
                        triggerFullReindex();
                        continue;
                    }
                    
                    @SuppressWarnings("unchecked")
                    WatchEvent<Path> pathEvent = (WatchEvent<Path>) event;
                    Path changed = pathEvent.context();
                    Path fullPath = ((Path) key.watchable()).resolve(changed);
                    
                    handleFileChange(kind, fullPath);
                }
                
                boolean valid = key.reset();
                if (!valid) {
                    System.out.println("⚠️ Watch key no longer valid - stopping watcher");
                    break;
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                System.err.println("❌ Error in watch loop: " + e.getMessage());
            }
        }
    }
    
    /**
     * Handle a file system change event
     */
    private void handleFileChange(WatchEvent.Kind<?> kind, Path path) {
        try {
            String fileName = path.getFileName().toString();
            
            // Skip temporary files and hidden files
            if (fileName.startsWith(".") || fileName.endsWith("~") || fileName.endsWith(".tmp")) {
                return;
            }
            
            if (kind == StandardWatchEventKinds.ENTRY_CREATE) {
                if (Files.isDirectory(path)) {
                    System.out.println("📁 New directory created: " + path);
                    // Register new directory for watching
                    try {
                        registerDirectoryRecursively(path);
                    } catch (IOException e) {
                        System.err.println("⚠️ Failed to register new directory: " + e.getMessage());
                    }
                    // Trigger incremental indexing for the new directory
                    triggerIncrementalIndex(path);
                } else if (Files.isRegularFile(path)) {
                    System.out.println("📄 New file created: " + path);
                    triggerIncrementalIndex(path);
                }
            } else if (kind == StandardWatchEventKinds.ENTRY_MODIFY) {
                if (Files.isRegularFile(path)) {
                    System.out.println("✏️ File modified: " + path);
                    triggerIncrementalIndex(path);
                }
            } else if (kind == StandardWatchEventKinds.ENTRY_DELETE) {
                System.out.println("🗑️ File/directory deleted: " + path);
                // Remove from index if it was indexed
                removeFromIndex(path);
            }
            
        } catch (Exception e) {
            System.err.println("❌ Error handling file change: " + e.getMessage());
        }
    }
    
    /**
     * Trigger incremental indexing for a specific path
     */
    private void triggerIncrementalIndex(Path path) {
        if (!config.isIncrementalIndexing()) {
            return;
        }
        
        // Run incremental indexing in background
        Thread incrementalThread = new Thread(() -> {
            try {
                System.out.println("🔄 Triggering incremental index for: " + path);
                // For now, just re-index the single file/directory
                // In a full implementation, this would be more sophisticated
                if (Files.isRegularFile(path)) {
                    // Re-index single file
                    indexer.indexFile(path, false);
                } else if (Files.isDirectory(path)) {
                    // Re-index directory
                    // This would need a new method in WorkspaceIndexer
                    System.out.println("📁 Directory indexing not yet implemented for incremental updates");
                }
            } catch (Exception e) {
                System.err.println("❌ Incremental indexing failed: " + e.getMessage());
            }
        });
        incrementalThread.setDaemon(true);
        incrementalThread.start();
    }
    
    /**
     * Remove a path from the index
     */
    private void removeFromIndex(Path path) {
        // This would need to be implemented in WorkspaceIndexer
        System.out.println("🗑️ Removing from index: " + path + " (not yet implemented)");
    }
    
    /**
     * Trigger a full re-index
     */
    private void triggerFullReindex() {
        if (!config.isBackgroundIndexing()) {
            return;
        }
        
        Thread reindexThread = new Thread(() -> {
            try {
                System.out.println("🔄 Triggering full background re-index");
                indexer.indexWorkspace(false); // No progress notifications for background
            } catch (Exception e) {
                System.err.println("❌ Background re-indexing failed: " + e.getMessage());
            }
        });
        reindexThread.setDaemon(true);
        reindexThread.start();
    }
}
