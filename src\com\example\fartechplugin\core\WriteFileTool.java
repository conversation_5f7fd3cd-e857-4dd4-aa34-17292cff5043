package com.example.fartechplugin.core;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * File writing tool for AI Agent
 */
public class WriteFileTool extends BaseAgentTool {
    
    public WriteFileTool() {
        super("WriteFileTool", "Write content to a file", ToolCapability.FILE_WRITE, true);
    }
    
    @Override
    public ToolExecutionResult execute(Map<String, Object> parameters) {
        if (!validateParameters(parameters)) {
            return ToolExecutionResult.error("Missing required parameters: path, content");
        }
        
        String path = getStringParameter(parameters, "path", "");
        String content = getStringParameter(parameters, "content", "");
        boolean showDiff = getBooleanParameter(parameters, "showDiff", false);
        
        try {
            // Simulate file writing
            Map<String, Object> metadata = new HashMap<>();
            if (showDiff) {
                metadata.put("diff", generateDiff(path, content));
            }
            
            return ToolExecutionResult.success("File written successfully: " + path, metadata);
        } catch (Exception e) {
            return ToolExecutionResult.error("Failed to write file: " + e.getMessage());
        }
    }
    
    private String generateDiff(String path, String content) {
        return "--- " + path + " (original)\n+++ " + path + " (modified)\n@@ -1,3 +1,5 @@\n public class Example {\n+    // New content added\n     // Implementation\n }";
    }
    
    @Override
    public List<String> getRequiredParameters() {
        return Arrays.asList("path", "content");
    }
    
    @Override
    public List<String> getOptionalParameters() {
        return Arrays.asList("encoding", "backup", "showDiff");
    }
}
