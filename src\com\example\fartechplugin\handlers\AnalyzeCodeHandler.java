package com.example.fartechplugin.handlers;

import com.example.fartechplugin.FarTechPlugin;
import com.example.fartechplugin.FarTechView;

/**
 * <PERSON><PERSON> for analyzing selected code with AI
 * Simplified version that works without Eclipse command framework
 */
public class AnalyzeCodeHandler extends BaseHandler {

    @Override
    public void execute() {
        try {
            // Get the selected text
            String selectedText = getSelectedText();

            if (selectedText == null || selectedText.trim().isEmpty()) {
                showWarning("No Code Selected",
                    "Please select some code to analyze.");
                return;
            }

            // Get the FarTech view
            FarTechView view = getFarTechView();

            if (view != null) {
                // Send the selected code for analysis
                String analysisPrompt = "Please analyze this code and provide insights about:\n" +
                    "1. Code quality and potential issues\n" +
                    "2. Performance considerations\n" +
                    "3. Best practices and improvements\n" +
                    "4. Security considerations if applicable\n\n" +
                    "Code to analyze:\n```\n" + selectedText + "\n```";

                view.sendMessageToAI(analysisPrompt);

                FarTechPlugin.logInfo("Code analysis requested for " + selectedText.length() + " characters");
            } else {
                showInfo("FarTech AI", "Code analysis would be performed here");
            }

        } catch (Exception e) {
            FarTechPlugin.getErrorHandler().handleError(
                "Failed to analyze code", e);
        }
    }
}
