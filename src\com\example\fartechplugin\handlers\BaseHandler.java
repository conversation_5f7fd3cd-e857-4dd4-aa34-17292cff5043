package com.example.fartechplugin.handlers;

import com.example.fartechplugin.FarTechPlugin;
import com.example.fartechplugin.FarTechView;

/**
 * Base handler class that provides common functionality
 * Compatible with Eclipse command framework
 */
public abstract class BaseHandler {

    /**
     * Execute the handler action
     */
    public abstract void execute();
    
    /**
     * Get the FarTech view instance
     */
    protected FarTechView getFarTechView() {
        // In a real Eclipse environment, this would use workbench APIs
        // For now, return null - the view would need to be passed in
        return null;
    }
    
    /**
     * Get selected text from active editor
     */
    protected String getSelectedText() {
        // In a real Eclipse environment, this would get text from active editor
        // For now, return empty string
        return "";
    }
    
    /**
     * Show warning dialog (simplified)
     */
    protected void showWarning(String title, String message) {
        FarTechPlugin.logWarning(title + ": " + message);
    }

    /**
     * Show error dialog (simplified)
     */
    protected void showError(String title, String message) {
        FarTechPlugin.logError(title + ": " + message, null);
    }

    /**
     * Show info dialog (simplified)
     */
    protected void showInfo(String title, String message) {
        FarTechPlugin.logInfo(title + ": " + message);
    }
    
    /**
     * Check if handler is enabled
     */
    public boolean isEnabled() {
        return FarTechPlugin.isInitialized();
    }
}
