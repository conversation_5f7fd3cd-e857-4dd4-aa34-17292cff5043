package com.example.fartechplugin.handlers;

import com.example.fartechplugin.FarTechPlugin;
import com.example.fartechplugin.FarTechView;

/**
 * <PERSON><PERSON> for explaining selected code with AI
 * Simplified version that works without Eclipse command framework
 */
public class ExplainCodeHandler extends BaseHandler {

    @Override
    public void execute() {
        try {
            // Get the selected text
            String selectedText = getSelectedText();

            if (selectedText == null || selectedText.trim().isEmpty()) {
                showWarning("No Code Selected",
                    "Please select some code to explain.");
                return;
            }

            // Get the FarTech view
            FarTechView view = getFarTechView();

            if (view != null) {
                // Send the selected code for explanation
                String explanationPrompt = "Please explain this code in detail:\n" +
                    "1. What does this code do?\n" +
                    "2. How does it work step by step?\n" +
                    "3. What are the key concepts or patterns used?\n" +
                    "4. Are there any important details a developer should know?\n\n" +
                    "Code to explain:\n```\n" + selectedText + "\n```";

                view.sendMessageToAI(explanationPrompt);

                FarTechPlugin.logInfo("Code explanation requested for " + selectedText.length() + " characters");
            } else {
                showInfo("FarTech AI", "Code explanation would be performed here");
            }

        } catch (Exception e) {
            FarTechPlugin.getErrorHandler().handleError(
                "Failed to explain code", e);
        }
    }
}
