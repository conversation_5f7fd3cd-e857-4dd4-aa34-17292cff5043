package com.example.fartechplugin.handlers;

import com.example.fartechplugin.FarTechPlugin;
import com.example.fartechplugin.FarTechView;

/**
 * <PERSON><PERSON> for generating documentation with AI
 * Simplified version that works without Eclipse command framework
 */
public class GenerateDocsHandler extends BaseHandler {

    @Override
    public void execute() {
        try {
            // Get the selected text
            String selectedText = getSelectedText();

            if (selectedText == null || selectedText.trim().isEmpty()) {
                showWarning("No Code Selected",
                    "Please select a class or method to generate documentation for.");
                return;
            }

            // Get the FarTech view
            FarTechView view = getFarTechView();

            if (view != null) {
                // Send the selected code for documentation generation
                String docsPrompt = "Please generate comprehensive documentation for this code:\n" +
                    "1. Create JavaDoc comments for classes and methods\n" +
                    "2. Document all parameters, return values, and exceptions\n" +
                    "3. Provide clear descriptions of functionality\n" +
                    "4. Include usage examples where appropriate\n" +
                    "5. Document any important implementation details\n" +
                    "6. Add @since, @author, and other relevant tags\n\n" +
                    "Code to document:\n```\n" + selectedText + "\n```\n\n" +
                    "Please provide the code with proper JavaDoc comments added.";

                view.sendMessageToAI(docsPrompt);

                FarTechPlugin.logInfo("Documentation generation requested for " + selectedText.length() + " characters");
            } else {
                showInfo("FarTech AI", "Documentation generation would be performed here");
            }

        } catch (Exception e) {
            FarTechPlugin.getErrorHandler().handleError(
                "Failed to generate documentation", e);
        }
    }
}
