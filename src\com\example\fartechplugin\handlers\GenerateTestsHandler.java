package com.example.fartechplugin.handlers;

import com.example.fartechplugin.FarTechPlugin;
import com.example.fartechplugin.FarTechView;

/**
 * <PERSON><PERSON> for generating unit tests with AI
 * Simplified version that works without Eclipse command framework
 */
public class GenerateTestsHandler extends BaseHandler {

    @Override
    public void execute() {
        try {
            // Get the selected text
            String selectedText = getSelectedText();

            if (selectedText == null || selectedText.trim().isEmpty()) {
                showWarning("No Code Selected",
                    "Please select a class or method to generate tests for.");
                return;
            }

            // Get the FarTech view
            FarTechView view = getFarTechView();

            if (view != null) {
                // Send the selected code for test generation
                String testPrompt = "Please generate comprehensive unit tests for this code:\n" +
                    "1. Create test methods for all public methods\n" +
                    "2. Include edge cases and boundary conditions\n" +
                    "3. Test both positive and negative scenarios\n" +
                    "4. Use appropriate assertions and mocking if needed\n" +
                    "5. Follow best practices for unit testing\n" +
                    "6. Include setup and teardown methods if necessary\n\n" +
                    "Code to test:\n```\n" + selectedText + "\n```\n\n" +
                    "Please provide the complete test class with proper imports and annotations.";

                view.sendMessageToAI(testPrompt);

                FarTechPlugin.logInfo("Unit test generation requested for " + selectedText.length() + " characters");
            } else {
                showInfo("FarTech AI", "Unit test generation would be performed here");
            }

        } catch (Exception e) {
            FarTechPlugin.getErrorHandler().handleError(
                "Failed to generate tests", e);
        }
    }
}
