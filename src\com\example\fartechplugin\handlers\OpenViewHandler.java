package com.example.fartechplugin.handlers;

import com.example.fartechplugin.FarTechPlugin;
import com.example.fartechplugin.FarTechView;
import com.example.fartechplugin.utils.EclipseCompatibility;

/**
 * <PERSON><PERSON> for opening the FarTech AI Assistant view
 * <PERSON>perly opens the view using Eclipse workbench
 */
public class OpenViewHandler extends BaseHandler {

    @Override
    public void execute() {
        try {
            // Try to open the FarTech AI view using Eclipse workbench
            if (EclipseCompatibility.isEclipseAvailable()) {
                boolean success = EclipseCompatibility.openView(FarTechView.ID);
                if (success) {
                    FarTechPlugin.logInfo("FarTech AI Assistant view opened successfully");
                } else {
                    FarTechPlugin.logInfo("Failed to open FarTech AI Assistant view via workbench");
                    showInfo("FarTech AI", "Please open the view manually: Window → Show View → Other → FarTech AI → FarTech AI Assistant");
                }
            } else {
                // Fallback for non-Eclipse environments
                FarTechPlugin.logInfo("FarTech AI Assistant view open requested (non-Eclipse environment)");
                showInfo("FarTech AI", "FarTech AI Assistant view would be opened here");
            }

        } catch (Exception e) {
            FarTechPlugin.getErrorHandler().handleError(
                "Failed to open FarTech AI Assistant view", e);
            // Provide user guidance
            showInfo("FarTech AI", "Unable to open view automatically. Please use: Window → Show View → Other → FarTech AI → FarTech AI Assistant");
        }
    }
}
