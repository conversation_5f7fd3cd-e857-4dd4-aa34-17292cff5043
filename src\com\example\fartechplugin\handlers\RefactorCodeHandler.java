package com.example.fartechplugin.handlers;

import com.example.fartechplugin.FarTechPlugin;
import com.example.fartechplugin.FarTechView;

/**
 * <PERSON><PERSON> for suggesting code refactoring with AI
 * Simplified version that works without Eclipse command framework
 */
public class RefactorCodeHandler extends BaseHandler {

    @Override
    public void execute() {
        try {
            // Get the selected text
            String selectedText = getSelectedText();

            if (selectedText == null || selectedText.trim().isEmpty()) {
                showWarning("No Code Selected",
                    "Please select some code to refactor.");
                return;
            }

            // Get the FarTech view
            FarTechView view = getFarTechView();

            if (view != null) {
                // Send the selected code for refactoring suggestions
                String refactorPrompt = "Please suggest refactoring improvements for this code:\n" +
                    "1. Identify code smells and issues\n" +
                    "2. Suggest specific refactoring techniques\n" +
                    "3. Provide improved code examples\n" +
                    "4. Explain the benefits of each suggestion\n" +
                    "5. Consider maintainability, readability, and performance\n\n" +
                    "Code to refactor:\n```\n" + selectedText + "\n```";

                view.sendMessageToAI(refactorPrompt);

                FarTechPlugin.logInfo("Code refactoring suggestions requested for " + selectedText.length() + " characters");
            } else {
                showInfo("FarTech AI", "Code refactoring suggestions would be provided here");
            }

        } catch (Exception e) {
            FarTechPlugin.getErrorHandler().handleError(
                "Failed to suggest refactoring", e);
        }
    }
}
