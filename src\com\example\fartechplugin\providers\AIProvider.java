package com.example.fartechplugin.providers;

/**
 * Interface for AI providers
 * Moved to providers package for better organization
 */
public interface AIProvider {
    
    /**
     * Send a prompt to the AI provider and get response
     */
    String sendPrompt(String prompt, String apiKey);
    
    /**
     * Get available models from the provider
     */
    String[] getAvailableModels(String apiKey, String baseUrl);
    
    /**
     * Get the provider name
     */
    String getProviderName();
    
    /**
     * Check if the provider supports the given configuration
     */
    boolean supportsConfiguration(String baseUrl);
}
