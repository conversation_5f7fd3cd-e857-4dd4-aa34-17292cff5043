package com.example.fartechplugin.providers;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * OpenAI Compatible provider implementation
 * Supports OpenAI API and compatible endpoints
 * Moved to providers package for better organization
 */
public class OpenAIProvider implements AIProvider {

    private static final String DEFAULT_BASE_URL = "https://ai.ad-ins.com/api";
    private static final String DEFAULT_MODEL = "Devstral Small";
    private static final int MAX_TOKENS = 2000;

    // Timeout settings for FarTech AI provider
    private static final int CONNECT_TIMEOUT = 30000; // 30 seconds
    private static final int READ_TIMEOUT = 120000;   // 2 minutes

    private String baseUrl = DEFAULT_BASE_URL; // FarTech AI endpoint
    private String model = DEFAULT_MODEL;

    // Map to store display name -> API name mapping
    private java.util.Map<String, String> modelNameMapping = new java.util.HashMap<>();
    
    @Override
    public String sendPrompt(String prompt, String apiKey) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            return "Error: API key is not configured. Please set it in Settings.";
        }
        
        if (prompt == null || prompt.trim().isEmpty()) {
            return "Error: Please enter a prompt";
        }
        
        try {
            String endpoint = baseUrl + "/chat/completions";
            URL url = new URL(endpoint);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setDoOutput(true);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Authorization", "Bearer " + apiKey);
            conn.setRequestProperty("Content-Type", "application/json");

            // Set timeout values for FarTech AI provider
            conn.setConnectTimeout(CONNECT_TIMEOUT);
            conn.setReadTimeout(READ_TIMEOUT);

            // Properly escape JSON content
            String escapedPrompt = escapeJsonString(prompt);

            // Get the original API model name (if mapped) or use the current model name
            String apiModelName = modelNameMapping.getOrDefault(model, model);

            // Debug: Log mapping details
            System.out.println("[FarTech AI DEBUG] Current mapping size: " + modelNameMapping.size());
            System.out.println("[FarTech AI DEBUG] Looking for model: '" + model + "'");
            System.out.println("[FarTech AI DEBUG] Found mapping: '" + modelNameMapping.get(model) + "'");
            System.out.println("[FarTech AI DEBUG] Using API model: '" + apiModelName + "'");

            String body = String.format("{" +
                "\"model\": \"%s\"," +
                "\"messages\": [{\"role\": \"user\", \"content\": \"%s\"}]," +
                "\"max_tokens\": %d," +
                "\"temperature\": 0.7" +
                "}", apiModelName, escapedPrompt, MAX_TOKENS);

            // Debug: Log the model being sent and request details
            System.out.println("[FarTech AI DEBUG] Final request body model: '" + apiModelName + "'");
            System.out.println("[FarTech AI DEBUG] Original prompt length: " + prompt.length());
            System.out.println("[FarTech AI DEBUG] Escaped prompt length: " + escapedPrompt.length());
            System.out.println("[FarTech AI DEBUG] Request body length: " + body.length());

            // Log first 200 chars of escaped prompt for debugging
            String debugPrompt = escapedPrompt.length() > 200 ?
                escapedPrompt.substring(0, 200) + "..." : escapedPrompt;
            System.out.println("[FarTech AI DEBUG] Escaped prompt preview: " + debugPrompt);

            try (OutputStream os = conn.getOutputStream()) {
                os.write(body.getBytes("UTF-8"));
            }

            int responseCode = conn.getResponseCode();
            InputStream is;
            if (responseCode >= 200 && responseCode < 300) {
                is = conn.getInputStream();
            } else {
                is = conn.getErrorStream();
            }
            
            BufferedReader br = new BufferedReader(new InputStreamReader(is, "UTF-8"));
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = br.readLine()) != null) {
                response.append(line);
            }
            br.close();
            
            if (responseCode >= 200 && responseCode < 300) {
                return parseResponse(response.toString());
            } else {
                String errorMessage = response.toString();
                System.err.println("[FarTech AI ERROR] HTTP " + responseCode + ": " + errorMessage);

                // Provide user-friendly error messages for common issues
                if (responseCode == 422 && errorMessage.contains("json_invalid")) {
                    return "Error: Invalid request format. This may be caused by special characters in your message. " +
                           "Try rephrasing your request or breaking it into smaller parts.";
                } else if (responseCode == 401) {
                    return "Error: Authentication failed. Please check your API key in Settings.";
                } else if (responseCode == 429) {
                    return "Error: Rate limit exceeded. Please wait a moment and try again.";
                } else if (responseCode == 500) {
                    return "Error: Server error. Please try again later.";
                } else {
                    return "Error: HTTP " + responseCode + " - " + errorMessage;
                }
            }

        } catch (Exception e) {
            return "Error: " + e.getMessage();
        }
    }
    
    private String parseResponse(String json) {
        try {
            // Simple JSON parsing for the content field
            int contentIndex = json.indexOf("\"content\":\"");
            if (contentIndex != -1) {
                int start = contentIndex + 11;
                int end = start;
                
                // Find the end of the content string, handling escaped quotes
                while (end < json.length()) {
                    char c = json.charAt(end);
                    if (c == '"' && (end == start || json.charAt(end - 1) != '\\')) {
                        break;
                    }
                    end++;
                }
                
                if (end < json.length()) {
                    String content = json.substring(start, end);
                    // Unescape common escape sequences
                    content = content.replace("\\n", "\n")
                                   .replace("\\r", "\r")
                                   .replace("\\t", "\t")
                                   .replace("\\\"", "\"")
                                   .replace("\\\\", "\\");
                    return content;
                }
            }
            return "Could not parse response: " + json;
        } catch (Exception e) {
            return "Error parsing response: " + e.getMessage();
        }
    }

    @Override
    public String getProviderName() {
        return "OpenAI Compatible";
    }

    @Override
    public boolean supportsConfiguration(String baseUrl) {
        return baseUrl != null && (baseUrl.contains("openai.com") ||
                                  baseUrl.contains("/v1") ||
                                  baseUrl.contains("ai.ad-ins.com") ||
                                  baseUrl.contains("/api"));
    }

    /**
     * Set the base URL for the API
     */
    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl != null ? baseUrl : DEFAULT_BASE_URL;
    }

    /**
     * Get the current base URL
     */
    public String getBaseUrl() {
        return baseUrl;
    }

    /**
     * Set the model to use
     */
    public void setModel(String model) {
        this.model = model != null ? model : DEFAULT_MODEL;
    }

    /**
     * Get the current model
     */
    public String getModel() {
        return model;
    }

    /**
     * Get available models from the API
     */
    @Override
    public String[] getAvailableModels(String apiKey, String baseUrl) {
        if (apiKey == null || apiKey.trim().isEmpty()) {
            return new String[]{DEFAULT_MODEL};
        }

        try {
            String endpoint = (baseUrl != null ? baseUrl : this.baseUrl) + "/models";
            URL url = new URL(endpoint);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setRequestProperty("Authorization", "Bearer " + apiKey);
            conn.setRequestProperty("Content-Type", "application/json");

            // Set timeout values for FarTech AI provider
            conn.setConnectTimeout(CONNECT_TIMEOUT);
            conn.setReadTimeout(READ_TIMEOUT);

            int responseCode = conn.getResponseCode();

            InputStream is;
            if (responseCode >= 200 && responseCode < 300) {
                is = conn.getInputStream();
            } else {
                return getDefaultModels();
            }

            BufferedReader br = new BufferedReader(new InputStreamReader(is, "UTF-8"));
            StringBuilder response = new StringBuilder();
            String line;
            while ((line = br.readLine()) != null) {
                response.append(line);
            }
            br.close();

            String jsonResponse = response.toString();
            System.out.println("[FarTech AI DEBUG] Raw API response: " + jsonResponse.substring(0, Math.min(500, jsonResponse.length())) + "...");
            String[] models = parseModelsResponse(jsonResponse);
            System.out.println("[FarTech AI DEBUG] Parsed " + models.length + " models:");
            for (String model : models) {
                System.out.println("[FarTech AI DEBUG] Model: '" + model + "'");
            }
            return models.length > 0 ? models : getDefaultModels();

        } catch (Exception e) {
            return getDefaultModels();
        }
    }

    /**
     * Parse models response and extract model names
     */
    private String[] parseModelsResponse(String json) {
        try {
            java.util.List<String> models = new java.util.ArrayList<>();

            // Clear previous mapping
            modelNameMapping.clear();

            // Handle complex JSON format with data array
            if (json.contains("\"data\":") && json.contains("[")) {
                parseComplexModelsFormat(json, models);
            } else {
                // Standard OpenAI format fallback
                parseModelField(json, "\"id\":", models);
            }

            // Remove duplicates while preserving order
            java.util.Set<String> uniqueModels = new java.util.LinkedHashSet<>(models);
            models = new java.util.ArrayList<>(uniqueModels);

            if (models.isEmpty()) {
                return getDefaultModels();
            }

            // Sort models and return
            models.sort(String::compareTo);
            return models.toArray(new String[0]);

        } catch (Exception e) {
            return getDefaultModels();
        }
    }

    /**
     * Parse complex JSON format with nested data - Enhanced for FarTech AI provider
     */
    private void parseComplexModelsFormat(String json, java.util.List<String> models) {
        try {
            System.out.println("Parsing complex models format...");

            // Find all "id" fields in the entire JSON
            parseAllIdFields(json, models);

            // Also look for "name" fields as some models might use that
            parseAllNameFields(json, models);

            System.out.println("Found " + models.size() + " models total");

        } catch (Exception e) {
            System.err.println("Error in parseComplexModelsFormat: " + e.getMessage());
            parseModelField(json, "\"id\":", models);
        }
    }

    /**
     * Parse all "id" fields from JSON
     */
    private void parseAllIdFields(String json, java.util.List<String> models) {
        String[] parts = json.split("\"id\"\\s*:");
        for (int i = 1; i < parts.length; i++) {
            String part = parts[i].trim();
            if (part.startsWith("\"")) {
                int endQuote = part.indexOf("\"", 1);
                if (endQuote > 0) {
                    String modelId = part.substring(1, endQuote);
                    String cleanedModelId = cleanModelName(modelId);
                    if (isValidChatModel(modelId) && !models.contains(cleanedModelId)) {
                        models.add(cleanedModelId);
                        modelNameMapping.put(cleanedModelId, modelId); // Store mapping
                        System.out.println("[FarTech AI DEBUG] Added model mapping: '" + cleanedModelId + "' -> '" + modelId + "'");
                    }
                }
            }
        }
    }

    /**
     * Parse all "name" fields from JSON as alternative model identifiers
     */
    private void parseAllNameFields(String json, java.util.List<String> models) {
        String[] parts = json.split("\"name\"\\s*:");
        for (int i = 1; i < parts.length; i++) {
            String part = parts[i].trim();
            if (part.startsWith("\"")) {
                int endQuote = part.indexOf("\"", 1);
                if (endQuote > 0) {
                    String modelName = part.substring(1, endQuote);
                    String cleanedModelName = cleanModelName(modelName);
                    if (isValidChatModel(modelName) && !models.contains(cleanedModelName)) {
                        models.add(cleanedModelName);
                        modelNameMapping.put(cleanedModelName, modelName); // Store mapping
                        System.out.println("[FarTech AI DEBUG] Added model mapping: '" + cleanedModelName + "' -> '" + modelName + "'");
                    }
                }
            }
        }
    }

    /**
     * Clean model names for better display and API compatibility
     * Converts "Anthropic: Claude 3.5 Haiku.txt" to "Claude 3.5 Haiku"
     */
    private String cleanModelName(String modelName) {
        if (modelName == null || modelName.trim().isEmpty()) {
            return modelName;
        }

        String cleaned = modelName.trim();

        // Remove common prefixes
        String[] prefixes = {
            "Anthropic: ", "OpenAI: ", "Google: ", "Meta: ", "xAI: ",
            "Agentica: ", "Arcee AI: ", "Microsoft: ", "Mistral: ",
            "DeepSeek: ", "Inception: "
        };

        for (String prefix : prefixes) {
            if (cleaned.startsWith(prefix)) {
                cleaned = cleaned.substring(prefix.length());
                break;
            }
        }

        // Remove file extensions
        if (cleaned.endsWith(".txt")) {
            cleaned = cleaned.substring(0, cleaned.length() - 4);
        }

        // Clean up extra spaces
        cleaned = cleaned.trim();

        return cleaned;
    }

    private int findMatchingBracket(String json, int start) {
        int count = 1;
        for (int i = start + 1; i < json.length(); i++) {
            char c = json.charAt(i);
            if (c == '[') count++;
            else if (c == ']') count--;
            if (count == 0) return i;
        }
        return -1;
    }

    private String extractFieldValue(String jsonObj, String fieldPattern) {
        int fieldStart = jsonObj.indexOf(fieldPattern);
        if (fieldStart == -1) return null;

        int valueStart = jsonObj.indexOf("\"", fieldStart + fieldPattern.length());
        if (valueStart == -1) return null;

        int valueEnd = jsonObj.indexOf("\"", valueStart + 1);
        if (valueEnd == -1) return null;

        return jsonObj.substring(valueStart + 1, valueEnd);
    }

    private void parseModelField(String json, String fieldPattern, java.util.List<String> models) {
        String[] parts = json.split(fieldPattern);
        for (int i = 1; i < parts.length; i++) {
            String part = parts[i].trim();
            if (part.startsWith("\"")) {
                int endQuote = part.indexOf("\"", 1);
                if (endQuote > 0) {
                    String modelId = part.substring(1, endQuote);
                    if (isValidChatModel(modelId)) {
                        models.add(modelId);
                    }
                }
            }
        }
    }

    private boolean isValidChatModel(String modelId) {
        if (modelId == null || modelId.trim().isEmpty()) {
            return false;
        }

        String lowerModelId = modelId.toLowerCase();

        // Exclude non-chat models
        if (lowerModelId.contains("embedding") ||
            lowerModelId.contains("whisper") ||
            lowerModelId.contains("tts") ||
            lowerModelId.contains("dall-e") ||
            lowerModelId.contains("moderation")) {
            return false;
        }

        // Include common chat model patterns and FarTech AI specific models
        return lowerModelId.contains("gpt") ||
               lowerModelId.contains("claude") ||
               lowerModelId.contains("llama") ||
               lowerModelId.contains("gemini") ||
               lowerModelId.contains("mistral") ||
               lowerModelId.contains("devstral") ||
               lowerModelId.contains("kimi") ||
               lowerModelId.contains("deepseek") ||
               lowerModelId.contains("chat") ||
               lowerModelId.contains("instruct") ||
               lowerModelId.contains("turbo") ||
               lowerModelId.contains("comparer") ||
               lowerModelId.contains("kinto") ||
               lowerModelId.contains("anthropic") ||
               lowerModelId.contains("openai") ||
               lowerModelId.contains("google") ||
               lowerModelId.contains("meta") ||
               lowerModelId.contains("xai") ||
               lowerModelId.contains("grok") ||
               lowerModelId.contains("magistral") ||
               lowerModelId.contains("arcee") ||
               lowerModelId.contains("agentica") ||
               lowerModelId.contains("inception") ||
               lowerModelId.contains("microsoft") ||
               lowerModelId.contains("minimax") ||
               modelId.equals("Devstral Small") ||
               modelId.equals("Kimi Dev 72B") ||
               modelId.equals("llm-answer-comparer") ||
               modelId.equals("kinto-cr-776") ||
               modelId.equals("Magistral Small") ||
               modelId.equals("OpenAI Model Router");
    }

    private String[] getDefaultModels() {
        return new String[]{"Devstral Small", "Kimi Dev 72B", DEFAULT_MODEL, "gpt-4", "gpt-4-turbo"};
    }

    /**
     * Properly escape a string for JSON content
     * Handles all special characters that can cause JSON parsing errors
     */
    private String escapeJsonString(String input) {
        if (input == null) {
            return "";
        }

        StringBuilder escaped = new StringBuilder();
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            switch (c) {
                case '"':
                    escaped.append("\\\"");
                    break;
                case '\\':
                    escaped.append("\\\\");
                    break;
                case '\b':
                    escaped.append("\\b");
                    break;
                case '\f':
                    escaped.append("\\f");
                    break;
                case '\n':
                    escaped.append("\\n");
                    break;
                case '\r':
                    escaped.append("\\r");
                    break;
                case '\t':
                    escaped.append("\\t");
                    break;
                default:
                    // Handle control characters
                    if (c < 0x20) {
                        escaped.append(String.format("\\u%04x", (int) c));
                    } else {
                        escaped.append(c);
                    }
                    break;
            }
        }
        return escaped.toString();
    }
}
