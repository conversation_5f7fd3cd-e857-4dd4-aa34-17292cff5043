package com.example.fartechplugin.test;

import com.example.fartechplugin.core.*;

/**
 * Test class to demonstrate the Production AI Agent functionality
 * This shows how the agent handles delete operations without endless approval loops
 */
public class ProductionAgentTest {
    
    public static void main(String[] args) {
        System.out.println("=== FarTech AI Production Agent Test ===\n");
        
        // Test 1: Agent mode disabled (should require approval)
        testAgentModeDisabled();
        
        // Test 2: Agent mode enabled (should auto-approve safe operations)
        testAgentModeEnabled();
        
        // Test 3: Delete operation with agent mode
        testDeleteOperationWithAgentMode();
    }
    
    private static void testAgentModeDisabled() {
        System.out.println("🔧 Test 1: Agent Mode Disabled");
        System.out.println("Expected: Should require user approval for all operations\n");
        
        ProductionAIAgent agent = new ProductionAIAgent();
        agent.setAgentMode(false); // Disable agent mode
        
        IDEContext context = createTestContext();
        AgentResponse response = agent.processRequest("delete my test file", context);
        
        System.out.println("Response: " + response.getMessage());
        System.out.println("Requires Approval: " + response.requiresUserApproval());
        System.out.println("Success: " + response.isSuccess());
        System.out.println("Agent Mode: " + agent.isAgentModeEnabled());
        System.out.println("---\n");
    }
    
    private static void testAgentModeEnabled() {
        System.out.println("🤖 Test 2: Agent Mode Enabled");
        System.out.println("Expected: Should auto-approve safe operations\n");
        
        ProductionAIAgent agent = new ProductionAIAgent();
        agent.setAgentMode(true); // Enable agent mode
        
        IDEContext context = createTestContext();
        AgentResponse response = agent.processRequest("read the main configuration file", context);
        
        System.out.println("Response: " + response.getMessage());
        System.out.println("Requires Approval: " + response.requiresUserApproval());
        System.out.println("Success: " + response.isSuccess());
        System.out.println("Agent Mode: " + agent.isAgentModeEnabled());
        System.out.println("---\n");
    }
    
    private static void testDeleteOperationWithAgentMode() {
        System.out.println("🗑️ Test 3: Delete Operation with Agent Mode");
        System.out.println("Expected: Should handle delete operations intelligently\n");
        
        ProductionAIAgent agent = new ProductionAIAgent();
        agent.setAgentMode(true); // Enable agent mode
        
        IDEContext context = createTestContext();
        
        // Test delete operation
        AgentResponse response = agent.deleteFile("C:\\Dev\\FarTech\\Plugin\\my-spring-app");
        
        System.out.println("Delete Response: " + response.getMessage());
        System.out.println("Requires Approval: " + response.requiresUserApproval());
        System.out.println("Success: " + response.isSuccess());
        
        if (response.requiresUserApproval()) {
            System.out.println("Approval ID: " + response.getMetadata().get("approvalId"));
            
            // Simulate user approval
            String approvalId = (String) response.getMetadata().get("approvalId");
            if (approvalId != null) {
                AgentResponse approvalResponse = agent.handleUserApproval(approvalId, true, "User confirmed deletion");
                System.out.println("After Approval: " + approvalResponse.getMessage());
                System.out.println("Final Success: " + approvalResponse.isSuccess());
            }
        }
        
        System.out.println("---\n");
    }
    
    private static IDEContext createTestContext() {
        IDEContext context = new IDEContext();
        context.setProjectRoot("C:\\Dev\\FarTech\\Plugin");
        context.setCurrentFile("C:\\Dev\\FarTech\\Plugin\\my-spring-app\\src\\main\\java\\Main.java");
        return context;
    }
    
    /**
     * Test the approval workflow to ensure it doesn't loop
     */
    public static void testApprovalWorkflow() {
        System.out.println("🔄 Test: Approval Workflow");
        System.out.println("Testing that approval requests don't create endless loops\n");
        
        UserApprovalManager approvalManager = new UserApprovalManager();
        
        // Test 1: Manual mode (should require approval)
        approvalManager.setAgentMode(false);
        ApprovalRequest request1 = new ApprovalRequest(
            "Delete file test.txt", 
            "Will delete: test.txt", 
            null
        );
        
        boolean requiresApproval1 = approvalManager.requiresApproval(request1);
        System.out.println("Manual mode requires approval: " + requiresApproval1);
        
        // Test 2: Agent mode with safe operation (should not require approval)
        approvalManager.setAgentMode(true);
        ApprovalRequest request2 = new ApprovalRequest(
            "Read file config.properties", 
            "Will read: config.properties", 
            null
        );
        
        boolean requiresApproval2 = approvalManager.requiresApproval(request2);
        System.out.println("Agent mode (safe operation) requires approval: " + requiresApproval2);
        
        // Test 3: Agent mode with unsafe operation (should require approval)
        ApprovalRequest request3 = new ApprovalRequest(
            "Delete system file", 
            "Will delete: /system/important.cfg", 
            null
        );
        
        boolean requiresApproval3 = approvalManager.requiresApproval(request3);
        System.out.println("Agent mode (unsafe operation) requires approval: " + requiresApproval3);
        
        System.out.println("---\n");
    }
    
    /**
     * Demonstrate the solution to the endless approval loop problem
     */
    public static void demonstrateSolution() {
        System.out.println("💡 SOLUTION DEMONSTRATION");
        System.out.println("How FarTech AI Production Agent solves the endless approval loop:\n");
        
        System.out.println("1. ✅ AGENT MODE DETECTION");
        System.out.println("   - When agent mode is enabled, safe operations are auto-approved");
        System.out.println("   - Delete operations for temp/test files are considered safe");
        System.out.println("   - Read operations are always considered safe\n");
        
        System.out.println("2. ✅ INTELLIGENT APPROVAL LOGIC");
        System.out.println("   - UserApprovalManager.requiresApproval() checks operation safety");
        System.out.println("   - Safe operations bypass the approval workflow entirely");
        System.out.println("   - Only unsafe operations require user confirmation\n");
        
        System.out.println("3. ✅ ACTUAL EXECUTION");
        System.out.println("   - DeleteFileTool.execute() performs real file deletion");
        System.out.println("   - When 'approved=true' parameter is set, files are actually deleted");
        System.out.println("   - No more simulation - real file operations\n");
        
        System.out.println("4. ✅ SINGLE APPROVAL FLOW");
        System.out.println("   - Each operation gets ONE approval request maximum");
        System.out.println("   - No repeated confirmation requests");
        System.out.println("   - Clear success/failure responses\n");
        
        System.out.println("🎯 RESULT: User-friendly delete operations without endless loops!");
    }
}
