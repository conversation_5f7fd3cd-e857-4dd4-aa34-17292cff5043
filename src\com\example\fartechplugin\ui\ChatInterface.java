package com.example.fartechplugin.ui;

import org.eclipse.swt.SWT;
import org.eclipse.swt.custom.StyledText;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.*;
import java.util.ArrayList;
import java.util.List;
import com.example.fartechplugin.utils.ConversationEntry;

/**
 * Handles the chat interface UI components and conversation display
 */
public class ChatInterface {
    
    private StyledText conversationText;
    private Text inputText;
    private Button sendButton;
    private Button attachButton;
    private List<ConversationEntry> conversationHistory;
    private ChatInterfaceListener listener;
    
    public interface ChatInterfaceListener {
        void onSendMessage(String message);
        void onAttachFile();
        void onClipboardPaste();
    }
    
    public ChatInterface(ChatInterfaceListener listener) {
        this.listener = listener;
        this.conversationHistory = new ArrayList<>();
    }
    
    public void createChatInterface(Composite parent) {
        Composite chatSection = new Composite(parent, SWT.NONE);
        chatSection.setLayout(new GridLayout(1, false));

        // Conversation history area
        Label historyLabel = new Label(chatSection, SWT.NONE);
        historyLabel.setText("Chat with FarTech AI:");
        historyLabel.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));

        conversationText = new StyledText(chatSection, SWT.BORDER | SWT.MULTI | SWT.READ_ONLY |
                                         SWT.V_SCROLL | SWT.WRAP);
        conversationText.setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));

        // Set chat background color (light gray like WhatsApp)
        conversationText.setBackground(chatSection.getDisplay().getSystemColor(SWT.COLOR_WIDGET_LIGHT_SHADOW));

        // Input area
        createInputArea(chatSection);
    }
    
    private void createInputArea(Composite parent) {
        Composite inputSection = new Composite(parent, SWT.NONE);
        inputSection.setLayoutData(new GridData(SWT.FILL, SWT.BOTTOM, true, false));
        inputSection.setLayout(new GridLayout(3, false));

        inputText = new Text(inputSection, SWT.BORDER | SWT.MULTI | SWT.WRAP | SWT.V_SCROLL);
        GridData inputData = new GridData(SWT.FILL, SWT.BOTTOM, true, false);
        inputData.heightHint = 80;
        inputData.widthHint = 400;
        inputText.setLayoutData(inputData);

        // Attach file button
        attachButton = new Button(inputSection, SWT.PUSH);
        attachButton.setText("Attach");
        attachButton.setToolTipText("Attach files or images");
        GridData attachData = new GridData(SWT.FILL, SWT.BOTTOM, false, false);
        attachData.heightHint = 80;
        attachData.widthHint = 80;
        attachButton.setLayoutData(attachData);
        attachButton.addListener(SWT.Selection, e -> {
            if (listener != null) listener.onAttachFile();
        });

        sendButton = new Button(inputSection, SWT.PUSH);
        sendButton.setText("Send");
        GridData buttonData = new GridData(SWT.FILL, SWT.BOTTOM, false, false);
        buttonData.heightHint = 80;
        buttonData.widthHint = 80;
        sendButton.setLayoutData(buttonData);
        sendButton.addListener(SWT.Selection, e -> sendMessage());

        // Key listeners
        inputText.addListener(SWT.KeyDown, e -> {
            if (e.keyCode == SWT.CR && (e.stateMask & SWT.CTRL) != 0) {
                sendMessage();
            } else if (e.keyCode == 'v' && (e.stateMask & SWT.CTRL) != 0) {
                if (listener != null) listener.onClipboardPaste();
            }
        });
    }
    
    private void sendMessage() {
        String message = inputText.getText().trim();
        if (!message.isEmpty() && listener != null) {
            listener.onSendMessage(message);
            inputText.setText("");
        }
    }
    
    public void addToConversation(String sender, String message, boolean isResponse) {
        ConversationEntry entry = new ConversationEntry(sender, message, System.currentTimeMillis());
        conversationHistory.add(entry);

        if (conversationText != null && !conversationText.isDisposed()) {
            String timestamp = new java.text.SimpleDateFormat("HH:mm:ss").format(new java.util.Date());
            String formattedMessage = createChatBubble(sender, message, timestamp, isResponse);
            conversationText.append(formattedMessage);
            conversationText.setTopIndex(conversationText.getLineCount() - 1);
        }
    }
    
    private String createChatBubble(String sender, String message, String timestamp, boolean isResponse) {
        StringBuilder result = new StringBuilder();
        boolean isUser = "You".equals(sender);
        boolean isSystem = "System".equals(sender);

        if (isSystem) {
            // System messages - centered
            String systemMessage = "--- " + sender + " " + timestamp + " ---";
            int padding = Math.max(0, (80 - systemMessage.length()) / 2);
            for (int i = 0; i < padding; i++) {
                result.append(" ");
            }
            result.append(systemMessage).append("\n");

            String[] lines = message.split("\n");
            for (String line : lines) {
                if (line.trim().length() > 0) {
                    int linePadding = Math.max(0, (80 - line.trim().length()) / 2);
                    for (int i = 0; i < linePadding; i++) {
                        result.append(" ");
                    }
                    result.append(line.trim()).append("\n");
                } else {
                    result.append("\n");
                }
            }
            result.append("\n");
        } else if (isUser) {
            result.append(timestamp).append("\n");
            result.append("You: ").append(message).append("\n\n");
        } else {
            result.append(timestamp).append("\n");
            result.append(message).append("\n\n");
        }

        return result.toString();
    }
    
    public void clearConversation() {
        conversationHistory.clear();
        if (conversationText != null && !conversationText.isDisposed()) {
            conversationText.setText("");
        }
    }
    
    public void setInputPlaceholder(String placeholder) {
        if (inputText != null && !inputText.isDisposed()) {
            inputText.setMessage(placeholder);
        }
    }
    
    public void setSendButtonEnabled(boolean enabled) {
        if (sendButton != null && !sendButton.isDisposed()) {
            sendButton.setEnabled(enabled);
        }
    }
    
    public boolean isDisposed() {
        return conversationText == null || conversationText.isDisposed();
    }
    
    public void setFocus() {
        if (inputText != null && !inputText.isDisposed()) {
            inputText.setFocus();
        }
    }
    
    public Shell getShell() {
        if (inputText != null && !inputText.isDisposed()) {
            return inputText.getShell();
        }
        return null;
    }
    
    public Display getDisplay() {
        if (inputText != null && !inputText.isDisposed()) {
            return inputText.getDisplay();
        }
        return null;
    }
}
