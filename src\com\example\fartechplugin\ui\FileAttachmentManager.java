package com.example.fartechplugin.ui;

import org.eclipse.swt.SWT;
import org.eclipse.swt.widgets.*;
import org.eclipse.swt.dnd.*;
import org.eclipse.swt.graphics.ImageData;
import org.eclipse.swt.graphics.ImageLoader;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Base64;
import java.util.ArrayList;
import java.util.List;

/**
 * Manages file attachments including drag & drop, clipboard paste, and file dialogs
 */
public class FileAttachmentManager {
    
    private List<AttachedFile> attachedFiles;
    private FileAttachmentListener listener;
    
    public interface FileAttachmentListener {
        void onFileAttached(AttachedFile file);
        void onAttachmentError(String message);
        void onAttachmentsUpdated(List<AttachedFile> files);
    }
    
    public FileAttachmentManager(FileAttachmentListener listener) {
        this.listener = listener;
        this.attachedFiles = new ArrayList<>();
    }
    
    public void addDragDropSupport(Text textWidget) {
        try {
            DropTarget dropTarget = new DropTarget(textWidget, DND.DROP_COPY | DND.DROP_MOVE | DND.DROP_DEFAULT);
            dropTarget.setTransfer(new Transfer[] { FileTransfer.getInstance() });

            dropTarget.addDropListener(new DropTargetAdapter() {
                @Override
                public void dragEnter(DropTargetEvent event) {
                    if (event.detail == DND.DROP_DEFAULT) {
                        event.detail = DND.DROP_COPY;
                    }
                }

                @Override
                public void dragOver(DropTargetEvent event) {
                    event.feedback = DND.FEEDBACK_SELECT | DND.FEEDBACK_SCROLL;
                }

                @Override
                public void drop(DropTargetEvent event) {
                    try {
                        if (FileTransfer.getInstance().isSupportedType(event.currentDataType)) {
                            String[] files = (String[]) event.data;
                            if (files != null && files.length > 0) {
                                for (String filePath : files) {
                                    File file = new File(filePath);
                                    if (file.exists()) {
                                        attachFile(file);
                                    }
                                }
                                notifyAttachmentsUpdated();
                            }
                        }
                    } catch (Exception e) {
                        notifyError("Error processing dropped files: " + e.getMessage());
                    }
                }
            });
        } catch (Exception e) {
            notifyError("Drag & drop not available: " + e.getMessage());
        }
    }
    
    public void handleClipboardPaste(Display display) {
        try {
            Clipboard clipboard = new Clipboard(display);

            // Try to get image from clipboard first
            ImageData imageData = (ImageData) clipboard.getContents(ImageTransfer.getInstance());
            if (imageData != null) {
                File tempFile = saveClipboardImage(imageData);
                if (tempFile != null) {
                    attachFile(tempFile);
                    notifyAttachmentsUpdated();
                }
            } else {
                // Try to get file paths from clipboard
                String[] files = (String[]) clipboard.getContents(FileTransfer.getInstance());
                if (files != null && files.length > 0) {
                    for (String filePath : files) {
                        File file = new File(filePath);
                        if (file.exists()) {
                            attachFile(file);
                        }
                    }
                    notifyAttachmentsUpdated();
                }
            }

            clipboard.dispose();
        } catch (Exception e) {
            notifyError("Clipboard paste failed: " + e.getMessage());
        }
    }
    
    private File saveClipboardImage(ImageData imageData) {
        try {
            File tempDir = new File(System.getProperty("java.io.tmpdir"));
            File tempFile = new File(tempDir, "clipboard_image_" + System.currentTimeMillis() + ".png");

            ImageLoader loader = new ImageLoader();
            loader.data = new ImageData[] { imageData };
            loader.save(tempFile.getAbsolutePath(), SWT.IMAGE_PNG);

            return tempFile;
        } catch (Exception e) {
            notifyError("Failed to save clipboard image: " + e.getMessage());
            return null;
        }
    }
    
    public void openFileDialog(Shell shell) {
        FileDialog dialog = new FileDialog(shell, SWT.OPEN | SWT.MULTI);
        dialog.setText("Select Files to Attach");
        dialog.setFilterNames(new String[] {
            "All Files (*.*)",
            "Images (*.png;*.jpg;*.jpeg;*.gif;*.bmp)",
            "Text Files (*.txt;*.md;*.java;*.py;*.js;*.html;*.css)",
            "Documents (*.pdf;*.doc;*.docx)"
        });
        dialog.setFilterExtensions(new String[] {
            "*.*",
            "*.png;*.jpg;*.jpeg;*.gif;*.bmp",
            "*.txt;*.md;*.java;*.py;*.js;*.html;*.css",
            "*.pdf;*.doc;*.docx"
        });

        String firstFile = dialog.open();
        if (firstFile != null) {
            String[] selectedFiles = dialog.getFileNames();
            String filterPath = dialog.getFilterPath();

            for (String fileName : selectedFiles) {
                File file = new File(filterPath, fileName);
                attachFile(file);
            }
            notifyAttachmentsUpdated();
        }
    }
    
    private void attachFile(File file) {
        try {
            if (!file.exists() || !file.canRead()) {
                notifyError("Cannot read file: " + file.getName());
                return;
            }

            // Check file size (limit to 10MB)
            long maxSize = 10 * 1024 * 1024; // 10MB
            if (file.length() > maxSize) {
                notifyError("File too large (max 10MB): " + file.getName());
                return;
            }

            AttachedFile attachedFile = new AttachedFile(file);
            attachedFiles.add(attachedFile);
            
            if (listener != null) {
                listener.onFileAttached(attachedFile);
            }

        } catch (Exception e) {
            notifyError("Failed to attach file: " + e.getMessage());
        }
    }
    
    private void notifyError(String message) {
        if (listener != null) {
            listener.onAttachmentError(message);
        }
    }
    
    private void notifyAttachmentsUpdated() {
        if (listener != null) {
            listener.onAttachmentsUpdated(new ArrayList<>(attachedFiles));
        }
    }
    
    public List<AttachedFile> getAttachedFiles() {
        return new ArrayList<>(attachedFiles);
    }
    
    public void clearAttachments() {
        attachedFiles.clear();
        notifyAttachmentsUpdated();
    }
    
    public boolean hasAttachments() {
        return !attachedFiles.isEmpty();
    }
    
    public String formatFileSize(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
    }
    
    /**
     * Inner class for attached files
     */
    public static class AttachedFile {
        private final String name;
        private final String path;
        private final long size;
        private final String mimeType;
        private final byte[] content;
        private final boolean isImage;

        public AttachedFile(File file) throws IOException {
            this.name = file.getName();
            this.path = file.getAbsolutePath();
            this.size = file.length();
            this.content = Files.readAllBytes(file.toPath());
            this.mimeType = determineMimeType(file.getName());
            this.isImage = isImageFile(file.getName());
        }

        private String determineMimeType(String fileName) {
            String lower = fileName.toLowerCase();
            if (lower.endsWith(".png")) return "image/png";
            if (lower.endsWith(".jpg") || lower.endsWith(".jpeg")) return "image/jpeg";
            if (lower.endsWith(".gif")) return "image/gif";
            if (lower.endsWith(".bmp")) return "image/bmp";
            if (lower.endsWith(".txt")) return "text/plain";
            if (lower.endsWith(".md")) return "text/markdown";
            if (lower.endsWith(".java")) return "text/x-java-source";
            if (lower.endsWith(".py")) return "text/x-python";
            if (lower.endsWith(".js")) return "text/javascript";
            if (lower.endsWith(".html")) return "text/html";
            if (lower.endsWith(".css")) return "text/css";
            if (lower.endsWith(".pdf")) return "application/pdf";
            return "application/octet-stream";
        }

        private boolean isImageFile(String fileName) {
            String lower = fileName.toLowerCase();
            return lower.endsWith(".png") || lower.endsWith(".jpg") ||
                   lower.endsWith(".jpeg") || lower.endsWith(".gif") ||
                   lower.endsWith(".bmp");
        }

        public String getName() { return name; }
        public String getPath() { return path; }
        public long getSize() { return size; }
        public String getMimeType() { return mimeType; }
        public byte[] getContent() { return content; }
        public boolean isImage() { return isImage; }

        public String getBase64Content() {
            return Base64.getEncoder().encodeToString(content);
        }

        public String getContentAsText() {
            if (isImage) {
                return "[Image: " + name + " - " + mimeType + "]";
            }
            try {
                return new String(content, "UTF-8");
            } catch (Exception e) {
                return "[Binary file: " + name + " - " + mimeType + "]";
            }
        }
    }
}
