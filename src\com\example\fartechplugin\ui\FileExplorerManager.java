package com.example.fartechplugin.ui;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.GridLayout;
import org.eclipse.swt.widgets.*;
import org.eclipse.jface.viewers.*;
import org.eclipse.core.resources.*;
import com.example.fartechplugin.utils.EclipseCompatibility;

/**
 * Manages the file explorer tree view and workspace navigation
 */
public class FileExplorerManager {
    
    private TreeViewer fileExplorer;
    private FileExplorerListener listener;
    
    public interface FileExplorerListener {
        void onFileSelected(Object file); // Using Object to avoid Eclipse dependency issues
        void onExplorerError(String message);
    }
    
    public FileExplorerManager(FileExplorerListener listener) {
        this.listener = listener;
    }
    
    public void createFileExplorer(Composite parent) {
        Composite fileSection = new Composite(parent, SWT.NONE);
        fileSection.setLayout(new GridLayout(1, false));

        Label fileLabel = new Label(fileSection, SWT.NONE);
        fileLabel.setText("Workspace Files:");
        fileLabel.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));

        fileExplorer = new TreeViewer(fileSection, SWT.BORDER | SWT.SINGLE | SWT.V_SCROLL | SWT.H_SCROLL);
        fileExplorer.getTree().setLayoutData(new GridData(SWT.FILL, SWT.FILL, true, true));

        setupProviders();
        setupListeners();
    }
    
    private void setupProviders() {
        // Set content and label providers with compatibility layer
        if (EclipseCompatibility.isEclipseAvailable()) {
            try {
                // Use default Eclipse workspace providers
                boolean contentSet = EclipseCompatibility.setContentProvider(fileExplorer, createWorkspaceContentProvider());
                boolean labelSet = EclipseCompatibility.setLabelProvider(fileExplorer, createWorkspaceLabelProvider());

                if (!contentSet || !labelSet) {
                    notifyError("File explorer providers not available");
                }
            } catch (Exception e) {
                notifyError("File explorer setup failed: " + e.getMessage());
            }
        } else {
            notifyError("File explorer not available - Eclipse environment required");
        }
    }

    /**
     * Create workspace content provider using reflection
     */
    private Object createWorkspaceContentProvider() {
        try {
            // Try to create BaseWorkbenchContentProvider or similar
            Class<?> providerClass = Class.forName("org.eclipse.ui.model.BaseWorkbenchContentProvider");
            return providerClass.newInstance();
        } catch (Exception e) {
            try {
                // Fallback to WorkbenchContentProvider
                Class<?> providerClass = Class.forName("org.eclipse.ui.model.WorkbenchContentProvider");
                return providerClass.newInstance();
            } catch (Exception e2) {
                return null;
            }
        }
    }

    /**
     * Create workspace label provider using reflection
     */
    private Object createWorkspaceLabelProvider() {
        try {
            // Try to create WorkbenchLabelProvider
            Class<?> providerClass = Class.forName("org.eclipse.ui.model.WorkbenchLabelProvider");
            return providerClass.newInstance();
        } catch (Exception e) {
            return null;
        }
    }
    
    private void setupListeners() {
        // Add double-click listener to insert file path
        fileExplorer.addDoubleClickListener(event -> {
            IStructuredSelection selection = (IStructuredSelection) event.getSelection();
            Object element = selection.getFirstElement();
            if (element instanceof IFile) {
                IFile file = (IFile) element;
                if (listener != null) {
                    listener.onFileSelected(file);
                }
            }
        });
    }
    
    public void refreshFileExplorer() {
        if (fileExplorer != null && !fileExplorer.getTree().isDisposed()) {
            try {
                IWorkspaceRoot root = ResourcesPlugin.getWorkspace().getRoot();
                fileExplorer.setInput(root);
                fileExplorer.refresh();
            } catch (Exception e) {
                notifyError("Failed to refresh file explorer: " + e.getMessage());
            }
        }
    }
    
    private void notifyError(String message) {
        if (listener != null) {
            listener.onExplorerError(message);
        }
    }
    
    public boolean isDisposed() {
        return fileExplorer == null || fileExplorer.getTree().isDisposed();
    }
    
    public TreeViewer getTreeViewer() {
        return fileExplorer;
    }
}
