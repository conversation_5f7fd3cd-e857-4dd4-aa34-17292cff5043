package com.example.fartechplugin.ui;

import org.eclipse.swt.SWT;
import org.eclipse.swt.layout.GridData;
import org.eclipse.swt.layout.RowLayout;
import org.eclipse.swt.widgets.*;

/**
 * Manages the toolbar with various action buttons
 */
public class ToolbarManager {
    
    private Button enableAgentButton;
    private ToolbarListener listener;
    private boolean agentMode;
    
    public interface ToolbarListener {
        void onSettingsClicked();
        void onAgentModeToggled(boolean enabled);
        void onClearHistoryClicked();
        void onRefreshFilesClicked();
        void onIndexWorkspaceClicked();
        void onShowIndexClicked();
    }
    
    public ToolbarManager(ToolbarListener listener) {
        this.listener = listener;
        this.agentMode = false;
    }
    
    public void createToolbar(Composite parent) {
        Composite toolbar = new Composite(parent, SWT.NONE);
        toolbar.setLayoutData(new GridData(SWT.FILL, SWT.TOP, true, false));
        toolbar.setLayout(new RowLayout(SWT.HORIZONTAL));

        // Settings button
        Button settingsButton = new Button(toolbar, SWT.PUSH);
        settingsButton.setText("Settings");
        settingsButton.setToolTipText("Configure AI provider and API key");
        settingsButton.addListener(SWT.Selection, e -> {
            if (listener != null) listener.onSettingsClicked();
        });

        // Agent mode toggle button
        enableAgentButton = new Button(toolbar, SWT.TOGGLE);
        enableAgentButton.setSelection(agentMode);
        updateAgentButtonText();
        enableAgentButton.addListener(SWT.Selection, e -> {
            agentMode = enableAgentButton.getSelection();
            updateAgentButtonText();
            if (listener != null) listener.onAgentModeToggled(agentMode);
        });

        // Clear history button
        Button clearButton = new Button(toolbar, SWT.PUSH);
        clearButton.setText("Clear History");
        clearButton.setToolTipText("Clear conversation history");
        clearButton.addListener(SWT.Selection, e -> {
            if (listener != null) listener.onClearHistoryClicked();
        });

        // Refresh files button
        Button refreshButton = new Button(toolbar, SWT.PUSH);
        refreshButton.setText("Refresh Files");
        refreshButton.setToolTipText("Refresh file explorer");
        refreshButton.addListener(SWT.Selection, e -> {
            if (listener != null) listener.onRefreshFilesClicked();
        });

        // Index workspace button
        Button indexButton = new Button(toolbar, SWT.PUSH);
        indexButton.setText("Index Workspace");
        indexButton.setToolTipText("Index workspace files for AI context");
        indexButton.addListener(SWT.Selection, e -> {
            if (listener != null) listener.onIndexWorkspaceClicked();
        });

        // Show index button
        Button showIndexButton = new Button(toolbar, SWT.PUSH);
        showIndexButton.setText("Show Index");
        showIndexButton.setToolTipText("Show workspace index information");
        showIndexButton.addListener(SWT.Selection, e -> {
            if (listener != null) listener.onShowIndexClicked();
        });
    }
    
    public void setAgentMode(boolean enabled) {
        this.agentMode = enabled;
        if (enableAgentButton != null && !enableAgentButton.isDisposed()) {
            enableAgentButton.setSelection(enabled);
            updateAgentButtonText();
        }
    }
    
    public boolean getAgentMode() {
        return agentMode;
    }
    
    private void updateAgentButtonText() {
        if (enableAgentButton != null && !enableAgentButton.isDisposed()) {
            if (agentMode) {
                enableAgentButton.setText("Disable Agent Mode");
                enableAgentButton.setToolTipText("Disable AI agent file operations");
            } else {
                enableAgentButton.setText("Enable Agent Mode");
                enableAgentButton.setToolTipText("Enable AI agent to perform file operations");
            }
        }
    }
    
    public boolean isDisposed() {
        return enableAgentButton == null || enableAgentButton.isDisposed();
    }
}
