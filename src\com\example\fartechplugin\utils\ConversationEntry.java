package com.example.fartechplugin.utils;

/**
 * Represents a single conversation entry in the chat history
 * Simple data class for storing conversation messages
 */
public class ConversationEntry {
    
    private final String sender;
    private final String message;
    private final long timestamp;
    
    public ConversationEntry(String sender, String message, long timestamp) {
        this.sender = sender;
        this.message = message;
        this.timestamp = timestamp;
    }
    
    public String getSender() {
        return sender;
    }
    
    public String getMessage() {
        return message;
    }
    
    public long getTimestamp() {
        return timestamp;
    }
    
    @Override
    public String toString() {
        return String.format("[%s] %s: %s", 
            new java.text.SimpleDateFormat("HH:mm:ss").format(new java.util.Date(timestamp)),
            sender, 
            message);
    }
}
