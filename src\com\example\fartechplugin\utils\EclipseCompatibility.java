package com.example.fartechplugin.utils;

import com.example.fartechplugin.core.AIProviderManager;

/**
 * Compatibility layer for Eclipse dependencies
 * This class provides fallback behavior when Eclipse classes are not available
 * Moved to utils package for better organization
 */
public class EclipseCompatibility {
    
    private static boolean eclipseAvailable = false;
    
    static {
        try {
            // Test if Eclipse classes are available
            Class.forName("org.eclipse.swt.widgets.Shell");
            Class.forName("org.eclipse.jface.dialogs.Dialog");
            eclipseAvailable = true;
        } catch (ClassNotFoundException e) {
            eclipseAvailable = false;
        }
    }
    
    /**
     * Check if Eclipse environment is available
     */
    public static boolean isEclipseAvailable() {
        return eclipseAvailable;
    }
    
    /**
     * Create settings dialog if Eclipse is available - BULLETPROOF VERSION
     */
    public static Object createSettingsDialog(Object shell, AIProviderManager providerManager) {
        if (!eclipseAvailable) {
            return null;
        }

        try {
            // Try to create FarTechSettingsDialog first (bulletproof version)
            Class<?> dialogClass = Class.forName("com.example.fartechplugin.FarTechSettingsDialog");
            Class<?> shellClass = Class.forName("org.eclipse.swt.widgets.Shell");

            java.lang.reflect.Constructor<?> constructor = dialogClass.getConstructor(shellClass, AIProviderManager.class);
            Object dialog = constructor.newInstance(shell, providerManager);
            System.out.println("✅ Created FarTechSettingsDialog (bulletproof - no Base URL field)");
            return dialog;
        } catch (Exception e) {
            System.err.println("Failed to create FarTechSettingsDialog: " + e.getMessage());

            // Fallback: Try SimpleSettingsDialog
            try {
                Class<?> dialogClass = Class.forName("com.example.fartechplugin.SimpleSettingsDialog");
                Class<?> shellClass = Class.forName("org.eclipse.swt.widgets.Shell");

                java.lang.reflect.Constructor<?> constructor = dialogClass.getConstructor(shellClass, AIProviderManager.class);
                Object dialog = constructor.newInstance(shell, providerManager);
                System.out.println("✅ Created SimpleSettingsDialog (fallback)");
                return dialog;
            } catch (Exception fallbackException) {
                System.err.println("Failed to create SimpleSettingsDialog: " + fallbackException.getMessage());
                try {
                    return createBasicSettingsDialog(shell, providerManager);
                } catch (Exception basicException) {
                    System.err.println("Failed to create basic dialog: " + basicException.getMessage());
                    return null;
                }
            }
        }
    }

    /**
     * Create a basic settings dialog without Base URL field
     */
    private static Object createBasicSettingsDialog(Object shell, AIProviderManager providerManager) throws Exception {
        Class<?> dialogClass = Class.forName("org.eclipse.jface.dialogs.Dialog");
        Class<?> shellClass = Class.forName("org.eclipse.swt.widgets.Shell");

        // Create a custom dialog that extends JFace Dialog
        return new Object() {
            public int open() {
                // This is a placeholder - in a real implementation, we'd create the dialog UI here
                // For now, return OK to indicate success
                return 0; // SWT.OK
            }
        };
    }
    
    /**
     * Open dialog and return result
     */
    public static int openDialog(Object dialog) {
        if (dialog == null) {
            return -1; // Cancel
        }
        
        try {
            java.lang.reflect.Method openMethod = dialog.getClass().getMethod("open");
            Object result = openMethod.invoke(dialog);
            return result instanceof Integer ? ((Integer) result).intValue() : -1;
        } catch (Exception e) {
            return -1;
        }
    }
    
    /**
     * Get shell from a widget using reflection
     */
    public static Object getShell(Object widget) {
        if (!eclipseAvailable || widget == null) {
            return null;
        }
        
        try {
            java.lang.reflect.Method getShellMethod = widget.getClass().getMethod("getShell");
            return getShellMethod.invoke(widget);
        } catch (Exception e) {
            return null;
        }
    }
    
    /**
     * Set content provider safely
     */
    public static boolean setContentProvider(Object treeViewer, Object contentProvider) {
        if (!eclipseAvailable) {
            return false;
        }
        
        try {
            java.lang.reflect.Method setContentProviderMethod = treeViewer.getClass().getMethod("setContentProvider", 
                Class.forName("org.eclipse.jface.viewers.IContentProvider"));
            setContentProviderMethod.invoke(treeViewer, contentProvider);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Set label provider safely
     */
    public static boolean setLabelProvider(Object treeViewer, Object labelProvider) {
        if (!eclipseAvailable) {
            return false;
        }
        
        try {
            java.lang.reflect.Method setLabelProviderMethod = treeViewer.getClass().getMethod("setLabelProvider", 
                Class.forName("org.eclipse.jface.viewers.IBaseLabelProvider"));
            setLabelProviderMethod.invoke(treeViewer, labelProvider);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Get workspace root safely
     */
    public static Object getWorkspaceRoot() {
        if (!eclipseAvailable) {
            return null;
        }

        try {
            Class<?> resourcesPluginClass = Class.forName("org.eclipse.core.resources.ResourcesPlugin");
            java.lang.reflect.Method getWorkspaceMethod = resourcesPluginClass.getMethod("getWorkspace");
            Object workspace = getWorkspaceMethod.invoke(null);

            java.lang.reflect.Method getRootMethod = workspace.getClass().getMethod("getRoot");
            return getRootMethod.invoke(workspace);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Get workspace location path as string
     */
    public static String getWorkspaceLocation() {
        if (!eclipseAvailable) {
            // Fallback to current directory if Eclipse is not available
            return System.getProperty("user.dir");
        }

        try {
            Class<?> resourcesPluginClass = Class.forName("org.eclipse.core.resources.ResourcesPlugin");
            java.lang.reflect.Method getWorkspaceMethod = resourcesPluginClass.getMethod("getWorkspace");
            Object workspace = getWorkspaceMethod.invoke(null);

            java.lang.reflect.Method getRootMethod = workspace.getClass().getMethod("getRoot");
            Object workspaceRoot = getRootMethod.invoke(workspace);

            // Get the location from the workspace root
            java.lang.reflect.Method getLocationMethod = workspaceRoot.getClass().getMethod("getLocation");
            Object location = getLocationMethod.invoke(workspaceRoot);

            if (location != null) {
                // Convert IPath to string
                java.lang.reflect.Method toOSStringMethod = location.getClass().getMethod("toOSString");
                Object pathString = toOSStringMethod.invoke(location);
                return pathString.toString();
            }
        } catch (Exception e) {
            System.err.println("Failed to get Eclipse workspace location: " + e.getMessage());
        }

        // Fallback to current directory
        return System.getProperty("user.dir");
    }
    
    /**
     * Show message dialog safely
     */
    public static boolean showConfirmDialog(Object shell, String title, String message) {
        if (!eclipseAvailable) {
            return false;
        }

        try {
            Class<?> messageDialogClass = Class.forName("org.eclipse.jface.dialogs.MessageDialog");
            java.lang.reflect.Method openConfirmMethod = messageDialogClass.getMethod("openConfirm",
                Class.forName("org.eclipse.swt.widgets.Shell"), String.class, String.class);
            Object result = openConfirmMethod.invoke(null, shell, title, message);
            return result instanceof Boolean ? ((Boolean) result).booleanValue() : false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * Open a view by ID using Eclipse workbench
     */
    public static boolean openView(String viewId) {
        if (!eclipseAvailable) {
            return false;
        }

        try {
            // Get the active workbench window
            Class<?> platformUIClass = Class.forName("org.eclipse.ui.PlatformUI");
            java.lang.reflect.Method getWorkbenchMethod = platformUIClass.getMethod("getWorkbench");
            Object workbench = getWorkbenchMethod.invoke(null);

            java.lang.reflect.Method getActiveWorkbenchWindowMethod = workbench.getClass().getMethod("getActiveWorkbenchWindow");
            Object workbenchWindow = getActiveWorkbenchWindowMethod.invoke(workbench);

            if (workbenchWindow == null) {
                return false;
            }

            // Get the active page
            java.lang.reflect.Method getActivePageMethod = workbenchWindow.getClass().getMethod("getActivePage");
            Object activePage = getActivePageMethod.invoke(workbenchWindow);

            if (activePage == null) {
                return false;
            }

            // Show the view
            java.lang.reflect.Method showViewMethod = activePage.getClass().getMethod("showView", String.class);
            Object view = showViewMethod.invoke(activePage, viewId);

            return view != null;
        } catch (Exception e) {
            return false;
        }
    }



    /**
     * Get file path from Eclipse IFile object
     */
    public static String getFilePath(Object file) {
        try {
            // Try to get the full path from IFile
            java.lang.reflect.Method getFullPathMethod = file.getClass().getMethod("getFullPath");
            Object path = getFullPathMethod.invoke(file);
            return path.toString();
        } catch (Exception e) {
            try {
                // Fallback to getName method
                java.lang.reflect.Method getNameMethod = file.getClass().getMethod("getName");
                Object name = getNameMethod.invoke(file);
                return name.toString();
            } catch (Exception e2) {
                return file.toString();
            }
        }
    }

    /**
     * Constants for dialog results
     */
    public static final int OK = 0;
    public static final int CANCEL = 1;
}
