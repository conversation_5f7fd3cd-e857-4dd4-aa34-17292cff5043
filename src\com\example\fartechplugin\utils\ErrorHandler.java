package com.example.fartechplugin.utils;

import org.eclipse.core.runtime.IStatus;
import org.eclipse.core.runtime.Status;
import org.eclipse.jface.dialogs.ErrorDialog;
import org.eclipse.jface.dialogs.MessageDialog;
import org.eclipse.swt.widgets.Display;
import org.eclipse.swt.widgets.Shell;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import com.example.fartechplugin.FarTechPlugin;

/**
 * Comprehensive error handling and logging system
 * Moved to utils package for better organization
 */
public class ErrorHandler {
    
    private static final int MAX_ERROR_COUNT = 10;
    private static final long ERROR_RESET_INTERVAL = 300000; // 5 minutes
    
    private final ConcurrentHashMap<String, AtomicInteger> errorCounts = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Long> lastErrorTimes = new ConcurrentHashMap<>();
    
    /**
     * Handle a general error
     */
    public void handleError(String message, Throwable throwable) {
        handleError(message, throwable, true, true);
    }
    
    /**
     * Handle an error with options
     */
    public void handleError(String message, Throwable throwable, boolean logToEclipse, boolean showToUser) {
        try {
            // Create error key for tracking
            String errorKey = message + (throwable != null ? throwable.getClass().getSimpleName() : "");
            
            // Check if we should suppress repeated errors
            if (shouldSuppressError(errorKey)) {
                return;
            }
            
            // Log to Eclipse error log
            if (logToEclipse) {
                logToEclipse(message, throwable);
            }
            
            // Show to user if requested
            if (showToUser) {
                showErrorToUser(message, throwable);
            }
            
            // Update error tracking
            updateErrorTracking(errorKey);
            
        } catch (Exception e) {
            // Fallback error handling
            System.err.println("Error in error handler: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Handle AI-related errors
     */
    public void handleAIError(String operation, Throwable throwable) {
        String message = "AI operation failed: " + operation;
        
        // Determine if this is a recoverable error
        boolean isRecoverable = isRecoverableAIError(throwable);
        
        if (isRecoverable) {
            handleRecoverableError(message, throwable);
        } else {
            handleError(message, throwable);
        }
    }
    
    /**
     * Handle file operation errors
     */
    public void handleFileError(String filePath, String operation, Throwable throwable) {
        String message = String.format("File operation failed: %s on %s", operation, filePath);
        handleError(message, throwable);
    }
    
    /**
     * Handle command execution errors
     */
    public void handleCommandError(String command, Throwable throwable) {
        String message = "Command execution failed: " + command;
        handleError(message, throwable);
    }
    
    /**
     * Handle network/connection errors
     */
    public void handleNetworkError(String endpoint, Throwable throwable) {
        String message = "Network error connecting to: " + endpoint;
        
        // Network errors are often temporary, so handle them as recoverable
        handleRecoverableError(message, throwable);
    }
    
    /**
     * Handle recoverable errors (show less intrusive notifications)
     */
    public void handleRecoverableError(String message, Throwable throwable) {
        // Log the error
        logToEclipse(message, throwable);

        // Show a less intrusive warning (simplified)
        FarTechPlugin.logWarning("FarTech AI Warning: " + message + " - The operation will be retried automatically.");
    }
    
    /**
     * Handle validation errors
     */
    public void handleValidationError(String field, String value, String reason) {
        String message = String.format("Validation failed for %s='%s': %s", field, value, reason);
        
        // Validation errors don't need stack traces
        handleError(message, null, true, true);
    }
    
    /**
     * Log error to Eclipse error log (simplified)
     */
    private void logToEclipse(String message, Throwable throwable) {
        FarTechPlugin.logError(message, throwable);
    }
    
    /**
     * Show error to user (simplified)
     */
    private void showErrorToUser(String message, Throwable throwable) {
        // Simplified error display - just log to console
        FarTechPlugin.logError("FarTech AI Error: " + message, throwable);
    }
    
    /**
     * Check if we should suppress repeated errors
     */
    private boolean shouldSuppressError(String errorKey) {
        long currentTime = System.currentTimeMillis();
        
        // Reset error counts if enough time has passed
        Long lastTime = lastErrorTimes.get(errorKey);
        if (lastTime != null && (currentTime - lastTime) > ERROR_RESET_INTERVAL) {
            errorCounts.remove(errorKey);
            lastErrorTimes.remove(errorKey);
        }
        
        // Check error count
        AtomicInteger count = errorCounts.get(errorKey);
        if (count != null && count.get() >= MAX_ERROR_COUNT) {
            return true; // Suppress this error
        }
        
        return false;
    }
    
    /**
     * Update error tracking
     */
    private void updateErrorTracking(String errorKey) {
        long currentTime = System.currentTimeMillis();
        
        errorCounts.computeIfAbsent(errorKey, k -> new AtomicInteger(0)).incrementAndGet();
        lastErrorTimes.put(errorKey, currentTime);
    }
    
    /**
     * Check if an AI error is recoverable
     */
    private boolean isRecoverableAIError(Throwable throwable) {
        if (throwable == null) {
            return false;
        }
        
        String message = throwable.getMessage();
        if (message == null) {
            return false;
        }
        
        String lowerMessage = message.toLowerCase();
        
        // Network-related errors are often recoverable
        return lowerMessage.contains("timeout") ||
               lowerMessage.contains("connection") ||
               lowerMessage.contains("network") ||
               lowerMessage.contains("socket") ||
               lowerMessage.contains("503") ||
               lowerMessage.contains("502") ||
               lowerMessage.contains("rate limit");
    }
    
    /**
     * Get stack trace as string
     */
    public static String getStackTrace(Throwable throwable) {
        if (throwable == null) {
            return "";
        }
        
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        throwable.printStackTrace(pw);
        return sw.toString();
    }
    
    /**
     * Create a user-friendly error message
     */
    public static String createUserFriendlyMessage(String operation, Throwable throwable) {
        StringBuilder message = new StringBuilder();
        message.append("Failed to ").append(operation).append(".");
        
        if (throwable != null) {
            String cause = throwable.getMessage();
            if (cause != null && !cause.trim().isEmpty()) {
                message.append("\n\nReason: ").append(cause);
            }
            
            // Add suggestions based on error type
            String suggestions = getSuggestions(throwable);
            if (suggestions != null) {
                message.append("\n\nSuggestions:\n").append(suggestions);
            }
        }
        
        return message.toString();
    }
    
    /**
     * Get suggestions based on error type
     */
    private static String getSuggestions(Throwable throwable) {
        String message = throwable.getMessage();
        if (message == null) {
            return null;
        }
        
        String lowerMessage = message.toLowerCase();
        
        if (lowerMessage.contains("api key") || lowerMessage.contains("unauthorized")) {
            return "• Check your API key in Settings\n• Ensure the API key is valid and active";
        }
        
        if (lowerMessage.contains("network") || lowerMessage.contains("connection")) {
            return "• Check your internet connection\n• Verify the API endpoint URL\n• Try again in a few moments";
        }
        
        if (lowerMessage.contains("rate limit")) {
            return "• Wait a few minutes before trying again\n• Consider upgrading your API plan";
        }
        
        if (lowerMessage.contains("timeout")) {
            return "• Try a shorter request\n• Check your network connection\n• Increase timeout in settings";
        }
        
        return "• Try the operation again\n• Check the Eclipse Error Log for details\n• Contact support if the problem persists";
    }
    
    /**
     * Clear error tracking (useful for testing or manual reset)
     */
    public void clearErrorTracking() {
        errorCounts.clear();
        lastErrorTimes.clear();
    }
}
