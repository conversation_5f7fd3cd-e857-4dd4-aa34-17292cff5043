@echo off
echo 🔍 Testing FarTech AI API Models
echo ===============================================
echo.

echo 📡 Making API request to https://ai.ad-ins.com/api/models
echo.

curl -X GET "https://ai.ad-ins.com/api/models" ^
  -H "Authorization: Bearer sk-44d6db647bf44719beb0d032029948b7" ^
  -H "Content-Type: application/json" ^
  --connect-timeout 30 ^
  --max-time 60 ^
  -w "HTTP Status: %%{http_code}\nTotal time: %%{time_total}s\n" ^
  -s

echo.
echo.
echo 🎯 INSTRUCTIONS:
echo ================
echo 1. Look for "id" fields in the JSON response above
echo 2. Use the exact "id" value as the model name in Eclipse plugin
echo 3. Common format is usually lowercase with hyphens
echo 4. Example: "claude-3-5-haiku" or "deepseek-v3"
echo.

pause
