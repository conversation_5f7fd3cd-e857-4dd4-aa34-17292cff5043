# Test script to check FarTech AI API models
# This will show us the exact model names and format your provider uses

$apiUrl = "https://ai.ad-ins.com/api/models"
$apiKey = "sk-44d6db647bf44719beb0d032029948b7"

Write-Host "🔍 Testing FarTech AI API Models" -ForegroundColor Cyan
Write-Host "=" * 50
Write-Host "URL: $apiUrl"
Write-Host "API Key: $($apiKey.Substring(0, 20))..."
Write-Host ""

try {
    # Create headers
    $headers = @{
        "Authorization" = "Bearer $apiKey"
        "Content-Type" = "application/json"
    }
    
    Write-Host "📡 Making API request..." -ForegroundColor Yellow
    
    # Make the API request
    $response = Invoke-RestMethod -Uri $apiUrl -Headers $headers -Method Get -TimeoutSec 30
    
    Write-Host "✅ SUCCESS! API responded successfully" -ForegroundColor Green
    Write-Host ""
    
    # Display raw response
    Write-Host "📋 Raw JSON Response:" -ForegroundColor Cyan
    $jsonResponse = $response | ConvertTo-Json -Depth 10
    Write-Host $jsonResponse
    Write-Host ""
    
    # Extract model information
    Write-Host "🤖 AVAILABLE MODELS:" -ForegroundColor Cyan
    Write-Host "=" * 30
    
    if ($response.data) {
        $models = $response.data
        Write-Host "Found $($models.Count) models:" -ForegroundColor Green
        Write-Host ""
        
        for ($i = 0; $i -lt $models.Count; $i++) {
            $model = $models[$i]
            Write-Host "$($i + 1). Model Info:" -ForegroundColor Yellow
            
            if ($model.id) {
                Write-Host "    ID: $($model.id)" -ForegroundColor White
            }
            if ($model.name) {
                Write-Host "    Name: $($model.name)" -ForegroundColor White
            }
            if ($model.object) {
                Write-Host "    Object: $($model.object)" -ForegroundColor White
            }
            if ($model.created) {
                Write-Host "    Created: $($model.created)" -ForegroundColor White
            }
            if ($model.owned_by) {
                Write-Host "    Owned by: $($model.owned_by)" -ForegroundColor White
            }
            
            # Show all properties
            $model.PSObject.Properties | ForEach-Object {
                if ($_.Name -notin @('id', 'name', 'object', 'created', 'owned_by')) {
                    Write-Host "    $($_.Name): $($_.Value)" -ForegroundColor Gray
                }
            }
            Write-Host ""
        }
        
        # Extract just the model IDs for easy reference
        Write-Host "📝 MODEL IDs FOR PLUGIN CONFIGURATION:" -ForegroundColor Cyan
        Write-Host "=" * 40
        $models | ForEach-Object { 
            if ($_.id) {
                Write-Host "  '$($_.id)'" -ForegroundColor Green
            }
        }
        
    }
    elseif ($response -is [array]) {
        Write-Host "Found $($response.Count) models:" -ForegroundColor Green
        Write-Host ""

        for ($i = 0; $i -lt $response.Count; $i++) {
            Write-Host "$($i + 1). $($response[$i])" -ForegroundColor White
        }

    }
    else {
        Write-Host "⚠️  Unexpected response format" -ForegroundColor Yellow
        Write-Host "Response type: $($response.GetType().Name)"
        Write-Host "Response: $response"
    }
    
} catch {
    $statusCode = $_.Exception.Response.StatusCode.value__
    $statusDescription = $_.Exception.Response.StatusDescription
    
    if ($statusCode) {
        Write-Host "❌ ERROR: HTTP $statusCode - $statusDescription" -ForegroundColor Red
    } else {
        Write-Host "❌ ERROR: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    if ($_.Exception.Response) {
        try {
            $errorStream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorStream)
            $errorBody = $reader.ReadToEnd()
            Write-Host "Error response: $errorBody" -ForegroundColor Red
        } catch {
            Write-Host "Could not read error response" -ForegroundColor Red
        }
    }
}

Write-Host ""
Write-Host "🎯 NEXT STEPS:" -ForegroundColor Cyan
Write-Host "1. Use the exact model IDs shown above in your Eclipse plugin"
Write-Host "2. Copy the model ID exactly as shown (case-sensitive)"
Write-Host "3. Test connection with the correct model ID"
