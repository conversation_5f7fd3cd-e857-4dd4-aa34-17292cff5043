#!/usr/bin/env python3
"""
Test script to check FarTech AI API models
This will show us the exact model names and format your provider uses
"""

import requests
import json

# Your API configuration
API_URL = "https://ai.ad-ins.com/api/models"
API_KEY = "sk-44d6db647bf44719beb0d032029948b7"

def test_api_models():
    """Test the API models endpoint"""
    print("🔍 Testing FarTech AI API Models")
    print("=" * 50)
    print(f"URL: {API_URL}")
    print(f"API Key: {API_KEY[:20]}...")
    print()
    
    try:
        # Make the API request
        headers = {
            "Authorization": f"Bearer {API_KEY}",
            "Content-Type": "application/json"
        }
        
        print("📡 Making API request...")
        response = requests.get(API_URL, headers=headers, timeout=30)
        
        print(f"📊 Response Status: {response.status_code}")
        print(f"📊 Response Headers: {dict(response.headers)}")
        print()
        
        if response.status_code == 200:
            print("✅ SUCCESS! API responded successfully")
            print()
            
            # Parse the JSON response
            data = response.json()
            print("📋 Raw JSON Response:")
            print(json.dumps(data, indent=2))
            print()
            
            # Extract model information
            print("🤖 AVAILABLE MODELS:")
            print("=" * 30)
            
            if isinstance(data, dict) and "data" in data:
                models = data["data"]
                print(f"Found {len(models)} models:")
                print()
                
                for i, model in enumerate(models, 1):
                    print(f"{i:2d}. Model Info:")
                    if isinstance(model, dict):
                        for key, value in model.items():
                            print(f"    {key}: {value}")
                    else:
                        print(f"    {model}")
                    print()
                    
            elif isinstance(data, list):
                print(f"Found {len(data)} models:")
                print()
                
                for i, model in enumerate(data, 1):
                    print(f"{i:2d}. {model}")
                    
            else:
                print("⚠️  Unexpected response format")
                print(f"Response type: {type(data)}")
                print(f"Response: {data}")
                
        else:
            print(f"❌ ERROR: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.Timeout:
        print("❌ ERROR: Request timed out (>30 seconds)")
        
    except requests.exceptions.ConnectionError:
        print("❌ ERROR: Connection failed")
        
    except requests.exceptions.RequestException as e:
        print(f"❌ ERROR: Request failed - {e}")
        
    except json.JSONDecodeError as e:
        print(f"❌ ERROR: Invalid JSON response - {e}")
        print(f"Raw response: {response.text}")
        
    except Exception as e:
        print(f"❌ ERROR: Unexpected error - {e}")

if __name__ == "__main__":
    test_api_models()
